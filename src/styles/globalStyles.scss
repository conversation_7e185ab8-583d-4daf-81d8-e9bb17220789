@import "colors";

@font-face {
    font-family: 'Plus Jakarta Sans';
    src: url('/fonts/PlusJakartaSans-Bold.ttf') format('ttf');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Plus Jakarta Sans';
    src: url('/fonts/PlusJakartaSans-ExtraBold.ttf') format('ttf');
    font-weight: bolder;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Plus Jakarta Sans';
    src: url('/fonts/PlusJakartaSans-Italic.ttf') format('ttf');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Plus Jakarta Sans';
    src: url('/fonts/PlusJakartaSans-Medium.ttf') format('ttf');
    font-weight: normal;
    font-style: normal;
    font-display: swap;

}

html {
    scroll-behavior: smooth;
}

.bg-yellow-gradient {
    background: $yellow-gradient;
}

.bg-blue-gradient {
    background: $blue-gradient;
}

.bg-grey-gradient {
    background: $grey-gradient;
}

.bg-mint-gradient {
    background: $mint-gradient;
}

.bg-salmon-gradient {
    background: $salmon-gradient;
}

.bg-mint-dark {
    background: $mint-gradient-dark;
}

.text-hover-mint:hover,
.text-hover-mint:active {
    color: $UO-mint;
    fill: $UO-mint;
}

.nav-opt-border {
    border-bottom: 1px solid $color-grey-s2;
}

.message-contact-form {
    grid-column: 1/-1;
}

.submit-contact-form {
    grid-column: 1/-1;
}

.border-blk {
    border: 1px solid #101010;
}

.bg-framer {
    background: rgb(237, 237, 237);
}

.anchor-text {
    width: 100%;
    background-image: url("../assets/images/slice/anchor-text.svg");
    background-repeat: repeat-x;
    background-size: contain;
  }

  #balance::-webkit-slider-runnable-track {
    height: 24px;
    background: rgb(235, 235, 235);
    border-radius: 16px;
  }

  #balance::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 48px;
    height: 48px;
    background: rgb(0, 0, 0);
    cursor: pointer;
    border-radius: 50%;
    transform: translateY(-12px);
    box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.2);
    border-width: 24px;
    border-style: solid;
    border-color: rgb(0, 0, 0);
  }
.card  .testimonial-left-button {
    position: absolute;
    /* margin-left: 435px; */
    top: 50%;
    left:32%;
    /* width: 24px;
    height: 8px; */
    background-color: #3CBEEC;
    z-index: 4;
  
  }
  .card  .testimonial-right-button {
    position: absolute;
    /* margin-left: 838px; */
    top: 50%;
    right: 32%;
    box-shadow: none;
    /* width: 24px;
    height: 8px; */
    background-color: #3CBEEC;
    z-index: 4;
  
  }
  .card .card-button.right {
    right: 0%;
  }
  .react-stacked-center-carousel {
    padding: 20px 0;
    overflow-y:visible !important;
    overflow-x:visible ;
  }
  
  .card-card {
    transition: all 300ms ease;
    cursor: pointer;
    width: 100%;
    border-radius: 20px;
    height: 484px;
    position: relative;
    background-color: #ffffff;
  }
  
  .card-card:hover {
    transform: scale(1.05);
  }
  
  .react-stacked-center-carousel-slide-0 .card-card {
    cursor: default;
    
  
  }
  
  .react-stacked-center-carousel-slide-0 .card-card:hover {
    transform: none;
  }
  
  .fill {
    width: 100%;
    height: 100%;
  }
  .card-carrier{
    background: transparent !important;
   padding-top: 3%;
   border: none;
   
  
   
  }
  
  .card-card .cover {
    position: absolute;
    transition: opacity 300ms ease;
  }
  .react-stacked-center-carousel-slide-0 .card-card .cover {
    transition: opacity 300ms ease, z-index 0ms 300ms;
  }
  
  .card-card .cover.on {
    opacity: 1;
    z-index: 1;
  }
  
  .card-card .cover.off {
    opacity: 0;
    z-index: -1;
  }
  
  .card-card .detail {
    display: flex;
  }
  .card-card .video {
    width: 40%;
  }
  
  .card-card >p{
    text-align: center;
    padding-top: 20px;
    font-family: Lato;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 21px;
  letter-spacing: 0.02em;
  text-align: justified;
  
  }
  
  .card-height {
    height: 484px;
  }
  
  @media screen and (max-width:540px){
  body{
    overflow-x:hidden
  }
  
    .card-card {
      transition: all 300ms ease;
      cursor: pointer;
      position: relative;
    }
  
    .card  .left {
      display: none;
    
    }
    .card  .right {
      display: none;
    
    }

    .fill {
      display: none;
    }

    .card-height {
      height: 408px;
    }
  }
  
  @media screen and (min-width:765px) and (max-width:992px){
   
    }
---
import { Image } from 'astro:assets';
import Button from "../components/Button.astro";

interface Props {
	className: string;
    text1: string;
    text2: string;
    imgsrc : any;
}

const { className, text1, text2, imgsrc } = Astro.props;

---
<section class={className}>
    <div class="heading mx-auto text-center max-w-2xl  lg:px-0 px-11">
        <p class="text-3xl md:text-4xl lg:text-5xl tracking-normal font-extrabold">
            {text1}
        </p>
        <p class="text-3xl md:text-4xl lg:text-5xl tracking-normal font-extrabold pb-12">
            {text2}
        </p>

        <a href="https://app.uown.co/register"
            ><Button
                type="button"
                color="btn-black btn-get-started"
                text="Get Started"
            /></a
        >
    </div>
    <Image src={imgsrc} class="mx-auto pt-24 md:max-w-xl" alt="" />
</section>
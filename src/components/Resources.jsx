import { motion } from 'motion/react'
import { Container } from '../components/ContainerA.jsx'
import { SectionHeading } from '../components/SectionHeading.jsx'
import abstractBackgroundImage from '../assets/images/resources/abstract-background.png'
import discordImage from '../assets/images/resources/discord.svg'
import figmaImage from '../assets/images/resources/figma.svg'
import videoPlayerImage from '../assets/images/resources/video-player.svg'


const resources = [
  {
    title: 'City League Table',
    description:
      'See which cities are getting the best yields and where rents are weaker.',
    image: function FigmaImage() {
      return (
        <div className="absolute inset-0 flex items-center justify-center bg-[radial-gradient(#2C313D_35%,#000)]">
          <img src={figmaImage.src} alt="" />
        </div>
      )
    },
  },
  {
    title: 'Buy-to-let Challenges',
    description:
      'There are strong headwinds for buy-to-let, find out about the alternatives.',
    image: function VideoPlayerImage() {
      return (
        <div className="absolute inset-0 flex items-center justify-center">
          <img
            className="absolute inset-0 h-full w-full object-cover"
            src={abstractBackgroundImage.src}
            alt=""
            sizes="(min-width: 1280px) 21rem, (min-width: 1024px) 33vw, (min-width: 768px) 19rem, (min-width: 640px) 50vw, 100vw"
          />
          <img
            className="relative"
            src={videoPlayerImage.src}
            alt=""
          />
        </div>
      )
    },
  },
  {
    title: 'Strategies of the future',
    description:
      "There has been a shift from institutional investors, see how you can benefit from the same opportunities.",
    image: function DiscordImage() {
      return (
        <div className="absolute inset-0 flex items-center justify-center bg-[#6366F1]">
          <img src={discordImage.src} alt="" />
        </div>
      )
    },
  },
]

export function Resources() {
  return (
    <section
      id="resources"
      aria-labelledby="resources-title"
      className="scroll-mt-14 py-16 sm:scroll-mt-32 sm:py-20 lg:py-32"
    >
      <Container>
        <SectionHeading number="3" id="resources-title">
          What's in the guide
        </SectionHeading>
        <motion.p initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
          transition={{ duration: 0.5, ease: "easeIn" }} className="mt-8 font-display text-4xl font-bold tracking-tight text-slate-900">
          Get a sneak peek at the wealth of information you can expect to find in the guide.
        </motion.p>
        <motion.p initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
          transition={{ duration: 0.5, ease: "easeIn" }} className="mt-4 text-lg tracking-tight text-slate-700">
          Easy to digest, actionable information. All backed by the latest research and data, you will come away
          with a real understanding of whats driving the property trends in the UK.
        </motion.p>
      </Container>
      <Container size="lg" className="mt-16">
        <ol
          role="list"
          className="-mx-3 grid grid-cols-1 gap-y-10 lg:grid-cols-3 lg:text-center xl:-mx-12 xl:divide-x xl:divide-slate-400/20"
        >
          {resources.map((resource) => (
            <motion.li initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }}
              key={resource.title}
              className="grid auto-rows-min grid-cols-1 items-center gap-8 px-3 sm:grid-cols-2 sm:gap-y-10 lg:grid-cols-1 xl:px-12"
            >
              <div className="relative h-48 overflow-hidden rounded-clg shadow-lg sm:h-60 lg:h-40">
                <resource.image />
              </div>
              <div>
                <h3 className="text-base font-medium tracking-tight text-slate-900">
                  {resource.title}
                </h3>
                <p className="mt-2 text-sm text-slate-600">
                  {resource.description}
                </p>
              </div>
            </motion.li>
          ))}
        </ol>
      </Container>
    </section>
  )
}
'use client'
import { motion } from 'motion/react';
import { CheckIcon } from '../components/CheckIcon.jsx'
import { Container } from '../components/ContainerA.jsx'

export function Introduction() {
  return (
    <section
      id="introduction"
      aria-label="Introduction"
      className="pb-16 pt-20 sm:pb-20 md:pt-36 lg:py-32"
    >
      <Container className="text-lg tracking-tight text-slate-700">
        <motion.p initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
          transition={{ duration: 0.5, ease: "easeIn" }} className="font-display text-4xl font-bold tracking-tight text-slate-900">
          This investment guide looks at the trends, projections and strategies you can use to make informed investment decisions.
        </motion.p>
        <motion.p initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
          transition={{ duration: 0.5, ease: "easeIn" }} className="mt-4">
          The UK property market has a strong reputation as a stable and well regulated
          place to buy property.
        </motion.p>

        <motion.p initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
          transition={{ duration: 0.5, ease: "easeIn" }} className="mt-4">
          Understanding the data driving the trends and shifts
          is important to making wise investment decisions, this guide gives you that data
          alongside expert analysis in an easy to digest format.
        </motion.p>
        <ul role="list" className="mt-8 space-y-3">
          {[
            'What will happen to house prices in 2025?',
            'Which region will see the biggest growth over the next five years?',
            'How will stamp duty affect the market this year?',
            'Which UK city has the highest rental yield?',
            'Will the government successfully deliver on their house building targets?',
          ].map((feature, index) => (
            <motion.li initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }}
              key={feature} className="flex">
              <CheckIcon className="h-8 w-8 flex-none fill-mint-500" />
              <span className="ml-4">{feature}</span>
            </motion.li>
          ))}
        </ul>
        <motion.p initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.6, ease: "easeIn" }} className="mt-8">
          By the end of the guide, you’ll have the answers to all these questions and more. This guide will give you the confidence in knowing where to invest and you will have the data to back up your decision.
        </motion.p>
        <p className="mt-10">
          <motion.a initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.7, ease: "easeIn" }}
            href="#free-chapters"
            className="text-base font-medium text-mint-500 hover:text-mint-300"
          >
            Get the guide straight to your inbox{' '}
            <span aria-hidden="true">&rarr;</span>
          </motion.a>
        </p>
      </Container>
    </section>
  )
}

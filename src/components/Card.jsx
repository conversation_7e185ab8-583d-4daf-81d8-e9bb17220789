import { AnimatePresence, motion } from "motion/react";
import React, { useState } from "react";

// import Image from "next/image";

const Card = ({ image }) => {
  const [showOverlay, setShowOverlay] = useState(false);

  return (
    <motion.div
      className="relative overflow-hidden h-[100px] w-[100px] bg-white rounded-clg flex justify-center items-center"
      key={image}
      onHoverStart={() => setShowOverlay(true)}
      onHoverEnd={() => setShowOverlay(false)}
    >
      {/* Hover overlay */}
      <AnimatePresence>
        {showOverlay && (
          <motion.div
            className="absolute left-0 top-0 bottom-0 right-0 z-10 flex justify-center items-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <div className="absolute bg-black-100 pointer-events-none opacity-30 h-full w-full" />
            <motion.h1
              className="font-semibold text-[10px] z-10 px-3 py-2 rounded-full flex items-center gap-[0.5ch] hover:opacity-95"
              initial={{ y: 10 }}
              animate={{ y: 0 }}
              exit={{ y: 10 }}
            >
            </motion.h1>
          </motion.div>
        )}
      </AnimatePresence>
      <img loading="lazy" src={image} alt={image} className="max-h-[86px] max-w-[86px]" />
    </motion.div>
  );
};

export default Card;
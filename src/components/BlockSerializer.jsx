import React from "react";
import {PortableText} from '@portabletext/react'
import ReactPlayer from 'react-player'

import {
  urlForImage
} from "../lib/sanity-client.ts";

let count=0;

const SampleImageComponent = (children) => {
  const value = children.value;
  const url = urlForImage(value).url();
  return (
    <img className="py-6 w-full" src={url} loading="lazy" />
  )
}

const components = {
  types: {
    image: SampleImageComponent,
    youtube: (node) => {
      const url = node.value.url;
      return (<iframe allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" title="UOWN Testimonial | Tracey - &quot;The Buy-To-Let Investor&quot;" width="100%" height="432px" src={url}></iframe>)
    }
  },
  marks: {
    // Ex. 1: custom renderer for the em / italics decorator
    em: ({children}) => <em className="text-gray-600 font-semibold">{children}</em>,

    internalLink: ({value, children}) => {
      const target = (value?.href || '').startsWith('http') ? '_blank' : undefined
      return (
        <a href={value?.href} target={target} rel={target === '_blank' && 'noindex nofollow'}>
          {children}
        </a>
      )},

    // Ex. 2: rendering a custom `link` annotation
    link: ({value, children}) => {
      const target = (value?.href || '').startsWith('http') ? '_blank' : undefined
      return (
        <a href={value?.href} target={target} rel={target === '_blank' && 'noindex nofollow'}>
          {children}
        </a>
      )
    },
  },
  block: {
    // Ex. 1: customizing common block types
    h1: ({children}) => <h1 className="text-2xl lg:text-3xl tracking-normal font-bold text-left">{children}</h1>,
    h2: ({children}) => <div id={"h" + count++}><h2 className="text-2xl lg:text-3xl tracking-normal font-bold pt-12 pb-3 text-left">{children}</h2></div>,
    h3: ({children}) => <h3 className="text-xl tracking-normal font-regular pb-6 text-left">{children}</h3>,
    h4: ({children}) => <h4 className="text-xl tracking-normal font-regular pb-6 text-left">{children}</h4>,
    h5: ({children}) => <h5 className="text-xl tracking-normal font-regular pb-6 text-left">{children}</h5>,
    h6: ({children}) => <h6 className="text-xl tracking-normal font-regular pb-6 text-left">{children}</h6>,
    normal: ({children}) => <p className="text-base lg:text-lg tracking-normal font-regular pb-6 text-left">{children}</p>,
    blockquote: ({children}) => <blockquote className="border-l-purple-500">{children}</blockquote>,

    // Ex. 2: rendering custom styles
    customHeading: ({children}) => (
      <h2 className="text-lg text-primary text-purple-700">{children}</h2>
    ),
  },
  list: {
    // Ex. 1: customizing common list types
    bullet: ({children}) => <ul className="text-left pl-4 md:pl-14 lg:pl-24 text-base lg:text-lg tracking-normal font-regular py-6">{children}</ul>,
    number: ({children}) => <ol className="text-left text-xl tracking-normal font-regular pt-6">{children}</ol>,

    // Ex. 2: rendering custom lists
    checkmarks: ({children}) => <ol className="m-auto text-lg">{children}</ol>,
  },
  listItem: {
    // Ex. 1: customizing common list types
    bullet: ({children}) => <li style={{listStyleType: 'disc'}}>{children}</li>,
    number: ({children}) => <ol className="text-xl tracking-normal font-regular pt-6">{children}</ol>,

    // Ex. 2: rendering custom list items
    checkmarks: ({children}) => <li>✅ {children}</li>,
  },
}

const content = (post) => {
  let value, postContent = post.post;
  
  if (postContent.bodyText) {
    value = postContent.bodyText;
  } else {
    value = postContent.contentHtml
  }
  count=0;
  return (
    <div>
      {/* <h1 className="text-3xl md:text-2xl lg:text-3xl tracking-normal font-extrabold md:font-bold text-left pb-12">{postContent.title}</h1> */}
      <PortableText value={value} components={components} />
    </div>
  );
}

export default content;

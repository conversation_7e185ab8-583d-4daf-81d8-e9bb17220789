'use client'
import { motion } from 'motion/react';
import { FireIcon, BanknotesIcon, UserGroupIcon, SparklesIcon } from '@heroicons/react/24/outline'

export default function HeroSection() {
    return (
        <section className="bg-white py-12 md:py-24 px-6 md:px-12">
            <div className='bg-gray-20 rounded-clg py-12 md:py-24 px-6 md:px-12'>
                <motion.h1 
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                transition={{ duration: 0.3, ease: "easeIn" }}
                className='text-3xl md:text-5xl lg:text-6xl max-w-3xl text-center mx-auto mb-24'>Real Estate Management Services</motion.h1>

                <div className='grid grid-cols-1 mlg:grid-cols-2 lg:grid-cols-3 gap-6'>
                    <motion.div 
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.5, ease: "linear" }}
                    className='bg-white rounded-clg col-span-1 mlg:col-span-2 p-6 text-left flex flex-col justify-between h-[280px]'>
                        <div className='text-3xl flex justify-between'>Property Listings<FireIcon  className="h-16 w-16 text-black-200 bg-mint-50 rounded-full p-4" aria-hidden="true" /></div>
                        <div className='text-lg  max-w-lg'>Easily browse and manage all your property listings with our user-friendly platform.</div>
                    </motion.div>
                    <motion.div 
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.6, ease: "linear" }}
                    className='bg-white rounded-clg col-span-1 p-6 text-left flex flex-col justify-between h-[280px]'>
                        <div className='text-3xl flex justify-between'><span className='max-w-28'>Financial Reporting</span><BanknotesIcon  className="h-16 w-16 text-black-200 bg-mint-50 rounded-full p-4" aria-hidden="true" /></div>
                        <div className='text-lg  max-w-lg'>Generate detailed financial statements and performance reports to stay on top of your property's financial health.</div>
                    </motion.div>
                    <motion.div 
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.7, ease: "linear" }}
                    className='bg-white rounded-clg col-span-1 p-6 text-left flex flex-col justify-between h-[280px]'>
                        <div className='text-3xl flex justify-between'><span className='max-w-28'>Service Requests</span><SparklesIcon  className="h-16 w-16 text-black-200 bg-mint-50 rounded-full p-4" aria-hidden="true" /></div>
                        <div className='text-lg max-w-lg'>Efficiently handle maintenance requests and work orders through our app.</div>
                    </motion.div>
                    <motion.div 
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.8, ease: "linear" }}
                    className='bg-white rounded-clg col-span-1 mlg:col-span-2 p-6 text-left flex flex-col justify-between h-[280px]'>
                        <div className='text-3xl flex justify-between'>Tenant Management<UserGroupIcon  className="h-16 w-16 text-black-200 bg-mint-50 rounded-full p-4" aria-hidden="true" /></div>
                        <div className='text-lg max-w-lg'>Keep track of tenant information and lease agreements without hassle. Our app allows you to store and manage all necessary details.</div>
                    </motion.div>
                </div>
            </div>
        </section>
    )
}

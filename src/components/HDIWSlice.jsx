import { motion } from 'motion/react'
import { Accordion, AccordionItem as Item } from "@szhsin/react-accordion";
import { ChevronDownIcon } from '@heroicons/react/24/outline'

import Hdiw from '../assets/images/slice/hdiw.png';

export default function HDIWSlice() {

    const AccordionItem = ({ id, header, ...rest }) => (
        <Item
            {...rest}
            header={({ state: { isEnter } }) => (
                <>
                    <p className="flex items-center justify-center"><span className="flex items-center justify-center bg-yellow-200 w-8 h-8 text-center rounded-full mr-3">{id}</span>{header}</p>
                    <ChevronDownIcon className={`ml-auto transition-transform duration-200 ease-out ${isEnter && "rotate-180"}`} />
                </>
            )}
            className="border-b font-bold"
            buttonProps={{
                className: ({ isEnter }) =>
                    `flex w-full p-4 text-left hover:bg-mint-50 ${isEnter
                    }`
            }}
            contentProps={{
                className: "text-base font-light transition-height duration-200 ease-out"
            }}
            panelProps={{ className: "p-4" }}
        />
    );

    return (
        <section className='p-6 md:p-10 max-w-7xl mx-auto'>
            <div className='flex flex-col justify-center items-center text-center max-w-2xl mx-auto gap-y-4 pb-20'>
                <motion.div initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.3, ease: "easeIn" }} className='text-sm font-bold tracking-widest uppercase px-2.5 py-2 bg-mint-100 text-black-50 rounded-full'>How does it work?</motion.div>
                <motion.h2 initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.4, ease: "easeIn" }} className="text-4xl md:text-5xl lxl:text-6xl text-black">
                    Invest in income-generating real estate with ease.
                </motion.h2>
            </div>
            <div className='flex flex-col mlg:flex-row justify-center items-center gap-x-20'>
                <img loading="lazy" src={Hdiw.src} className='flex mlg:basis-1/2 rounded-clg object-cover mlg:max-w-[500px]' alt="" />
                <div className='flex flex-col mlg:basis-1/2 pt-10 gap-y-10'>
                    <Accordion transition transitionTimeout={200}>
                        <AccordionItem id="1" header="Create your profile in less than 1 minute." initialEntered>
                            It's super quick and easy to get setup. Whats more it's free and allows you to see all the details for each project.
                        </AccordionItem>

                        <AccordionItem id="2" header="Invest in one of our real estate projects.">
                            Once you have an account find the project you like and invest. You can pay directly from your bank account in seconds.
                        </AccordionItem>

                        <AccordionItem id="3" header="View your portfolio and track your returns in the dashboard.">
                            Once you have made your investment you can view your portfolio and track your returns in our dashboard.
                        </AccordionItem>
                    </Accordion>
                    <button type="button" className="mt-10 btn-black max-w-[200px] lxl:text-xl rounded-full px-4 py-2.5 text-lg font-bold text-white shadow-sm transition duration-150 ease-in hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-mint-300">Get Started</button>
                </div>
            </div>
        </section>
    )
}

import { motion } from 'motion/react'
import { Container } from '../components/ContainerA.jsx'
import { SectionHeading } from '../components/SectionHeading.jsx'
import signImage from '../assets/images/screencasts/SIGNUP.jpg'
import projectsImage from '../assets/images/screencasts/PICK_PROJECT.jpg'
import setupImage from '../assets/images/screencasts/SELECT.jpg'
import strokesImage from '../assets/images/screencasts/SIT_BACK.jpg'

const videos = [
  {
    title: 'Setup your free account',
    description:
      "It's quick and easy to register. Once you're done you can begin to dip your toe in and make your first investment.",
    image: signImage,
    runtime: { minutes: 16, seconds: 54 },
  },
  {
    title: 'Pick your project',
    description:
      'Our in-house team is always on the hunt for the best deals out there! They sift through hundreds of deals each month and bring you the best ones.',
    image: projectsImage,
    runtime: { minutes: 9, seconds: 12 },
  },
  {
    title: 'Choose how much to invest',
    description:
      "You decide how much you start investing with. With your support, we can bring the project vision to life!",
    image: setupImage,
    runtime: { minutes: 23, seconds: 25 },
  },
  {
    title: 'Sit back and let us do the work',
    description:
      "Our experienced team will oversee the project. We'll keep you informed every step of the way, so you can enjoy the show!",
    image: strokesImage,
    runtime: { minutes: 28, seconds: 44 },
  },
]



export function Screencasts() {
  return (
    <section
      id="screencasts"
      aria-labelledby="screencasts-title"
      className="scroll-mt-14 py-16 sm:scroll-mt-32 sm:py-20 lg:py-32"
    >
      <Container>
        <SectionHeading number="2" id="screencasts-title">
          Property Crowdfunding
        </SectionHeading>
        <motion.p initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
          transition={{ duration: 0.5, ease: "easeIn" }} className="mt-8 font-display text-4xl font-bold tracking-tight text-slate-900">
          Learn how you can begin investing today in minutes with UOWN.
        </motion.p>
        <motion.p initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
          transition={{ duration: 0.5, ease: "easeIn" }} className="mt-4 text-lg tracking-tight text-slate-700">
          Learn how simple and straightforward property investing can be. You can register and invest in minutes and access projects otherwise unavailable to individuals.
        </motion.p>
      </Container>
      <Container size="lg" className="mt-16">
        <ol
          role="list"
          className="grid grid-cols-1 gap-x-8 gap-y-10 [counter-reset:video] sm:grid-cols-2 lg:grid-cols-4"
        >
          {videos.map((video) => (
            <motion.li initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }} key={video.title} className="[counter-increment:video]">
              <div
                className="relative flex h-44 items-center justify-center rounded-clg px-6 shadow-lg"
                style={{
                  backgroundImage:
                    'conic-gradient(from -49.8deg at 50% 50%, #7331FF 0deg, #00A3FF 59.07deg, #4E51FF 185.61deg, #39DBFF 284.23deg, #B84FF1 329.41deg, #7331FF 360deg)',
                }}
              >
                <div className="flex overflow-hidden rounded shadow-sm">
                  <img src={video.image.src} alt="" />
                </div>
                <div className="absolute bottom-2 left-2 flex items-center rounded-lg bg-black/30 px-1.5 py-0.5 text-sm text-white [..supports(backdrop-filter:blur(0))]:bg-white/10 [..supports(backdrop-filter:blur(0))]:backdrop-blur">


                </div>
              </div>
              <h3 className="mt-8 text-base font-medium tracking-tight text-slate-900 before:mb-2 before:block before:font-mono before:text-sm before:text-slate-500 before:content-[counter(video,decimal-leading-zero)]">
                {video.title}
              </h3>
              <p className="mt-2 text-sm text-slate-600">{video.description}</p>
            </motion.li>
          ))}
        </ol>
      </Container>
    </section>
  )
}

---
import { Image } from 'astro:assets';
import ArticleTile from "../components/ArticleTile.astro";
import {
    getPopularHelpArticles,
    getFullUrl,
    getReadingTime,
} from "../lib/sanity-client.ts";

import CircleBlack from '../assets/images/help/img-quarter-circle-black.png';
import SphereGreen from '../assets/images/help/img-sphere-green.png';
import SphereGold from '../assets/images/help/img-sphere-half-gold.png';

const popularHelpArticles = await getPopularHelpArticles();

interface Props {
	isHelpCentreMainPage: boolean;
}

const { isHelpCentreMainPage } = Astro.props;
const sectionClass = "lg:py-64 md:py-24 py-12 pb-24 relative md:px-10 lg:px-20 lxl:px-36 xl:px-60 overflow-hidden" + ((isHelpCentreMainPage == false ) ? ' hidden lg:block' : '');
---
<section class={sectionClass}>
    <Image class='absolute z-0 top-[5%] right-[92%] lg:w-56 lg:h-44 md:w-24 md:h-20' width="74" height="59" src={CircleBlack} alt="" />
    <Image class='absolute z-0 top-[20%] left-[80%] lg:w-40 lg:h-32 md:w-24 md:h-20' width="75" height="63" src={SphereGreen} alt="" />
    <Image class='absolute z-0 top-[85%] right-[20%] lg:w-40 lg:h-32 md:w-24 md:h-20' width="74" height="61" src={SphereGold} alt="" />
    <p
        class="max-w-4xl lg:text-5xl md:text-4xl text-3xl letterspacing-none font-extrabold md:pb-24 pb-12 text-center mx-auto"
    >
        Most Popular Articles
    </p>

    <div class="flex flex-wrap justify-center p-7">
        {
            popularHelpArticles.map((helpArticle: any) => (
            <ArticleTile readingTime={getReadingTime("article", helpArticle.slug.current, )} postHref={getFullUrl(Astro.url.origin, helpArticle.fullSlug)} title={helpArticle.title} sectionType="" />
            ))
        }
    </div>
</section>
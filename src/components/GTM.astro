<script is:inline>
    const searchParams = new URL(window.location.href);
    const isDebug = searchParams.searchParams.get("gtm_debug");
    const scripts = [
      {
        src: null,
        text: `
          (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','GTM-WXD5GDR');
        `,
      },
    ];
    function injectScripts() {
      scripts.forEach((s) => {
        const script = document.createElement("script");
        script.type = isDebug ? "text/javascript" : "text/partytown";
        if (s.src) script.src = s.src;
        if (s.text) script.text = s.text;
        document.head.appendChild(script);
      });
    }
    injectScripts();
</script>
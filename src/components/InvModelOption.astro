---

interface Props {
	textH5: string;
    textp: string;
    imgUrl: string;
    videoUrl: string
}

const { textH5, imgUrl, textp, videoUrl } = Astro.props;
let mediaTag;

if (imgUrl == '') {
    mediaTag = '<video playsinline="" autoplay loop muted transition:persist="media-player"  class="mx-auto py-12 lg:h-[400px] md:h-[337px] h-[230px]" src="' +  videoUrl + '"></video>'
} else {
    mediaTag = '<img src="' + imgUrl + '" alt="" class="mx-auto py-12 lg:h-[400px] md:h-[337px] h-[230px]" />';
}
---

<div class="bg-mint-50 max-w-xs md:max-w-xl p-6 text-center rounded-cxl mx-auto py-12 mb-6">
    <h5 class="text-3xl md:text-4xl lg:text-5xl tracking-normal font-extrabold">
       {textH5}
    </h5>
    <Fragment set:html={mediaTag} />
    
    <p class="text-base md:text-lg lg:text-xl max-w-md mx-auto tracking-normal font-regular pb-6">
        {textp} 
    </p>
    
</div>

<style scoped>

    .shadow-light {
        box-shadow: 0px 4px 4px 0px #00000040;
    }
   
</style>

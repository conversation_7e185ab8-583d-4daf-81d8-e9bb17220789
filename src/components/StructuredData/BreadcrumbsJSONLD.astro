---
const { items } = Astro.props;

const schema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
        items.map((item: any) => ({
            "@type": "ListItem",
            position: item.id,
            item: {
                "@id": item.url,
                name: item.name,
            },
        })),
    ],
};
---

<script type="application/ld+json" set:html={JSON.stringify(schema)} />

---
const { title, summary, publishedAt, author, category } = Astro.props;


const schema = {
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": title,
  "description": summary,
  "image": [(category && category.mainImage ? category.mainImage : '')],
  "author": {
    "@type": "Person",
    "name": author,
  },
  /* the schema expects Date or DateTime using ISO 8601 format. For Date that is yyyy-MM-dd */
  "datePublished": publishedAt,
};
---

<script type="application/ld+json" set:html={JSON.stringify(schema)} />
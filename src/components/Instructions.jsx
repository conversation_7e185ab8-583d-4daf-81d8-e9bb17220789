'use client'
import { motion } from 'motion/react';
import { ChevronRightIcon } from '@heroicons/react/20/solid'

import Projects from "../assets/images/app/pyp.webp";
import Investment from "../assets/images/app/inl.webp";
import Dashboard from "../assets/images/app/db.webp";
import Invest from '../pages/invest.astro';

const Instructions = () => {

    return (
        <div className="bg-white">
            <div className="relative isolate overflow-hidden bg-gradient-to-b from-indigo-100/20">
                <div className="mx-auto max-w-7xl pb-24 pt-10 sm:pb-32 flex lg:gap-x-8 lg:px-8 lg:py-40">
                    <div className="block px-6 lg:px-0 lg:pt-4 w-full max-w-2xl">
                        <div className="mx-auto max-w-2xl">
                            <img
                                className="h-11"
                                src="https://tailwindui.com/plus/img/logos/mark.svg?color=indigo&shade=600"
                                alt="Your Company"
                            />
                            <div className="max-w-lg w-full flex flex-col justify-between">
                                <h1 className="mt-10 text-pretty text-3xl tracking-tight text-gray-900 sm:text-5xl">
                                    Pick your Projects
                                </h1>
                                <p className="mt-6 text-lg/8 text-black-50">
                                    Portfolio Management
                                </p>
                                <p className="mt-6 text-lg/8 text-gray-600">
                                    Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo.
                                </p>
                            </div>
                        </div>
                    </div>
                    <img
                        src={Projects.src}
                        alt="image"
                        className='object-cover rounded-clg flex'
                    />
                </div>
                <div className="mx-auto max-w-7xl pb-24 pt-10 sm:pb-32 flex lg:gap-x-8 lg:px-8 lg:py-40">
                    <div className="px-6 lg:px-0 lg:pt-4">
                        <div className="mx-auto max-w-2xl">
                            <img
                                className="h-11"
                                src="https://tailwindui.com/plus/img/logos/mark.svg?color=indigo&shade=600"
                                alt="Your Company"
                            />
                            <div className="max-w-lg flex flex-col justify-between">
                                <h1 className="mt-10 text-pretty text-3xl tracking-tight text-gray-900 sm:text-5xl">
                                    Set your Investment Level
                                </h1>
                                <p className="mt-6 text-lg/8 text-black-50">
                                    Portfolio Management
                                </p>
                                <p className="mt-6 text-lg/8 text-gray-600">
                                    Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo.
                                </p>
                            </div>
                        </div>
                    </div>
                    <img
                        src={Investment.src}
                        alt="image"
                        className='object-cover rounded-clg flex'
                    />
                </div>
                <div className="mx-auto max-w-7xl pb-24 pt-10 sm:pb-32 flex lg:gap-x-8 lg:px-8 lg:py-40">
                    <div className="flex px-6 lg:px-0 lg:pt-4">
                        <div className="mx-auto max-w-2xl">
                            <img
                                className="h-11"
                                src="https://tailwindui.com/plus/img/logos/mark.svg?color=indigo&shade=600"
                                alt="Your Company"
                            />
                            <div className="max-w-lg flex flex-col justify-between">
                                <h1 className="mt-10 text-pretty text-3xl tracking-tight text-gray-900 sm:text-5xl">
                                    Monitor your Portfolio in your Dashboard
                                </h1>
                                <p className="mt-6 text-lg/8 text-black-50">
                                    Portfolio Management
                                </p>
                                <p className="mt-6 text-lg/8 text-gray-600">
                                    Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo.
                                </p>
                            </div>
                        </div>
                    </div>
                    <img
                        src={Dashboard.src}
                        alt="image"
                        className='object-cover rounded-clg flex'
                    />
                </div>
            </div>
        </div>
    );
};

export default Instructions;
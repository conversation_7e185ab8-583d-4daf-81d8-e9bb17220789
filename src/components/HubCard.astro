---

interface Props {
	title: any;
    postHref: string;
    imgSrc: string;
    colorClass: string;
}

const { title, postHref, imgSrc, colorClass } = Astro.props;

const cardClass = "z-10 flex shrink-0 w-full md:w-[223px] h-[150px] md:h-[300px] lg:w-[389px] lg:h-[400px] text-white rounded-cmd mb-4 " + colorClass;
const imgClass = "hidden md:block relative right-[40px] lg:right-[30px] w-[170px] lg:w-[240px]";

---
<div class={cardClass}>
    <a class="h-full w-full flex flex-col justify-between pl-6 lg:pl-10 pt-4 lg:pt-12 pb-2 lg:pb-6" href={postHref}>
        <p class="text-3xl md:text-4xl lg:text-5xl tracking-normal font-extrabold">{title}</p>
        <img class={imgClass} src={imgSrc} alt="" />
        <p class="md:hidden block text-xl tracking-normal font-medium pb-3">See most popular  ↓</p>
    </a>
</div>
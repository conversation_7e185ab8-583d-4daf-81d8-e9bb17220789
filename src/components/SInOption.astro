---
import <PERSON><PERSON> from './Button.astro';

interface Props {
	textH5: string;
    text2xl: string;
    textp: string;
    imgUrl: string;
    btnText: string;
    btnUrl: string;
}

const { textH5, imgUrl, text2xl, textp, btnText, btnUrl } = Astro.props;
---

<div class="max-w-xs md:max-w-sm lg:max-w-md p-6 lg:px-8 text-center rounded-cxl bg-white mx-auto py-12 shadow-light mb-12">
    <h5 class="text-3xl lg:text-5xl tracking-normal font-extrabold pb-4">
       {textH5}
    </h5>
    <p class="text-lg lg:text-2xl tracking-wide font-bold pb-6">
        {text2xl} 
    </p>
    <video playsinline="" autoplay loop muted  class="mx-auto py-6 w-[180px] lg:w-[240px]">
        <source src={imgUrl} type="video/mp4">
    </video>
    <p class="text-base lg:text-xl tracking-wider font-regular pb-6 px-3">
        {textp} 
    </p>
    <a href={btnUrl}><Button type="button" color="btn-black" text={btnText} /></a>
    
</div>

<style scoped>
    .shadow-light {
        box-shadow: 0px 4px 4px 0px #00000040;
    }
   
</style>

import React, { useState } from "react";
import { XMarkIcon } from '@heroicons/react/20/solid'

import { motion } from 'motion/react';

export default function BannerC() {
    const [showBanner, setShowBanner] = useState(true);
  const hideBannerHandler = () => setShowBanner(false);

  return (
    <motion.div initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0, duration: 10 }}>
      {showBanner ? 
       <div className="flex items-center gap-x-6 bg-gray-900 px-6 py-2.5 sm:px-3.5 sm:before:flex-1">
       <p className="text-sm/6 text-white">
         <a href="/guides/uk-property-investment-guide">
           <strong className="font-semibold">UK Property Investment Guide</strong>
           <svg viewBox="0 0 2 2" aria-hidden="true" className="mx-2 inline size-0.5 fill-current">
             <circle r={1} cx={1} cy={1} />
           </svg>
           Find out what is driving the market in 2025  <span aria-hidden="true">&rarr;</span>
         </a>
       </p>
       <div className="flex flex-1 justify-end">
         <button type="button" className="-m-3 p-3 focus-visible:outline-offset-[-4px]">
           <span className="sr-only">Dismiss</span>
           <XMarkIcon aria-hidden="true" className="size-5 text-white" />
         </button>
       </div>
     </div> : ''}
    </motion.div>
  )
}
import { motion } from 'motion/react'
import { Container } from '../components/ContainerA.jsx'
import {
  Expandable,
} from '../components/Expandable.jsx'
import { SectionHeading } from '../components/SectionHeading.jsx'

const tableOfContents = {
  'House Prices': {
    'House Prices: A look back over the past 5 years': 3,
    'Interest Rates: Forecasts and most effected areas': 4,
    'House Price Projections: Wages, affordability and regional differences': 7,
    'Investment Strategies: 2025-2030': 7,
  },
  'Rental Market': {
    'Rental Performance: Whats happened since covid?': 15,
    'Key Drivers of the rental market moving forward': 17,
    'UK City Rental League Table': 19,
    'Challenges facing landlords': 20,
    'Viable alternatives to buy-to-let': 21,
  },
  'Construction Industry': {
    'The state of housebuilding: Where do we stand?': 23,
    "Construction Inflation: What's hapenning to construction costs": 26,
    "House Building Target: UK government's position": 27,
  },
  'Conclusions': {
    'Key Conclusions': 30,
  },
}

export function TableOfContents() {
  return (
    <section
      id="table-of-contents"
      aria-labelledby="table-of-contents-title"
      className="scroll-mt-14 py-16 sm:scroll-mt-32 sm:py-20 lg:py-32"
    >
      <Container>
        <SectionHeading number="1" id="table-of-contents-title">
          Table of contents
        </SectionHeading>
        <motion.p initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
          transition={{ duration: 0.5, ease: "easeIn" }} className="mt-8 font-display text-4xl font-bold tracking-tight text-slate-900">
          Get a look at all of the content covered in the guide. Everything you
          need to know is inside.
        </motion.p>
        <motion.p initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
          transition={{ duration: 0.5, ease: "easeIn" }} className="mt-4 text-lg tracking-tight text-slate-700">
          Our UK property investment guide is comprised of 30 tightly edited,
          highly visual pages designed to teach you everything you need to know
          about property invertment in the UK.
        </motion.p>
        <Expandable>
          <ol role="list" className="mt-16 space-y-10 sm:space-y-16">
            {Object.entries(tableOfContents).map(([title, pages]) => (
              <motion.li initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                transition={{ duration: 0.5, ease: "easeIn" }} key={title}>
                <h3 className="font-display text-3xl font-bold tracking-tight text-slate-900">
                  {title}
                </h3>
                <ol
                  role="list"
                  className="mt-8 divide-y divide-slate-300/30 rounded-clg bg-slate-50 px-6 py-3 text-base tracking-tight sm:px-8 sm:py-7"
                >
                  {Object.entries(pages).map(([title, pageNumber]) => (
                    <motion.li initial={{ opacity: 0, y: 50 }}
                      whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                      transition={{ duration: 0.5, ease: "easeIn" }}
                      key={title}
                      className="flex justify-between py-3"
                      aria-label={`${title} on page ${pageNumber}`}
                    >
                      <span
                        className="font-medium text-slate-900"
                        aria-hidden="true"
                      >
                        {title}
                      </span>
                      <span
                        className="font-mono text-slate-400"
                        aria-hidden="true"
                      >
                        {pageNumber}
                      </span>
                    </motion.li>
                  ))}
                </ol>
              </motion.li>
            ))}
          </ol>
        </Expandable>
      </Container>
    </section>
  )
}

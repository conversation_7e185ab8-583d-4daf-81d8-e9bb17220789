---
import InvModelOption from "../components/InvModelOption.astro";

import InvSelect from "../assets/images/home/<USER>";
import InvDevelop from "../assets/videos/home-inv-develop.mp4";
import InvAcquire from "../assets/videos/home-inv-acquire.mp4";
import InvPayout from "../assets/videos/home-inv-payout.mp4";
import InvRaise from "../assets/videos/home-inv-raise.mp4";
---

<div class="carousel-container relative w-full">
    <!-- Carousel wrapper -->
    <div class="carousel_items relative overflow-hidden h-[500px] md:h-[600px] lg:h-[700px]">
        <!-- Item 1 -->
        <div
            class="carousel_item"
        >
            <InvModelOption
                textH5="Select"
                textp="We examine hundreds of deals every month, working tirelessly to bring only the best projects to our platform."
                imgUrl={InvSelect.src}
                videoUrl=""
            />
        </div>
        <!-- Item 2 -->
        <div
            class="carousel_item"
        >
            <InvModelOption
                textH5="Acquire"
                textp="We maximise project value while reducing any risks. By prioritising critical factors and consulting experts we have everything covered."
                videoUrl={InvAcquire}
                imgUrl=""
            />
        </div>
        <!-- Item 3 -->
        <div
            class="carousel_item"
        >
            <InvModelOption
                textH5="Raise"
                textp="Using the power of the crowd our team raises the funds needed to purchase and develop our  incredible projects. "
                videoUrl={InvRaise}
                imgUrl=""
            />
        </div>
        <!-- Item 4 -->
        <div
            class="carousel_item"
        >
            <InvModelOption
                textH5="Develop"
                textp="Timelines are created by project managers. Once development is underway surveyors ensure everything is progressing properly and to standard."
                videoUrl={InvDevelop}
                imgUrl=""
            />
        </div>
        <!-- Item 5 -->
        <div
            class="carousel_item"
        >
            <InvModelOption
                textH5="Payout"
                textp="Our projects can pay out on a monthly basis from the point you invest, with your initial investment paid back at project completion."
                videoUrl={InvPayout}
                imgUrl=""
            />
        </div>
    </div>
    <!-- Slider indicators -->
    <div
        class="absolute z-30 flex space-x-3 -translate-x-1/2 left-1/2"
    >
        <button
            type="button"
            class="w-3 h-3 rounded-full"
            aria-current="true"
            aria-label="Slide 1"
            data-carousel-slide-to="0"></button>
        <button
            type="button"
            class="w-3 h-3 rounded-full"
            aria-current="false"
            aria-label="Slide 2"
            data-carousel-slide-to="1"></button>
        <button
            type="button"
            class="w-3 h-3 rounded-full"
            aria-current="false"
            aria-label="Slide 3"
            data-carousel-slide-to="2"></button>
        <button
            type="button"
            class="w-3 h-3 rounded-full"
            aria-current="false"
            aria-label="Slide 4"
            data-carousel-slide-to="3"></button>
        <button
            type="button"
            class="w-3 h-3 rounded-full"
            aria-current="false"
            aria-label="Slide 5"
            data-carousel-slide-to="4"></button>
    </div>
    <!-- Slider controls -->
    <button
        type="button"
        class="absolute top-0 left-0 z-30 hidden md:flex items-center justify-center h-full md:pl-[6%] lg:pl-[15%] cursor-pointer group focus:outline-none"
        aria-label="Next"
        data-carousel-prev
    >
        <svg
            width="40"
            height="40"
            viewBox="0 0 40 40"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clip-path="url(#clip0_826_14695)">
                <circle
                    cx="20"
                    cy="20"
                    r="20"
                    transform="rotate(-180 20 20)"
                    fill="black"></circle>
                <path
                    d="M29.9453 18.4714L29.9453 22.0254L16.4645 22.0254L22.5002 28.582L17.7513 28.582L10.6432 21.1063L10.6432 19.4518L17.7513 11.9148L22.5002 11.9148L16.4951 18.4714L29.9453 18.4714Z"
                    fill="white"></path>
            </g>
            <defs>
                <clipPath id="clip0_826_14695">
                    <rect
                        width="1920"
                        height="9511"
                        fill="white"
                        transform="translate(-528 -5776)"></rect>
                </clipPath>
            </defs>
        </svg>
    </button>
    <button
        type="button"
        class="absolute top-0 right-0 z-30 hidden md:flex items-center justify-center h-full md:pr-[6%] lg:pr-[15%] cursor-pointer group focus:outline-none"
        aria-label="Next"
        data-carousel-next
    >
        <svg
            width="40"
            height="40"
            viewBox="0 0 40 40"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <circle cx="20" cy="20" r="20" fill="black"></circle>
            <path
                d="M10.0547 21.5286L10.0547 17.9746L23.5355 17.9746L17.4998 11.418L22.2487 11.418L29.3568 18.8937L29.3568 20.5482L22.2487 28.0852L17.4998 28.0852L23.5049 21.5286L10.0547 21.5286Z"
                fill="white"></path>
        </svg>
    </button>
</div>

<style scoped>
    .carousel_items {
        display: flex;
        wrap: nowrap;
        overflow: hidden;
    }
    .carousel_item {
        position: relative;
        min-width: 100%;
        height: 100vh;
        transition: all 0.5s linear;
        background-repeat: no-repeat;
        background-size: cover;
        background-attachment: fixed;
    }
</style>

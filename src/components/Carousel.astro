---

---

<script>
    const carousels = document.querySelectorAll(".carousel-container");

    Array.from(carousels).forEach((carousel, indexOne) => {
        let carouselItems = carousel.querySelectorAll(".carousel_item");
        let i = 1;
        setInterval(() => {
            // Accessing All the carousel Items
            Array.from(carouselItems).forEach((item, index) => {
                if (i == 0) {
                    item.style.transition = `none`;
                    item.style.transform = `translateX(0%)`;
                } else if (i < carouselItems.length) {
                    item.style.transition = `all 0.5s linear`;
                    item.style.transform = `translateX(-${i * 100}%)`;
                }
            });

            if (i < carouselItems.length) {
                i++;
            } else {
                i = 0;
            }
        }, 2000);
    });
</script>

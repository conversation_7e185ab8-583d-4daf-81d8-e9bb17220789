import { motion } from 'motion/react'
import { ChartBarIcon } from '@heroicons/react/20/solid'

export default function Features() {
    return (
        <section className="flex flex-col justify-center items-center px-6 md:px-10 py-20 sm:py-28 mx-auto">
            <div className='flex flex-col justify-center items-center text-center max-w-2xl mx-auto gap-y-4 pb-20'>
                <motion.div initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.3, ease: "easeIn" }} className='text-sm font-bold tracking-widest uppercase px-2.5 py-2 bg-mint-100 text-black-50 rounded-full'>Features</motion.div>
                <motion.h2 initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.4, ease: "easeIn" }} className="text-4xl md:text-5xl">
                    Property is the cornerstone to build your wealth.
                </motion.h2>
            </div>
            <div className='grid grid-cols-1 mlg:grid-cols-3 text-left gap-10 max-w-[1100px]'>
                <motion.div initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.8, ease: "easeIn" }} className='flex flex-col gap-y-4 p-6 rounded-clg bg-yellow-10 justify-between h-[370px] mlg:max-w-[350px]'>
                    <div className='bg-mint-300 p-4 rounded-csm w-fit'>
                        <svg className="w-7 h-7" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" focusable="false" color="rgb(0, 0, 0)" style={{ "userSelect": "none", "display": "inline-block", "fill": "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", "color": "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", "flexShrink": "0" }}><g color="var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))" weight="regular"><path d="M232,208a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V48a8,8,0,0,1,16,0V200H224A8,8,0,0,1,232,208ZM132,160a12,12,0,1,0-12-12A12,12,0,0,0,132,160Zm-24-56A12,12,0,1,0,96,92,12,12,0,0,0,108,104ZM76,176a12,12,0,1,0-12-12A12,12,0,0,0,76,176Zm96-48a12,12,0,1,0-12-12A12,12,0,0,0,172,128Zm24-40a12,12,0,1,0-12-12A12,12,0,0,0,196,88Zm-20,76a12,12,0,1,0,12-12A12,12,0,0,0,176,164Z"></path></g></svg>
                    </div>
                    <div className='flex flex-col gap-y-5'>
                        <p className='text-2xl'>Improved stability</p>
                        <p className='text-xl text-[#737373]'>Historically, real estate has provided lower volatility compared to stock investments.</p>
                    </div>
                </motion.div>
                <motion.div initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.8, ease: "easeIn" }} className='flex flex-col gap-y-4 p-6 rounded-clg bg-yellow-10 justify-between h-[370px] mlg:max-w-[350px]'>
                    <div className='bg-mint-300 p-4 rounded-csm w-fit'>
                        <svg className="w-7 h-7" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" focusable="false" color="rgb(0, 0, 0)" style={{ "userSelect": "none", "display": "inline-block", "fill": "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", "color": "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", "flexShrink": "0" }}><g color="var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))" weight="regular"><path d="M222.29,123.06l-88-112a8,8,0,0,0-12.58,0l-88,112a8,8,0,0,0,0,9.88l88,112a8,8,0,0,0,12.58,0l88-112A8,8,0,0,0,222.29,123.06ZM136,39.13l67.42,85.8L136,155.58ZM120,155.58,52.58,124.93,120,39.13Zm0,17.57v43.72l-53.43-68Zm16,0,53.43-24.29-53.43,68Z"></path></g></svg>
                    </div>
                    <div className='flex flex-col gap-y-5'>
                        <p className='text-2xl'>Steady income</p>
                        <p className='text-xl text-[#737373]'>Generate a consistent income without being as exposed to recessions.</p>
                    </div>
                </motion.div>
                <motion.div initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.8, ease: "easeIn" }} className='flex flex-col gap-y-4 p-6 rounded-clg bg-yellow-10 justify-between h-[370px] mlg:max-w-[350px]'>
                    <div className='bg-mint-300 p-4 rounded-csm w-fit'>
                        <svg className="w-7 h-7" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" focusable="false" color="rgb(0, 0, 0)" style={{ "userSelect": "none", "display": "inline-block", "fill": "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", "color": "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", "flexShrink": "0" }}><g color="var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))" weight="regular"><path d="M248,128a56,56,0,0,1-95.6,39.6l-.33-.35L92.12,99.55a40,40,0,1,0,0,56.9l8.52-9.62a8,8,0,1,1,12,10.61l-8.69,9.81-.33.35a56,56,0,1,1,0-79.2l.33.35,59.95,67.7a40,40,0,1,0,0-56.9l-8.52,9.62a8,8,0,1,1-12-10.61l8.69-9.81.33-.35A56,56,0,0,1,248,128Z"></path></g></svg>
                    </div>
                    <div className='flex flex-col gap-y-5'>
                        <p className='text-2xl'>Inflation protection</p>
                        <p className='text-xl text-[#737373]'>Safeguard your investment against inflation with our built-in inflation projections.</p>
                    </div>
                </motion.div>
            </div>
        </section>
    )
}

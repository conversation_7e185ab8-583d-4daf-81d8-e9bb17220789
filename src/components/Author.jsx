import { motion } from 'motion/react'
import { GridPattern } from '../components/GridPattern.jsx'
import { SectionHeading } from '../components/SectionHeading.jsx'
import authorImage from '../assets/images/avatars/author2.jpg'

function XIcon(props) {
  return (
    <svg aria-hidden="true" viewBox="0 0 24 24" {...props}>
      <path d="M13.6823 10.6218L20.2391 3H18.6854L12.9921 9.61788L8.44486 3H3.2002L10.0765 13.0074L3.2002 21H4.75404L10.7663 14.0113L15.5685 21H20.8132L13.6819 10.6218H13.6823ZM11.5541 13.0956L10.8574 12.0991L5.31391 4.16971H7.70053L12.1742 10.5689L12.8709 11.5655L18.6861 19.8835H16.2995L11.5541 13.096V13.0956Z" />
    </svg>
  )
}

export function Author() {
  return (
    <section
      id="author"
      aria-labelledby="author-title"
      className="relative scroll-mt-14 pb-3 pt-8 sm:scroll-mt-32 sm:pb-16 sm:pt-10 lg:pt-16"
    >
      <div className="absolute inset-x-0 bottom-0 top-1/2 text-slate-900/10 [mask-image:linear-gradient(transparent,white)]">
        <GridPattern x="50%" y="100%" />
      </div>
      <div className="relative mx-auto max-w-5xl pt-16 sm:px-6">
        <div className="bg-slate-50 pt-px sm:rounded-6xl">
          <div className="relative mx-auto -mt-16 h-44 w-44 overflow-hidden rounded-full bg-slate-200 md:float-right md:h-64 md:w-64 md:[shape-outside:circle(40%)] lg:mr-20 lg:h-72 lg:w-72">
            <motion.img initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }}
              className="absolute inset-0 h-full w-full object-cover"
              src={authorImage.src}
              alt=""
              sizes="(min-width: 1024px) 18rem, (min-width: 768px) 16rem, 11rem"
            />
          </div>
          <div className="px-4 py-10 sm:px-10 sm:py-16 md:py-20 lg:px-20 lg:py-32">
            <SectionHeading number="4" id="author-title">
              Book a Call
            </SectionHeading>
            <motion.p initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }} className="mt-8 font-display text-3xl smd:text-5xl font-extrabold tracking-tight text-slate-900 sm:text-3xl">
              <span className="block text-mint-500">Haaris Ahmed –</span> Founder of UOWN
            </motion.p>
            <motion.p initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }} className="mt-4 text-lg tracking-tight text-slate-700">
              Property investment has been a tried-and-tested way for
              people and businesses to build wealth overthe years. And
              the UK market still has plenty to offer in 2025.</motion.p>
            <motion.p initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }} className="mt-4 text-lg tracking-tight text-slate-700">
              In this guide, we break down what’s driving the market—
              from interestrates to population growth—and show you
              why looking beyond the usual hotspots in the South East
              could be a smart move.</motion.p>
            <motion.p initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }} className="mt-4 text-lg tracking-tight text-slate-700">
              Buy-to-letinvestors are facing new challenges, like higher
              stamp duty and stricter regulations, butthat doesn’t mean
              the opportunities are gone. Investors are increasingly
              turning to platforms like UOWN to make property
              investment work forthem in today’s market.
            </motion.p>
            <motion.div initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }} className="mt-10 flex flex-col justify-center gap-x-6 pb-14">
              <div className="text-base font-semibold leading-6 text-gray-900">
                <a href="https://cal.com/suhayb-uown/uown-guide-2025-invest-with-uown" data-attr="bookacall" className='bg-mint-300 rounded-full px-4 py-3 text-md font-bold text-black-200 shadow-sm transition duration-150 ease-in hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2'>
                  Book a call
                </a>
              </div>
            </motion.div>

          </div>
        </div>
      </div>
    </section>
  )
}

---
---
<script>

import HeroVideoXl from "../assets/videos/why-uown-hero-video-xl.mp4";
import HeroVideoLg from "../assets/videos/why-uown-hero-video-lg.mp4";
import HeroVideoMd from "../assets/videos/why-uown-hero-video-md.mp4";
import HeroVideoSm from "../assets/videos/why-uown-hero-video-sm.mp4";

var video = document.getElementById('hero');

var WindowWidth = document.body.clientWidth;

if (video != null) {
    if (WindowWidth > 1280) {
    video.setAttribute("src", HeroVideoXl);
} else if (WindowWidth > 800) {
    video.setAttribute("src", HeroVideoLg);
} else if (WindowWidth > 480) {
    video.setAttribute("src", HeroVideoMd);
} else {
    video.setAttribute("src", HeroVideoSm);
}
}

</script>
import { motion } from 'motion/react'
import ctaImage from "../assets/images/ctas/ctaImage.png";

export default function CTASection() {

  return (
    <motion.div initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
      transition={{ duration: 0.5, ease: "easeIn" }}
      className="relative bg-gray-900">
      <div className="relative mx-auto max-w-7xl py-24 sm:py-32 lg:px-8 lg:py-40">
        <div className="pl-6 pr-6 md:mr-auto mlg:w-1/3 mlg:pr-16 lxl:w-1/2 lxl:pl-0 lxl:pr-24 xl:pr-32">

          <p className="mt-2 text-4xl font-semibold tracking-tight text-white sm:text-5xl">Get into the data</p>
          <p className="mt-6 text-base/7 text-gray-300"> Our guide uses high quality data to breakdown the UK housing market, and predict where prices and rents are going in 2025 and beyond. We look at the key drivers from interest rates to population growth.</p>
          <div className="mt-8">
            <a href="#free-chapters" className="inline-flex rounded-md bg-white/10 px-3.5 py-2.5 text-sm font-semibold text-white shadow-xs hover:bg-white/20 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white">Get the guide</a>
          </div>
        </div>
      </div>
      <div className="relative h-full overflow-hidden bg-white mlg:absolute mlg:top-0 mlg:right-0 mlg:w-2/3 lxl:w-1/2">
        <div className='size-full flex justify-center items-center'>
          <img className="p-6" src={ctaImage.src} alt="" width="1920" />
        </div>
      </div>
    </motion.div>
  )
}
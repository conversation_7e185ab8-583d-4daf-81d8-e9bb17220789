'use client'
import { motion } from 'motion/react';

import { Button } from '../components/ButtonA.jsx'
import { GridPattern } from '../components/GridPattern.jsx'
import { StarRating } from '../components/StarRating.jsx'

import coverImage from '../assets/images/coverImage.png'
import Trustpilot from "../assets/images/home/<USER>";

function Testimonial() {
  return (
    <div className="relative mx-auto max-w-md text-center lg:mx-0 lg:text-left">
      <motion.div initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
        transition={{ duration: 0.5, ease: "easeIn" }}
        className="flex justify-center text-mint-500 lg:justify-start">
        <div className="flex justify-center items-center">
					<span className="text-2xl tracking-normal font-bold"
						>4.9/5</span><a
						href="https://uk.trustpilot.com/review/uown.co"
						target="_blank"><img src={Trustpilot.src} alt="" /></a
					>
				</div>
      </motion.div>
    </div>
  )
}

export function Hero() {
  return (
    <header className="overflow-hidden bg-slate-100 lg:bg-transparent lg:px-5">
      <div className="mx-auto grid max-w-6xl grid-cols-1 grid-rows-[auto_1fr] gap-y-16 pt-16 md:pt-20 lg:grid-cols-12 lg:gap-y-20 lg:px-3 lg:pb-36 lg:pt-20 xl:py-32">
        <div className="relative flex items-end lg:col-span-5 lg:row-span-2">
          <div className="absolute -bottom-12 -top-20 left-0 right-1/2 z-10 rounded-br-6xl bg-mint-500 text-white/10 md:bottom-8 lg:-inset-y-32 lg:left-[-100vw] lg:right-full lg:-mr-40">
            <GridPattern
              x="100%"
              y="100%"
              patternTransform="translate(112 64)"
            />
          </div>
          <motion.div initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
            transition={{ duration: 0.5, ease: "easeIn" }} className="relative z-10 mx-auto flex w-64 rounded-xl   md:w-80 lg:w-auto">
            <img className="w-full" src={coverImage.src} alt="" />
          </motion.div>
        </div>
        <div className="relative px-4 sm:px-6 lg:col-span-7 lg:pb-14 lg:pl-16 lg:pr-0 xl:pl-20">
          <div className="hidden lg:absolute lg:-top-32 lg:bottom-0 lg:left-[-100vw] lg:right-[-100vw] lg:block lg:bg-slate-100" />
          <Testimonial />
        </div>
        <div className="bg-white py-16 lg:col-span-7 lg:bg-transparent lg:pl-16 lg:py-0 xl:pl-20">
          <div className="mx-auto px-4 sm:px-6 md:max-w-2xl md:px-4 lg:px-0">
            <motion.h1 initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.6, ease: "easeIn" }}
              className="font-display text-5xl font-extrabold text-slate-900 sm:text-6xl">
              UOWN 2025 UK Property Investment Guide
            </motion.h1>
            <motion.p initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.7, ease: "easeIn" }}
              className="mt-4 text-3xl text-slate-600">
              Discover the key trends and insights to help you make informed investments in UK property this year.
            </motion.p>
            <motion.div initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.8, ease: "easeIn" }}
              className="mt-8 flex gap-4">
              <Button href="#free-chapters" color="mint" aria-label="Get the guide">
                Get the guide
              </Button>

            </motion.div>
          </div>
        </div>
      </div>
    </header>
  )
}

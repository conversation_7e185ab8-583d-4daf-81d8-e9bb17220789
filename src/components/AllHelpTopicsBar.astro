---
import { getAllTopics, getFullUrl } from "../lib/sanity-client.ts";

const topics = await getAllTopics();

interface Props {
	isSticky: boolean;
    hasBorder: boolean;
    selectedTopic: string;
}

const {  isSticky, hasBorder, selectedTopic } = Astro.props;

const divClass= "hidden w-[280px] max-h-[620px] lg:flex lg:gap-y-8 shrink-0 flex-col text-left p-8 rounded-cmd bg-white " + ((isSticky) ? ' sticky top-24' : '') + ((hasBorder) ? ' border-blk' : ' topics-shadow');
---

<div
    class={divClass}
>
    {
        topics.map((topic: any) => (
            (selectedTopic == topic.name) ?
            <a
                class="text-xl tracking-normal font-bold"
                href={getFullUrl(Astro.url.origin, topic.full_slug)}
            >
                {topic.name}
            </a>
            :
            <a
                class="text-xl tracking-normal font-medium"
                href={getFullUrl(Astro.url.origin, topic.full_slug)}
            >
                {topic.name}
            </a>
        ))
    }
</div>

<style scoped>
    .topics-shadow {
        box-shadow: 0px 3.9958341121673584px 5.993751049041748px -1.9979170560836792px #0000000D;
    }
</style>
'use client'
import { motion } from 'motion/react';

import Prop1 from '../assets/images/stake-page/prop-1.png';
import Prop2 from '../assets/images/stake-page/prop-2.png';
import Prop3 from '../assets/images/stake-page/prop-3.png';
import Prop1Sm from '../assets/images/stake-page/prop-1-sm.png';
import Prop2Sm from '../assets/images/stake-page/prop-2-sm.png';
import Prop3Sm from '../assets/images/stake-page/prop-3-sm.png';


export default function InvestStake() {
    return (
        <section className="relative py-24 overflow-hidden">
            <div className="relative mx-auto max-w-7xl overflow-visible">
                <div className="max-w-2xl text-black-100 flex flex-col items-center justify-center text-center gap-y-1.5 mx-auto pb-12">
                    <p className="text-sm lxl:text-base text-mint-500">How it works</p>
                    <h2 className="text-4xl lxl:text-5xl pb-4">Build an income-generating real estate portfolio, easily</h2>
                </div>
            </div>
        </section>
    )

}

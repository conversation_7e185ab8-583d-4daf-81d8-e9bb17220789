import React, { useState, type FormEvent } from 'react';
import Input from 'react-phone-number-input/input';
import { isPossiblePhoneNumber } from 'react-phone-number-input'

import DialogOverlay from "../components/Dialog.jsx";
import { createRoot, type Root } from "react-dom/client";

export default function SendWhatsappSMS() {

  const useCbOnce = (cb: (arg0: any) => void) => {
    const [called, setCalled] = useState(false);

    // Below can be wrapped in useCallback whenever re-renders becomes a problem
    return (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      if (!called) {
        setCalled(true);
        disableSubmitBtn();

        if (checkIfDisableBtn() == false) {
          cb(e);
          updateMaxTries();
          setCalled(false);
          enableSubmitBtn();
        }
      }
    }
  }

  function disableSubmitBtn() {
    document.getElementById("submit")?.setAttribute('disabled', 'disabled');
  }

  function enableSubmitBtn() {
    document.getElementById('submit')?.removeAttribute("disabled");
  }

  function getMaxTries() {
    const maxTries = localStorage.getItem('maxTries');

    if (maxTries) {
      return JSON.parse(maxTries);
    }

    return null;
  }

  function getTimeStamp() {
    const timeStamp = localStorage.getItem('timeStamp');

    if (timeStamp) {
      return JSON.parse(timeStamp);
    }

    return null;
  }

  function updateMaxTries() {
    const maxTries = getMaxTries();
    const timeStamp = getTimeStamp();

    localStorage.setItem('maxTries', JSON.stringify(maxTries as number + 1));

    if (maxTries !== null && maxTries > 1 && timeStamp == null) {
      localStorage.setItem('timeStamp', JSON.stringify(Date.now()));
    }

  }

  function checkIfDisableBtn() {
    const timeStamp = getTimeStamp();
    const dateNow = Date.now();

    if (timeStamp !== null && (dateNow - timeStamp) < 21600000 && getMaxTries() >= 1) {
      disableSubmitBtn();
      return true;
    } else if (timeStamp !== null && (dateNow - timeStamp) > 21600000) {
      localStorage.removeItem('timeStamp');
      localStorage.removeItem('maxTries');
    }
    return false;
  }

  function PhoneNumber() {
    const [value, setValue] = useState<string>();
    return (
      <Input
        useNationalFormatForDefaultCountryValue={false}
        required
        withCountryCallingCode
        country="GB"
        international
        name="number"
        id="number"
        placeholder="+44 7889 012 312"
        value={value}
        className="block w-full rounded-full border-0 py-3 pl-20 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-500 focus:ring-2 focus:ring-inset focus:border-mint-300 text-lg tracking-widest focus:ring-mint-300"
        onChange={(string) => setValue(string)} />
    )
  }
  const submit = useCbOnce(async (e: React.FormEvent<HTMLFormElement>) => {

    let root: Root | null = null;
    function showErrorDialog(message: string) {
      var dialogDiv = document.getElementById('dialog-container');
      if (dialogDiv) {
        if (!root) {
          root = createRoot(dialogDiv);
        }

        root?.render(<DialogOverlay
          heading="Submission failed"
          content={message}
          button="Close"
          classes="bg-salmon-200 text-black hover:bg-salmon-300 focus-visible:outline-salmon-300"
          type="error"
        />);
      }
    }

    function showSuccessDialog() {
      var dialogDiv = document.getElementById('dialog-container');
      if (dialogDiv) {
        if (!root) {
          root = createRoot(dialogDiv);
        }
        root?.render(<DialogOverlay
          heading="Submission Successful"
          content="We have sent you a copy of the investment brochure."
          button="Close"
          classes="bg-mint-300 text-black hover:bg-mint-500 focus-visible:outline-mint-500"
        />);
      }
    }

    const formData = new FormData(e.target as HTMLFormElement);
    const number = formData.get('number');

    if (number !== null) {
      if (!isPossiblePhoneNumber(number as string)) {
        showErrorDialog('The phone number you entered is not valid. Please try again');
      } else {
        const response = await fetch("/api/sms", {
          method: "POST",
          body: formData,
        });

        const data = await response.json();

        if (data.status == "accepted") {
          showSuccessDialog();
        } else {
          showErrorDialog('Oops... Something has gone wrong. Please try again.');
        }
      }
    }
  });

  return (
    <form method="POST" onSubmit={submit} className="flex flex-col form max-w-lg gap-x-6 gap-y-4 mx-3 mt-4 z-10 relative h-[48px]">
      <div>
        <div className="absolute inset-y-0 left-0 flex items-center">
          <label htmlFor="country" className="sr-only">Country</label>
          <select
            id="country"
            name="country"
            autoComplete="country"
            className="h-full rounded-full border-0 bg-transparent py-0 pl-3 pr-7 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:border-mint-300 text-base focus:ring-mint-300 sm:text-sm"
          >
            <option>GB</option>
          </select>
        </div>
        {PhoneNumber()}
      </div>
      <button onClick={(e) => { e.stopPropagation(); }} id="submit" type="submit" className="block h-[48px] rounded-full bg-mint-300 px-4 py-3 text-base font-bold text-black shadow-sm hover:bg-mint-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-mint-300">
        Get the Brochure
      </button>
      <div id="dialog-container">
      </div>
    </form>
  );
}



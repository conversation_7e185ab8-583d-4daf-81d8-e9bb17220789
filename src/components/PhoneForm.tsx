import LoadingState from "../assets/images/wicker-island/three-dots-wicker.svg";
import CheckMark from "../assets/images/wicker-island/check-wicker.svg";
import Cross from "../assets/images/wicker-island/times-solid-wicker.svg";

import React, { useState } from 'react'
import Input from 'react-phone-number-input/input'

export default function PhoneForm() {

    function PhoneNumber() {
        const [value, setValue] = useState<string>();
        return (
            <Input
                useNationalFormatForDefaultCountryValue={false}
                required
                withCountryCallingCode
                country="GB"
                international
                name="phone-number"
                id="phone-number"
                placeholder="+44 7889 012 312"
                value={value}
                className="block w-full rounded-md border-0 py-3 pl-20 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-500 focus:ring-2 focus:ring-inset focus:border-rose-500 text-base focus:ring-rose-500 sm:text-sm sm:leading-6"
                onChange={(string) => setValue(string)} />
        )
    }

    return (
        <form method="POST" id="phone-form" className="mt-12 sm:flex sm:w-full sm:max-w-lg">
            <div className="min-w-0 flex-1">
                <label htmlFor="hero-name" className="sr-only">Name</label>
                <input
                    id="p-name"
                    name="name"
                    type="text"
                    className="block w-full rounded-md border border-gray-300 px-5 py-3 text-base text-gray-900 placeholder-gray-500 shadow-sm focus:border-rose-500 focus:ring-rose-500"
                    placeholder="Enter your mobile number"
                    minLength={6}
                    required
                />
                <label htmlFor="hero-phone" className="sr-only">Phone Number</label>
                <div className="relative mt-2 rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 flex items-center">
                        <label htmlFor="country" className="sr-only">Country</label>
                        <select
                            id="country"
                            name="country"
                            autoComplete="country"
                            className="h-full rounded-md border-0 bg-transparent py-0 pl-3 pr-7 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm"
                        >
                            <option>GB</option>
                        </select>
                    </div>
                    {PhoneNumber()}
                </div>
                <button
                    id="recaptcha2"
                    type="submit"
                    className="block w-full rounded-md border border-transparent bg-rose-500 px-5 mt-2 py-3 text-base font-medium text-white shadow hover:bg-rose-600 focus:outline-none focus:ring-2 focus:ring-rose-500 focus:ring-offset-2 sm:px-10"
                >
                    <span id="submit" className="text-submit">Arrange a call</span>
                    <span id="loading" className="flex h-[25px] justify-center items-center loading-state hidden"><img src={LoadingState.src} alt="" className="absolute w-14" /></span>
                    <span id="checkmark" className="flex h-[25px] justify-center items-center checkmark hidden"><img src={CheckMark.src} alt="" className="absolute w-6" /></span>
                    <span id="cross" className="flex h-[25px] justify-center items-center cross hidden"><img src={Cross.src} alt="" className="absolute w-6" /></span>

                </button>
            </div>
        </form>
    );
}
---

interface Props {
	readingTime: any;
    postHref: string;
    imgSrc: string;
    title: string;
    color: string;
}

const { readingTime, postHref, imgSrc, title, color } = Astro.props;

const imgClass= "z-0 rounded-t-[20px] hidden md:block object-cover h-[150px] lg:h-[200px] border-solid border-b-[10px] " + color;
---

<a class="flex flex-col flex-wrap shrink-0 w-full md:w-[222px] lg:w-[389px] mb-6 md:mb-12" href={postHref}>
    <img class={imgClass} src={imgSrc} />
    <div class={"flex flex-col justify-between md:h-[150px] lg:h-[200px] p-3 lg:p-6 rounded-[20px] md:rounded-t-none  md:rounded-b-[20px] shadow-articletile md:border-0 border-solid border-[1.4px] " + color }>
        <p class="text-lg lg:text-2xl tracking-normal font-bold pb-6 md:pb-0">{title}</p>
        <p class="text-sm lg:text-lg tracking-wide font-medium"> {readingTime} min read<span class="pl-3">→</span></p>
    </div>
</a>
'use client';
import { useTransform, useScroll, motion } from 'motion/react';
import { useEffect, useRef } from 'react';
import Lenis from '@studio-freight/lenis'

import Cardiff from "../assets/images/wicker-island/cardiff-main.webp";
import Table from "../assets/images/wicker-island/Setting-Dinner-Table.webp";

const slides = [
    {
        id: 1,
        title: "Identities",
        description: "We create motion identities that transform your brand.",
        src: Cardiff,
    },
    {
        id: 2,
        title: "Systems",
        description: "We build motion systems that scale your identity.",
        src: Table,
    },
    {
        id: 3,
        title: "Applications",
        description: "We activate your brand across a range of global touchpoints.",
        src: Cardiff,
    },
]

export default function Home() {
    const container = useRef(null);
    const { scrollYProgress } = useScroll({
        target: container,
        offset: ['start start', 'end end']
    })

    useEffect(() => {
        const lenis = new Lenis()

        function raf(time) {
            lenis.raf(time)
            requestAnimationFrame(raf)
        }

        requestAnimationFrame(raf)
    })

    return (
        <div className="bg-black-100 lg:px-28 smd:px-6 px-3">
            <div className="pb-16">
                <div ref={container} className="relative lg:max-w-4xl mx-auto">
                    {
                        slides.map((slide, i) => {
                            const targetScale = 1 - ((slides.length - i) * 0.05);
                            return <Card key={`p_${i}`} i={i} {...slide} progress={scrollYProgress} range={[i * .25, 1]} targetScale={targetScale} />
                        })
                    }
                </div>
            </div>
        </div>
    )
}

const Card = ({ i, id, title, description, src, progress, range, targetScale }) => {

    const container = useRef(null);
    const { scrollYProgress } = useScroll({
        target: container,
        offset: ['start end', 'start start']
    })

    const imageScale = useTransform(scrollYProgress, [0, 1], [2, 1])
    const scale = useTransform(progress, range, [1, targetScale]);

    return (
        <div ref={container} className="bg-black-100 h-screen flex items-normal center justify-center sticky top-[100px]">
            <motion.div
                style={{ scale, top: `calc(0vh + ${i * 25}px)` }}
                className="bg-black-100 flex lg:flex-row flex-col justify-between gap-x-24 relative top-[0%] h-[620px] smd:h-[500px] lg:h-[600px] max-w-[1000px] pt-0 px-3 md:px-0 origin-top"
            >
                <div className='flex gap-x-12 smd:gap-x-24 lg:gap-x-12 items-start'>
                    <div className='text-[120px] lg:text-[240px] font-bold text-white'>{id}</div>
                    <div className='text-2xl lg:text-lg text-white mt-[45px] lg:mt-[100px]'>({title})</div>
                </div>
                <div className="flex h-full lg:mt-[100px]">
                    <div className="relative flex flex-col text-white">
                        <h3 className="text-left m-0 text-2xl md:text-[28px] mb-4">{description}</h3>
                        <div className="max-w-[300px] smd:max-w-[400px] mt-[20px]">
                            <motion.div
                                className="w-full h-full"
                            >
                                <img
                                    src={src.src}
                                    alt="image"
                                    className='object-cover  rounded-cmd'
                                />
                            </motion.div>
                        </div>
                    </div>

                </div>
            </motion.div>
        </div>
    )
}
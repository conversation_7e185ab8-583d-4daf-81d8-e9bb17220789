'use client'
import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { ShieldCheckIcon, LifebuoyIcon } from '@heroicons/react/24/outline'

import Tactic from "../assets/images/stake-page/tactic.svg";
import Lock from "../assets/images/stake-page/lock.svg";
import Bug from "../assets/images/stake-page/bug.svg";
import Crowdsource from "../assets/images/stake-page/crowd.svg";

const tabs = [
    {
        name: 'Ringfenced Projects',
        desc: "Invest with the assurance that each project is financially reingfenced from the others.",
        btnTxt: "Learn more",
        icon: LifebuoyIcon,
        items: [
            {
                imgUrl: Tactic,
                title: "Each project has it's own Special Purpose Vehicle (SPV)",
                desc: "This means if something goes wrong with one project, it won't affect the others."
            },
            {
                imgUrl: Crowdsource,
                title: "Point Number 2",
                desc: "Regulated in Saudi Arabia by the CMA"
            },
        ],
    },
    {
        name: 'Digital Security',
        desc: "Investors receive verifiable ownership documents issued by globally recognised government bodies and top tier fund managers",
        btnTxt: "Learn more",
        icon: ShieldCheckIcon,
        items: [
            {
                imgUrl: Lock,
                title: "Share Certificates and Title Deeds in the UK",
                desc: "Share Certificates are backed by the Dubai International Financial Centre, and Title Deeds are issued by the DLD."
            },
            {
                imgUrl: Bug,
                title: "Regular Penetration Testing",
                desc: "We regularly use 3rd party security experts to penetration test our platform to ensure it is secure."
            },
        ],
    },

]

function classNames(...classes) {
    return classes.filter(Boolean).join(' ')
}

export default function SafetyStake() {
    const [selectedTab, setSelectedTab] = useState(tabs[0]);

    return (
        <section className="bg-[#121c30]">
            <div className="max-w-7xl mx-auto relative py-12 px-6 md:px-12 lg:px-24 overflow-hidden">
                <div className="max-w-2xl text-white flex flex-col items-center justify-center text-center gap-y-1.5 mx-auto pb-12">
                    <p className="text-sm lxl:text-base text-mint-500">Safety never sleeps</p>
                    <h2 className="text-4xl lxl:text-5xl pb-4">Trust is the most important thing we have</h2>
                </div>
                <div className="block pb-6 md:pb-12">
                    <nav aria-label="Tabs" className="flex items-center justify-center space-x-4">
                        {tabs.map((tab) => (
                            <a
                                key={tab.name}
                                className={tab === selectedTab ? "bg-[#19373e] text-mint-500 rounded-md px-3 py-2 text-base lxl:text-lg font-medium" : "text-white hover:bg-[#263044] hover:bg-opacity-40 rounded-md px-3 py-2  text-base lxl:text-lg font-medium"}
                                onClick={() => setSelectedTab(tab)}>
                                {tab.name}
                            </a>
                        ))}
                    </nav>
                </div>
                <div className="tabs text-white">
                    <AnimatePresence mode="wait">
                        <motion.div
                            key={selectedTab ? selectedTab.name : "empty"}
                            initial={{ y: 10, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            exit={{ y: -10, opacity: 0 }}
                            transition={{ duration: 0.2 }}
                            className="flex flex-col lg:flex-row gap-x-5 bg-[#1c263a] rounded-cmd border border-[#263044] p-8 md:p-12"
                        >
                            <div className="flex flex-col">
                                <selectedTab.icon className="w-10 h-10 text-mint-500 mb-6" />
                                <p className="text-3xl font-bold pb-6 lg:pb-12">{selectedTab ? selectedTab.name : "😋"}</p>
                                <p className="text-lg pb-6 lg:pb-12">{selectedTab ? selectedTab.desc : ""}</p>
                                <a href="/" className="text-base w-fit text-white rounded-cmd bg-[#3a4458] py-1.5 px-3.5 mb-6">{selectedTab ? selectedTab.btnTxt : ""}</a>
                            </div>
                            <div className="flex flex-col gap-y-4 lg:max-w-[468px] w-full">
                                {selectedTab.items.map((item) => (
                                    <div key={item.title} className="flex flex-col justify-between rounded-cmd border border-[#263044] p-4 md:p-6 h-[200px]">
                                        <img src={item.imgUrl.src} className="w-10 h-10 rounded-full fill-mint-500"  alt=""/>
                                        <div>
                                            <p className="text-lg font-semibold">{item.title}</p>
                                            <p className="text-xs lxl:text-sm">{item.desc}</p>
                                        </div>
                                    </div>
                                ))
                                }
                            </div>
                        </motion.div>
                    </AnimatePresence>
                </div>
            </div>
        </section>
    )
}


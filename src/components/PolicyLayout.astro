---
import { Image } from 'astro:assets';

import HalfCylinder from '../assets/images/legal/img-half-large-cylinder.png';
import HalfCircle from '../assets/images/legal/img-large-half-circle-cork.png';
import QuarterCircle from '../assets/images/legal/img-large-quarter-circle-green.png';
import Screw from '../assets/images/legal/img-large-screw.png';
import SphereBlack from '../assets/images/legal/img-large-sphere-black.png';
import SphereGold from '../assets/images/legal/img-large-sphere-golden.png';
import Squircle from '../assets/images/legal/img-large-squircle.png';

interface Props {
	heading: string,
  subheading: string;
}

const { heading, subheading } = Astro.props;
const className = "btn rounded-full bg-black-100 px-4 py-2.5 md:px-6 md:py-4 text-base md:text-lg font-bold text-white tracking-wide mb-7";
const classSelected = "btn rounded-full bg-mint-300 px-4 py-2.5 md:px-6 md:py-4 text-base md:text-lg font-bold text-black tracking-wide mb-7";
---

<div class='main py-16 pt-40 lg:mt-32 lg:pt-32 lg:pb-40 relative overflow-hidden z-1 lg:px-0 px-8'>
    <Image class='sgold absolute z-0 lg:w-52 lg:h-44 md:w-28 md:h-28' width="99" height="82" src={SphereGold}' alt="" />
    <Image class='qcircle absolute z-0 lg:w-52 lg:h-48 md:w-36 md:h-28' width="68" height="77" src={QuarterCircle} alt="" />
    <Image class='squircle absolute z-0' width="486" height="384" src={Squircle} alt="" />
    <Image class='hcircle absolute z-0' width="121" height="100" src={HalfCircle} alt="" />
    <Image class='screw absolute z-0' width="354" height="273" src={Screw} alt="" />
    <Image class='sblack absolute z-0' width="103" height="83" src={SphereBlack} alt=""  />
    <Image class='hcylinder absolute z-0' width="272" height="151" src={HalfCylinder} alt="" />

    <div class='container max-w-5xl mx-auto relative'>
      <h1 class='text-4xl md:text-5xl text-center tracking-normal font-extrabold'>{heading}</h1>
      <p
        class='text-lg md:text-xl lg:text-2xl text-center tracking-wide font-regular max-w-lg mx-auto mt-3 md:mt-6 mb-8 md:mb-24'
      >{subheading}</p>
      <div class='max-w-3xl mx-auto flex flex-wrap justify-center justify-items-center gap-x-8 lg:pb-40 pb-24'>
      <a href="/terms-and-conditions" class={Astro.url.pathname === '/terms-and-conditions' ? classSelected : className }>Terms &amp; Conditions</a>
      <a href="/privacy-policy" class={Astro.url.pathname === '/privacy-policy' ? classSelected : className }>Privacy Policy</a>
      <a href="/cookie-policy" class={Astro.url.pathname === '/cookie-policy' ? classSelected : className }>Cookies Policy</a>
      <a href="https://mangopay.com/terms/payment-services_EN_2024_02.pdf" class={className} target="_blank">MangoPay T&Cs</a>
      <a href="/risk-statement" class={Astro.url.pathname === '/risk-statement' ? classSelected : className }>Risk Statement</a>
      </div>
      <div class='content max-w-5xl lg:mx-auto bg-white mx-8 md:mx-20 px-6 py-4 md:px-11 md:py-9 lg:px-16 lg:py-12 z-1 space-y-4'>
        <slot></slot>
      </div>
    </div>
  </div>

  <style scoped>

    .content {
      border-radius: 40px;
      box-shadow: 4px 8px 14px 14px rgba(6, 6, 6, 0.01);
    }
    
    .btn {
      max-height: 58px;
    }

    .sgold {
      right: 80%;
      top: 1%;
    }

    .qcircle {
      left: 85%;
      top: 15%;
    }

    .squircle {
      right: 73%;
      top: 23%;
    }

    .hcircle {
      left: 86%;
      top: 48%;
    }

    .screw {
      right: 71%;
      top: 61%;
    }

    .sblack {
      left: 86%;
      top: 83%;
    }

    .hcylinder {
      right: 82%;
      top: 96%;
    }

    .z-1 {
      z-index: 1;
    }

    @media screen and (max-width: 480px) {

    }
  </style>
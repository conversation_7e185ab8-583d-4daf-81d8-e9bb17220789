import { AnimatePresence, motion } from 'motion/react';
import React, { useState } from "react";

const Card = ({ image }) => {

  return (
    <motion.div
      className="relative overflow-hidden h-[200px] w-[200px] flex justify-center items-center"
      key={image}
    >
          <motion.div
            className="absolute left-0 top-0 bottom-0 right-0 z-10 flex justify-center items-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.h1
              className="font-semibold text-[10px] z-10 px-3 py-2 rounded-full flex items-center gap-[0.5ch]"
              initial={{ y: 10 }}
              animate={{ y: 0 }}
              exit={{ y: 10 }}
            >
            </motion.h1>
          </motion.div>
      <img loading="lazy" src={image} alt={image} className="max-h-[150px]" />
    </motion.div>
  );
};

export default Card;
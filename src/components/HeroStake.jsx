'use client'
import { motion, useScroll, useTransform } from 'motion/react'

import { useInView } from 'react-intersection-observer';

import GDownload from '../assets/images/stake-page/play-download.svg';
import ADownload from '../assets/images/stake-page/apple-download.svg';
import Phone1 from '../assets/images/stake-page/phone-1.png';
import Phone2 from '../assets/images/stake-page/prop-3.png';
import Phone3 from '../assets/images/slice/uown-mobile.webp';
import S from '../assets/images/roomzzz-glasgow/kitchen.jpg';
import GreenG from '../assets/images/stake-page/green-glow.avif';
import YellowG from '../assets/images/stake-page/yellow-glow.avif';


export default function HeroStake() {

    const { scrollY } = useScroll();
    const y = useTransform(scrollY, [0, 300], [0, -150]);

    const [ref] = useInView({
        threshold: 0.5,
        triggerOnce: false
    });

    return (
        <section className="bg-[#f6f7f9] relative overflow-hidden">
            <motion.div className="relative max-w-7xl px-12 pt-4 flex gap-6 flex-col lg:flex-row justify-center items-center overflow-visible mx-auto">
                <div className="max-w-2xl mx-auto flex flex-col  text-center items-center lg:text-left lg:items-start gap-y-4 text-black-200 lg:basis-1/2 py-16 lg:py-40">
                    <p className="w-fit rounded-full py-1 px-2 bg-mint-50 text-mint-500 border border-mint-500 text-sm font-bold">10.1% average investor returns in 2024</p>
                    <h1 className="text-5xl lxl:text-6xl font-extrabold">Build your wealth through <span className="text-mint-500">real estate</span></h1>
                    <p className="text-base lxl:text-lg">Thousands of investors all over the world are using Stake to access income generating real estate deals in high growth markets, from only AED 500</p>
                    <div className="flex gap-x-3 z-100">
                        <a href="https://apps.apple.com/ee/app/uown-a-home-for-your-money/id1457329490"><img className="h-[49px]" src={ADownload.src} /></a>
                        <a href="https://play.google.com/store/apps/details?id=com.uown&hl=en"><img className="h-[49px]" src={GDownload.src} /></a>
                    </div>
                </div>

                <img className="absolute z-0 right-[-40%] top-[10%] opacity-80" src={YellowG.src} alt="" />
                <img className="absolute z-0 left-[-20%] top-[10%] opacity-40" src={GreenG.src} alt="" />
                <img className="absolute z-0 right-[-40%] top-[-80%] opacity-20" src={GreenG.src} alt="" />

                <motion.div ref={ref} className="relative hidden lg:flex lg:basis-1/2">
                    <motion.div style={{ y }} className="relative w-full h-[607px]">
                        <div className="absolute flex justify-center items-center gap-3 rotate-[-20deg] bottom-[-80%]">
                            <div className="w-[343px] flex flex-col gap-3">
                                <img className="w-full h-full" src={Phone1.src} alt="" />
                                <img className="w-full h-full" src={Phone2.src} alt="" />
                            </div>
                            <div className="w-[343px] h-[675px] flex flex-col gap-3">
                                <img className="w-full h-full" src={Phone3.src} alt="" />
                            </div>
                        </div>
                    </motion.div>
                    <div className="rounded-csm absolute bg-white/85 w-[350px] h-[85px] p-1 top-[60px] lg:top-[68%] lg:left-[-10%] shadow-stake rotate-[-20deg]">
                        <div className="relative flex gap-x-2 items-center rounded-csm bg-mint-50 p-2 w-full h-full">
                            <svg width="48" height="24" viewBox="0 0 48 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.3386 14.2694C20.3386 16.8492 19.3162 19.3234 17.4962 21.1476C15.6763 22.9718 13.2079 23.9966 10.6341 23.9966C8.06037 23.9966 5.592 22.9718 3.77206 21.1476C1.95212 19.3234 0.929688 16.8492 0.929688 14.2694L0.929688 0.845967C0.945298 0.626796 1.03922 0.420614 1.19423 0.265242C1.34924 0.10987 1.55494 0.0157264 1.7736 7.91782e-05H5.57065C5.6819 -0.00145697 5.79233 0.0193733 5.89541 0.0613392C5.99849 0.103305 6.09214 0.165555 6.17081 0.244411C6.24948 0.323267 6.31159 0.417129 6.35345 0.520451C6.39532 0.623773 6.4161 0.734458 6.41457 0.845967V14.2694C6.42974 15.3875 6.88492 16.4542 7.68101 17.2375C8.07287 17.6163 8.53537 17.9141 9.04209 18.1137C9.54881 18.3133 10.0898 18.4108 10.6341 18.4007C11.1785 18.4108 11.7195 18.3133 12.2262 18.1137C12.7329 17.9141 13.1954 17.6163 13.5873 17.2375C14.3834 16.4542 14.8386 15.3875 14.8537 14.2694V0.845967C14.8522 0.734556 14.8729 0.623965 14.9147 0.52072C14.9565 0.417475 15.0185 0.323665 15.0971 0.244825C15.1756 0.165984 15.2692 0.10371 15.3721 0.0616755C15.475 0.0196407 15.5854 -0.00130318 15.6965 7.91782e-05H19.4936C19.6048 -0.00145697 19.7152 0.0193733 19.8183 0.0613392C19.9214 0.103305 20.015 0.165555 20.0937 0.244411C20.1724 0.323267 20.2345 0.417129 20.2764 0.520451C20.3182 0.623773 20.339 0.734458 20.3375 0.845967L20.3386 14.2694Z" fill="#71E5BD"></path><path d="M38.3749 17.9737C37.374 18.5285 36.2489 18.8195 35.1052 18.8195C33.9614 18.8195 32.8363 18.5285 31.8354 17.9737C30.7773 17.392 29.8955 16.5349 29.2829 15.4928C28.6704 14.4506 28.3498 13.262 28.355 12.0525C28.355 10.2299 29.0773 8.48193 30.3631 7.19315C31.6488 5.90438 33.3927 5.18035 35.2111 5.18035C37.0294 5.18035 38.7733 5.90438 40.0591 7.19315C41.3448 8.48193 42.0672 10.2299 42.0672 12.0525C42.0093 13.2738 41.6394 14.4598 40.9928 15.4966C40.3463 16.5334 39.4448 17.3864 38.3749 17.9737ZM35.1052 6.61337e-06C32.7477 6.61337e-06 30.4431 0.700712 28.483 2.01351C26.5228 3.32632 24.995 5.19225 24.0929 7.37536C23.1907 9.55847 22.9547 11.9607 23.4146 14.2783C23.8745 16.5959 25.0097 18.7247 26.6767 20.3956C28.3437 22.0665 30.4676 23.2043 32.7798 23.6653C35.0919 24.1263 37.4886 23.8897 39.6666 22.9855C41.8446 22.0812 43.7062 20.5498 45.0159 18.5851C46.3257 16.6204 47.0248 14.3104 47.0248 11.9475C47.0264 10.378 46.7192 8.82369 46.1208 7.37341C45.5224 5.92313 44.6444 4.6054 43.5373 3.49565C42.4301 2.3859 41.1154 1.50592 39.6685 0.906087C38.2216 0.306257 36.6709 -0.00164549 35.1052 6.61337e-06Z" fill="#101010"></path></svg>
                            <div className="flex flex-col gap-y-1 text-black-100">
                                <p className="uppercase text-base font-bold"><span className="text-mint-300">u</span>own</p>
                                <p className="text-base">You’ve made a profit of £200</p>
                            </div>
                        </div>
                    </div>
                    <div className="hidden lg:flex flex-col gap-y-2 rounded-cmd absolute bg-white w-[200px] p-3 top-[60px] lg:top-[5%] lg:left-[30%] shadow-stake rotate-[-20deg]">
                        <img className="rounded-cmd" src={S.src} alt="" />
                        <p className="text-xl text-black-100">Roomzzz Glasgow</p>
                        <p className="text-2xl font-bold text-mint-500">+12.3%</p>
                    </div>
                </motion.div>
                <motion.div ref={ref} className="max-w-6xl w-full relative flex lg:hidden">
                    <motion.div style={{ y }} className="relative w-full h-[350px] mlg:h-[500px]">
                        <div className="absolute flex justify-center items-center gap-3 top-[20%] left-[-50%] smd:left-0">
                            <div className="flex gap-3">
                                <img className="w-[200px] mlg:w-[343px] h-full rotate-[-20deg]" src={Phone1.src} alt="" />
                                <img className="w-[200px] mlg:w-[343px] h-full rotate-[-20deg]" src={Phone2.src} alt="" />
                                <img className="w-[200px] mlg:w-[343px] h-full rotate-[-20deg]" src={Phone3.src} alt="" />
                            </div>
                        </div>
                    </motion.div>
                    <div className="rounded-csm absolute bg-white/85 w-[350px] h-[85px] p-1 top-[45%] left-[20%] lg:top-[68%] lg:left-[-10%] shadow-stake rotate-[-20deg]">
                        <div className="relative flex gap-x-2 items-center rounded-csm bg-mint-50 p-2 w-full h-full">
                            <svg width="48" height="24" viewBox="0 0 48 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.3386 14.2694C20.3386 16.8492 19.3162 19.3234 17.4962 21.1476C15.6763 22.9718 13.2079 23.9966 10.6341 23.9966C8.06037 23.9966 5.592 22.9718 3.77206 21.1476C1.95212 19.3234 0.929688 16.8492 0.929688 14.2694L0.929688 0.845967C0.945298 0.626796 1.03922 0.420614 1.19423 0.265242C1.34924 0.10987 1.55494 0.0157264 1.7736 7.91782e-05H5.57065C5.6819 -0.00145697 5.79233 0.0193733 5.89541 0.0613392C5.99849 0.103305 6.09214 0.165555 6.17081 0.244411C6.24948 0.323267 6.31159 0.417129 6.35345 0.520451C6.39532 0.623773 6.4161 0.734458 6.41457 0.845967V14.2694C6.42974 15.3875 6.88492 16.4542 7.68101 17.2375C8.07287 17.6163 8.53537 17.9141 9.04209 18.1137C9.54881 18.3133 10.0898 18.4108 10.6341 18.4007C11.1785 18.4108 11.7195 18.3133 12.2262 18.1137C12.7329 17.9141 13.1954 17.6163 13.5873 17.2375C14.3834 16.4542 14.8386 15.3875 14.8537 14.2694V0.845967C14.8522 0.734556 14.8729 0.623965 14.9147 0.52072C14.9565 0.417475 15.0185 0.323665 15.0971 0.244825C15.1756 0.165984 15.2692 0.10371 15.3721 0.0616755C15.475 0.0196407 15.5854 -0.00130318 15.6965 7.91782e-05H19.4936C19.6048 -0.00145697 19.7152 0.0193733 19.8183 0.0613392C19.9214 0.103305 20.015 0.165555 20.0937 0.244411C20.1724 0.323267 20.2345 0.417129 20.2764 0.520451C20.3182 0.623773 20.339 0.734458 20.3375 0.845967L20.3386 14.2694Z" fill="#71E5BD"></path><path d="M38.3749 17.9737C37.374 18.5285 36.2489 18.8195 35.1052 18.8195C33.9614 18.8195 32.8363 18.5285 31.8354 17.9737C30.7773 17.392 29.8955 16.5349 29.2829 15.4928C28.6704 14.4506 28.3498 13.262 28.355 12.0525C28.355 10.2299 29.0773 8.48193 30.3631 7.19315C31.6488 5.90438 33.3927 5.18035 35.2111 5.18035C37.0294 5.18035 38.7733 5.90438 40.0591 7.19315C41.3448 8.48193 42.0672 10.2299 42.0672 12.0525C42.0093 13.2738 41.6394 14.4598 40.9928 15.4966C40.3463 16.5334 39.4448 17.3864 38.3749 17.9737ZM35.1052 6.61337e-06C32.7477 6.61337e-06 30.4431 0.700712 28.483 2.01351C26.5228 3.32632 24.995 5.19225 24.0929 7.37536C23.1907 9.55847 22.9547 11.9607 23.4146 14.2783C23.8745 16.5959 25.0097 18.7247 26.6767 20.3956C28.3437 22.0665 30.4676 23.2043 32.7798 23.6653C35.0919 24.1263 37.4886 23.8897 39.6666 22.9855C41.8446 22.0812 43.7062 20.5498 45.0159 18.5851C46.3257 16.6204 47.0248 14.3104 47.0248 11.9475C47.0264 10.378 46.7192 8.82369 46.1208 7.37341C45.5224 5.92313 44.6444 4.6054 43.5373 3.49565C42.4301 2.3859 41.1154 1.50592 39.6685 0.906087C38.2216 0.306257 36.6709 -0.00164549 35.1052 6.61337e-06Z" fill="#101010"></path></svg>
                            <div className="flex flex-col gap-y-1 text-black-100">
                                <p className="uppercase text-base font-bold"><span className="text-mint-300">u</span>own</p>
                                <p className="text-base">You’ve made a profit of £200</p>
                            </div>
                        </div>
                    </div>
                    <div className="hidden lg:flex flex-col gap-y-2 rounded-cmd absolute bg-white w-[200px] p-3 top-[60px] lg:top-[5%] lg:left-[30%] shadow-stake rotate-[-20deg]">
                        <img className="rounded-cmd" src={S.src} alt="" />
                        <p className="text-xl text-black-100">Roomzzz Glasgow</p>
                        <p className="text-2xl font-bold text-mint-500">+12.3%</p>
                    </div>
                </motion.div>
            </motion.div>
            <div className="bg-gradient-to-b from-[rgba(255,255,255,0)] to-[rgb(255,255,255)] absolute w-full h-[156px] z-1 bottom-0"></div>
        </section>
    )

}

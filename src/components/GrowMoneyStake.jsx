'use client'
import { motion } from 'motion/react';

import Prop1 from '../assets/images/stake-page/prop-1.png';
import Prop2 from '../assets/images/stake-page/prop-2.png';
import Prop3 from '../assets/images/stake-page/prop-3.png';
import Prop1Sm from '../assets/images/stake-page/prop-1-sm.png';
import Prop2Sm from '../assets/images/stake-page/prop-2-sm.png';
import Prop3Sm from '../assets/images/stake-page/prop-3-sm.png';
import GreenG from '../assets/images/stake-page/green-glow.avif';
import YellowG from '../assets/images/stake-page/yellow-glow.avif';


export default function GrowMoneyStake() {
    return (
        <section className="relative py-24 overflow-hidden">
            <div className="relative mx-auto max-w-7xl overflow-visible">
                <div className="max-w-2xl text-black-100 flex flex-col items-center justify-center text-center gap-y-1.5 mx-auto pb-12">
                    <p className="text-sm lxl:text-base text-mint-500">It’s your money, grow it</p>
                    <h2 className="text-4xl lxl:text-5xl pb-4">So, how do I make money?</h2>
                    <p className="text-2xl lxl:text-3xl">Join <span className="text-mint-500">18K+</span> other real estate </p>
                    <p className="text-2xl lxl:text-3xl">investors making <span className="text-mint-500">12.48%</span> on average</p>
                </div>

                <img className="absolute z-0 right-[-50%] top-0 lg:top-[-10%] opacity-40" src={GreenG.src} alt="" />
                <img className="absolute z-0 left-[-50%] top-[65%] opacity-50" src={YellowG.src} alt="" />

                <div className="flex flex-col mlg:flex-row items-center justify-center gap-x-10">
                    <div className="relative hidden mlg:flex shrink-0 justify-center w-[323px] h-[373px] lg:h-[654px] lg:w-[508px]">
                        <div className="absolute z-0 top-[17%] bg-mint-300 rounded-full w-full h-[328px] lg:h-[516px]"></div>
                        <motion.img initial={{ opacity: 0, x: -70 }}
                            whileInView={{ opacity: 1, x: 0, threshold: 0.99 }}
                            transition={{ duration: 0.5, ease: "easeIn" }} className="w-[195px] h-[396px] lg:h-[600px] lg:w-[300px] z-10" src={Prop1.src} alt="" />
                    </div>
                    <div className="px-12 py-12 relative flex mlg:hidden">
                        <img src={Prop1Sm.src} alt="" />
                    </div>
                    <div className="flex flex-col gap-y-6 px-4">
                        <p className="text-3xl lxl:text-4xl">Earn consistent <br className="hidden mlg:block" /> passive income</p>
                        <p className="text-base lxl:text-lg text-gray-700">Build new income streams with monthly payments <br className="hidden mlg:block" /> from income generating properties and funds</p>
                        <div className="flex justify-start gap-x-12 smd:gap-x-20 mlg:gap-x-8">
                            <div className="flex flex-col">
                                <p className="text-2xl lxl:text-3xl font-semibold">GBP 5M+</p>
                                <p className="text-xs lxl:text-base text-gray-700">Total Returns Paid</p>
                            </div>
                            <div className="flex flex-col">
                                <p className="text-2xl lxl:text-3xl font-semibold">11 <span className="text-mint-500">%</span></p>
                                <p className="text-xs lxl:text-base text-gray-700">Average Annual Return in 2024</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="flex flex-col mlg:flex-row items-center justify-center gap-x-10">
                    <div className="order-2 mlg:order-1 flex flex-col gap-y-6  px-4">
                        <p className="text-3xl  lxl:text-4xl">Make a difference <br className="hidden mlg:block" /> invest wisely</p>
                        <p className="text-base lxl:text-lg text-gray-700">We're building a better future <br className="hidden mlg:block" /> with high quality housing</p>
                        <div className="flex justify-start gap-x-12 smd:gap-x-20 mlg:gap-x-8">
                            <div className="flex flex-col">
                                <p className="text-2xl lxl:text-3xl font-semibold">200+</p>
                                <p className="text-xs lxl:text-base text-gray-700">New homes delivered</p>
                            </div>
                            <div className="flex flex-col">
                                <p className="text-2xl lxl:text-3xl font-semibold">17.7 <span className="text-mint-500">%</span></p>
                                <p className="text-xs lxl:text-base text-gray-700">Average Project Return</p>
                            </div>
                        </div>
                    </div>
                    <div className="order-1 mlg:order-2 relative hidden mlg:flex shrink-0 justify-center w-[323px] h-[373px] lg:h-[654px] lg:w-[508px]">
                        <div className="absolute z-0 top-[17%] bg-mint-300 rounded-full w-full h-[328px] lg:h-[516px]"></div>
                        <motion.img initial={{ opacity: 0, x: 70 }}
                            whileInView={{ opacity: 1, x: 0, threshold: 0.99 }}
                            transition={{ duration: 0.5, ease: "easeIn" }} className="w-[195px] h-[396px] lg:h-[600px] lg:w-[300px] z-10" src={Prop2.src} alt=""/>
                    </div>
                    <div className="px-12 py-12 relative flex mlg:hidden">
                        <img src={Prop2Sm.src} alt=""/>
                    </div>
                </div>
                <div className="flex flex-col mlg:flex-row items-center justify-center gap-x-10">
                    <div className="relative hidden mlg:flex shrink-0 justify-center w-[323px] h-[373px] lg:h-[654px] lg:w-[508px]">
                        <div className="absolute z-0 top-[17%] bg-mint-300 rounded-full w-full h-[328px] lg:h-[516px]"></div>
                        <motion.img initial={{ opacity: 0, x: -70 }}
                            whileInView={{ opacity: 1, x: 0, threshold: 0.99 }}
                            transition={{ duration: 0.5, ease: "easeIn" }} className="w-[195px] h-[396px] lg:h-[600px] lg:w-[300px] z-10" src={Prop3.src} alt=""/>
                    </div>
                    <div className="px-12 py-12 relative flex mlg:hidden">
                        <img src={Prop3Sm.src} alt=""/>
                    </div>
                    <div className="flex flex-col gap-y-6 px-4">
                        <p className="text-3xl lxl:text-4xl">Earn consistent <br className="hidden mlg:block" /> passive income</p>
                        <p className="text-base lxl:text-lg text-gray-700">Projects pay out upon completion and sale <br className="hidden mlg:block" /> or from refinancing.</p>
                        <div className="flex justify-start gap-x-12 smd:gap-x-20 mlg:gap-x-8">
                            <div className="flex flex-col">
                                <p className="text-2xl lxl:text-3xl font-semibold">15</p>
                                <p className="text-xs lxl:text-base text-gray-700">Projects Completed</p>
                            </div>
                            <div className="flex flex-col">
                                <p className="text-2xl lxl:text-3xl font-semibold">11.6 <span className="text-mint-500">m+</span></p>
                                <p className="text-xs lxl:text-base text-gray-700">Project Value</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )

}

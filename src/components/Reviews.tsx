'use client'

import { useEffect, useMemo, useRef, useState } from 'react'
import clsx from 'clsx'
import { useInView } from 'motion/react'

import { Container } from '../components/Container'

interface Review {
  title: string
  body: string
  author: string
  rating: 1 | 2 | 3 | 4 | 5
}

const reviews: Array<Review> = [
  {
    title: 'This is the best place for returns & top customer service',
    body: 'I have been thoroughly impressed with my experience using UOWN.co. ',
    author: '<PERSON><PERSON> Ayaz',
    rating: 5,
  },
  {
    title: 'I have been investing with UOWN for several years now and they have never let me down.',
    body: 'UOWN allows me to invest in property through an expert team, without the time commitment of doing it all myself.',
    author: '<PERSON>',
    rating: 5,
  },
  {
    title: 'Great service',
    body: 'This company is an active business and this has given me confidence in using the platform for the future.',
    author: '<PERSON>ya<PERSON> L Saw',
    rating: 5,
  },
  {
    title: 'Used Uown for years, great experience',
    body: 'I used uown for 3-4 years, and earned a very solid return. When it came time to withdraw basically all my portfolio, I was able to do so very quickly, with no hassle.',
    author: 'Jasim',
    rating: 5,
  },
  {
    title: 'What a great company',
    body: 'What a great company. Had an issue with one of my bank transfers, it was sorted out by one of the owners of the company.',
    author: '<PERSON> T',
    rating: 5,
  },
  {
    title: 'Great place to invest on properties.',
    body: 'Great place to invest on properties.I have joined them long time and had great returns.',
    author: 'Shamoon Mushtaq',
    rating: 5,
  },
  {
    title: 'Fantastic opportunities',
    body: 'Fantastic opportunities and these guys have always delivered.',
    author: 'Naveen Ahmed',
    rating: 5,
  },
  {
    title: 'Great returns so far.',
    body: 'UOWN is a fantastic place to invest. They offer several properties which allows you to diversify, but also new properties enter their portfolio every couple of months. Well worth it!',
    author: 'Swissmat',
    rating: 5,
  },
  {
    title: "Can't recommend highly enough!",
    body: 'Best kept secret in the investment sector. Nailed on for returns after returns.',
    author: 'Adam Smith',
    rating: 5,
  },
  {
    title: 'Very happy I found this little gem',
    body: "I've made multiple investments through the UOWN platform, and every single time they've delivered what they said they would.",
    author: 'Matt Lord',
    rating: 5,
  },
  {
    title: 'Great investment platform',
    body: 'Great investment platform that delivers excellent returns. Have invested in multiple projects.',
    author: 'S.',
    rating: 5,
  },
  {
    title: 'Very pleased and will continue to invest with UOWN!',
    body: "I’ve invested with UOWN for some years now, and have always been very pleased with both the investments and the service from UOWN.",
    author: 'Joe Dooley',
    rating: 5,
  },
  {
    title: 'Excellent service',
    body: 'The right platform to invest your savings and get value back.',
    author: 'Hugo Shepherd',
    rating: 5,
  },
  {
    title: 'Great company',
    body: 'Great company. Never had any problems with them and got steady returns on my investments. Encouraged my parents and brother to use them, and would recommend them to anyone.',
    author: 'Thomas',
    rating: 5,
  },
]

function StarIcon(props: React.ComponentPropsWithoutRef<'svg'>) {
  return (
    <svg viewBox="0 0 20 20" aria-hidden="true" {...props}>
      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
    </svg>
  )
}

function StarRating({ rating }: { rating: Review['rating'] }) {
  return (
    <div className="flex">
      {[...Array(5).keys()].map((index) => (
        <StarIcon
          key={index}
          className={clsx(
            'h-5 w-5',
            rating > index ? 'fill-mint-500' : 'fill-gray-300',
          )}
        />
      ))}
    </div>
  )
}

function Review({
  title,
  body,
  author,
  rating,
  className,
  ...props
}: Omit<React.ComponentPropsWithoutRef<'figure'>, keyof Review> & Review) {
  let animationDelay = useMemo(() => {
    let possibleAnimationDelays = ['0s', '0.1s', '0.2s', '0.3s', '0.4s', '0.5s']
    return possibleAnimationDelays[
      Math.floor(Math.random() * possibleAnimationDelays.length)
    ]
  }, [])

  return (
    <figure
      className={clsx(
        'animate-fade-in rounded-3xl bg-white p-6 opacity-0 shadow-md rounded-cmd shadow-gray-900/5',
        className,
      )}
      style={{ animationDelay }}
      {...props}
    >
      <blockquote className="text-gray-900">
        <StarRating rating={rating} />
        <p className="mt-4 text-lg font-semibold leading-6 before:content-['“'] after:content-['”']">
          {title}
        </p>
        <p className="mt-3 text-base leading-7">{body}</p>
      </blockquote>
      <figcaption className="mt-3 text-sm text-gray-600 before:content-['–_']">
        {author}
      </figcaption>
    </figure>
  )
}

function splitArray<T>(array: Array<T>, numParts: number) {
  let result: Array<Array<T>> = []
  for (let i = 0; i < array.length; i++) {
    let index = i % numParts
    if (!result[index]) {
      result[index] = []
    }
    result[index].push(array[i])
  }
  return result
}

function ReviewColumn({
  reviews,
  className,
  reviewClassName,
  msPerPixel = 0,
}: {
  reviews: Array<Review>
  className?: string
  reviewClassName?: (reviewIndex: number) => string
  msPerPixel?: number
}) {
  let columnRef = useRef<React.ElementRef<'div'>>(null)
  let [columnHeight, setColumnHeight] = useState(0)
  let duration = `${columnHeight * msPerPixel}ms`

  useEffect(() => {
    if (!columnRef.current) {
      return
    }

    let resizeObserver = new window.ResizeObserver(() => {
      setColumnHeight(columnRef.current?.offsetHeight ?? 0)
    })

    resizeObserver.observe(columnRef.current)

    return () => {
      resizeObserver.disconnect()
    }
  }, [])

  return (
    <div
      ref={columnRef}
      className={clsx('animate-marquee space-y-8 py-4', className)}
      style={{ '--marquee-duration': duration } as React.CSSProperties}
    >
      {reviews.concat(reviews).map((review, reviewIndex) => (
        <Review
          key={reviewIndex}
          aria-hidden={reviewIndex >= reviews.length}
          className={reviewClassName?.(reviewIndex % reviews.length)}
          {...review}
        />
      ))}
    </div>
  )
}

function ReviewGrid() {
  let containerRef = useRef<React.ElementRef<'div'>>(null)
  let isInView = useInView(containerRef, { once: true, amount: 0.4 })
  let columns = splitArray(reviews, 3)
  let column1 = columns[0]
  let column2 = columns[1]
  let column3 = splitArray(columns[2], 2)

  return (
    <div
      ref={containerRef}
      className="relative -mx-4 mt-16 grid h-[49rem] max-h-[150vh] grid-cols-1 items-start gap-8 overflow-hidden px-4 sm:mt-20 md:grid-cols-2 lg:grid-cols-3"
    >
      {isInView && (
        <>
          <ReviewColumn
            reviews={[...column1, ...column3.flat(), ...column2]}
            reviewClassName={(reviewIndex) =>
              clsx(
                reviewIndex >= column1.length + column3[0].length &&
                'md:hidden',
                reviewIndex >= column1.length && 'lg:hidden',
              )
            }
            msPerPixel={10}
          />
          <ReviewColumn
            reviews={[...column2, ...column3[1]]}
            className="hidden md:block"
            reviewClassName={(reviewIndex) =>
              reviewIndex >= column2.length ? 'lg:hidden' : ''
            }
            msPerPixel={15}
          />
          <ReviewColumn
            reviews={column3.flat()}
            className="hidden lg:block"
            msPerPixel={10}
          />
        </>
      )}
      <div className="pointer-events-none absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-gray-10" />
      <div className="pointer-events-none absolute inset-x-0 bottom-0 h-32 bg-gradient-to-t from-gray-10" />
    </div>
  )
}

export function Reviews() {
  return (
    <section
      id="reviews"
      aria-labelledby="reviews-title"
      className="bg-gray-10 pb-16 pt-20 sm:pb-24 sm:pt-32"
    >
      <Container>
        <div className='max-w-3xl mx-auto text-center px-6'>
          <h2 className="font-display text-4xl font-bold tracking-tight text-slate-900">
            See what our customers have to say...
          </h2>
          <p className="mt-4 text-lg tracking-tight text-slate-600">
            We have been delivering fantastic returns for our investors since 2016. With a trustpilot score of 4.9
            we are proud of what we have achieved for our tens of thousands of investors.
          </p>
        </div>
        <ReviewGrid />
      </Container>
    </section>
  )
}

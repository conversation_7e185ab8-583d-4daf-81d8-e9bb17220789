import { StarIcon } from '@heroicons/react/20/solid'

const ReviewContainer = ({ review }) => {
    return (
        <div className="reviewItem">
            <div key={review.id} className="reviewContainer flex flex-col items-start justify-between bg-[#F4F2EF] rounded-cmd p-6 w-[384px] h-[420px]">
                <div className="flex gap-x-0.5 w-[116px] h-[20px]">
                    <StarIcon className="text-yellow-400" />
                    <StarIcon className="text-yellow-400" />
                    <StarIcon className="text-yellow-400" />
                    <StarIcon className="text-yellow-400" />
                    <StarIcon className="text-yellow-400" />
                </div>
                <div className="text-[15px] text-black-50 opacity-80 tracking-normal">
                    <p>{review.reviewLine1}</p>
                    <br />
                    <p>{review.reviewLine2}</p>
                </div>
                <div className="bg-[#dce4ea] relative overflow-hidden w-full !h-[2px]"></div>
                <div>
                    <div className="font-semibold text-xl">{review.author}</div>
                    <div className="text-[15px] text-gray-200">{review.reviewTitle}</div>
                </div>

            </div>
        </div>
    );
};

export default ReviewContainer;
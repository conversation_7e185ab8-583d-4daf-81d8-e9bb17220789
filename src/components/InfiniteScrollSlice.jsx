'use client'
import Card from "../components/CardSlice";
import { animate, motion, useMotionValue } from "motion/react";
import { useEffect, useState } from "react";
import useMeasure from "react-use-measure";

import Bk from "../assets/images/roomzzz-glasgow/logo_booking.png";
import NGI from "../assets/images/roomzzz-glasgow/logo_ngi.png";
import S from "../assets/images/roomzzz-glasgow/logo_scotsman.png";
import BN from "../assets/images/roomzzz-glasgow/logo_business_north.png";
import Tele from "../assets/images/roomzzz-glasgow/logo_telegraph.png";
import Times from "../assets/images/roomzzz-glasgow/logo_times.png";
import GrandDesigns from "../assets/images/roomzzz-glasgow/grand_designs.webp";
import IH from "../assets/images/stake-page/ideal-home.svg";

export default function Home({ bgClass, title }) {
    const images = [Bk, Tele, Times, GrandDesigns, IH, NGI, S, BN];
    const FAST_DURATION = 45;
    const SLOW_DURATION = 75;

    const [duration, setDuration] = useState(FAST_DURATION);
    let [ref, { width }] = useMeasure();

    const xTranslation = useMotionValue(0);

    const [mustFinish, setMustFinish] = useState(false);
    const [rerender, setRerender] = useState(false);

    useEffect(() => {
        let controls;
        let finalPosition = -width / 2 - 8;

        if (mustFinish) {
            controls = animate(xTranslation, [xTranslation.get(), finalPosition], {
                ease: "linear",
                duration: duration * (1 - xTranslation.get() / finalPosition),
                onComplete: () => {
                    setMustFinish(false);
                    setRerender(!rerender);
                },
            });
        } else {
            controls = animate(xTranslation, [0, finalPosition], {
                ease: "linear",
                duration: duration,
                repeat: Infinity,
                repeatType: "loop",
                repeatDelay: 0,
            });
        }

        return controls?.stop;
    }, [rerender, xTranslation, duration, width]);

    return (
        <section className="max-w-7xl mx-auto">
            <div className={bgClass + " rounded-clg m-6 md:m-12 py-6"}>
                <div className="relative py-12 min-h-[300px] overflow-hidden mx-3 mlg:mx-12" style={{ maskImage: 'linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgb(0, 0, 0) 30%, rgb(0, 0, 0) 70%, rgba(0, 0, 0, 0) 100%)' }}>
                    <motion.h2 initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, ease: "easeIn" }} className="text-center text-lg md:text-2xl font-semibold leading-8 text-gray-900 mb-3 md:mb-12">{title}</motion.h2>
                    <motion.div
                        className="absolute left-0 flex gap-x-16"
                        style={{ x: xTranslation }}
                        ref={ref}
                        onHoverStart={() => {
                            setMustFinish(true);
                            setDuration(SLOW_DURATION);
                        }}
                        onHoverEnd={() => {
                            setMustFinish(true);
                            setDuration(FAST_DURATION);
                        }}
                    >
                        {[...images, ...images].map((item, idx) => (
                            <Card image={`${item.src}`} key={idx} />
                        ))}
                    </motion.div>
                </div>
            </div>
        </section>

    );
}
---

interface Props {
	readingTime: any;
    postHref: string;
    title: string;
    sectionType: string;
}

const { readingTime, postHref, title, sectionType } = Astro.props;

const borderClass = 'bg-white flex flex-col justify-between h-[130px] md:h-[168px] lg:h-[200px] p-4 md:p-6 rounded-cmd ' + ((sectionType == 'topic') ? 'shadow-articletile' : 'border-blk');
---

<a class="grow flex flex-col flex-wrap w-full md:w-[306px] lg:w-[389px] mb-6 md:ml-6 text-left transform hover:scale-95 transition duration-300" href={postHref}>
    <div class={borderClass}>
        <p class="lg:text-2xl md:text-xl text-lg tracking-normal font-bold">{title}</p>
        <p class="lg:text-lg md:text-base text-sm tracking-wide font-medium"> {readingTime} min read<span class="pl-3">→</span></p>
    </div>
</a>
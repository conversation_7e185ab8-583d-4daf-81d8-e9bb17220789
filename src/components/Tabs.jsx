import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";

import TodsonHouse from "../assets/images/tabs/todson-house.jpg";
import Bakery from "../assets/images/tabs/the-bakery.jpg";
import VillageStreet from "../assets/images/tabs/village-street.jpg";
import WickerIsland from "../assets/images/tabs/wicker-island.jpg";

const tabs = [
  {
    id: 1,
    name: "Todson House",
    title: "Extension of Luxury Student Living",
    description: "Helped to fund the construction of an additional 41 student apartments for a luxury student living brand. The project raised over £1m and paid an average return of 24% over 26 months.",
    imgurl: TodsonHouse,
    pl: "12 months",
    ar: "£400,000",
    noi: "234",
    mr: "£450,000"
  },
  {
    id: 2,
    name: "The Bakery",
    title: "Twelve city centre apartments in the heart of Sheffield",
    description: "We brought four derelict floors of a historic building back into use with the development of twelve new apartments. Even thought the project was delayed due to covid our investors made our forecasted 15% return on the project.",
    imgurl: Bakery,
    pl: "12 months",
    ar: "£400,000",
    noi: "234",
    mr: "£450,000"
  },
  {
    id: 3,
    name: "Village Street",
    title: "Leadership for easy community access",
    description: "Helped to fund the construction of an additional 41 student apartments for a luxury student living brand. The project raised over £1m and paid an average return of 24% over 26 months.",
    imgurl: VillageStreet,
    pl: "12 months",
    ar: "£400,000",
    noi: "234",
    mr: "£450,000"
  },
  {
    id: 4,
    name: "Wicker Island",
    title: "Design System for easy community access",
    description: "Helped to fund the construction of an additional 41 student apartments for a luxury student living brand. The project raised over £1m and paid an average return of 24% over 26 months.",
    imgurl: WickerIsland,
    pl: "12 months",
    ar: "£400,000",
    noi: "234",
    mr: "£450,000"
  },
];

export default function Tabs() {
  const [selectedTab, setSelectedTab] = useState(tabs[0]);

  return (
    <div className="w-full rounded-csm py-24 px-6 smd:px-12 lxl:px-36 bg-white">
      <nav className="flex justify-between">
        <div className="rounded-full border border-gray-100 px-6 py-2 text-base hidden lg:block ">Project Specifics</div>
        <ul className="flex gap-x-3 md:gap-x-8">
          {tabs.map((item) => (
            <li key={`i_` + item.id} className={item === selectedTab ? "text-black-50 cursor-pointer text-center" : "text-gray-100 cursor-pointer text-center"} onClick={() => setSelectedTab(item)}>
              {`${item.name}`}
              {item === selectedTab ? (<motion.div className="mt-2 h-[2px] bg-black-50 rounded-full" layoutId="underline" />) : (<motion.div className="mt-2 h-[2px] bg-gray-100 rounded-full" layoutId="underline" />)}
            </li>
          ))}
        </ul>
      </nav>
      <main>
        <AnimatePresence mode="wait">
          <motion.div
            key={selectedTab ? selectedTab.title : "empty"}
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -10, opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex flex-col lg:flex-row w-full py-12 smd:py-24 gap-x-6 lxl:gap-x-12 justify-center items-center">
              <div className="flex flex-col gap-y-12 justify-center max-w-xl lg:text-left text-center">
                <div className="text-4xl md:text-5xl lxl:text-6xl">{selectedTab ? selectedTab.title : "empty"}</div>
                <div className="text-xl lxl:text-2xl">{selectedTab ? selectedTab.description : "😋"}</div>
              </div>
              <div>

              </div>
              <img
                src={selectedTab ? selectedTab.imgurl.src : ""}
                alt="image"
                className='object-cover rounded-clg hidden lg:block'
              />
            </div>
            <div className="flex flex-col smd:flex-row gap-x-6 w-full">
              <img
                src={selectedTab ? selectedTab.imgurl.src : ""}
                alt="image"
                className='object-cover rounded-clg flex lg:hidden max-h-[550px] smd:w-[50%] mlg:w-full'
              />
              <div className="w-full flex flex-col lg:flex-row justify-between gap-x-4 gap-y-3 text-lg mlg:text-2xl smd:pt-0 pt-12">
                <div className="flex flex-col gap-y-1 smd:gap-y-2">
                  <p className="text-gray-100">Project Length</p>
                  <p className="text-black-50">{selectedTab ? selectedTab.pl : "0 months"}</p>
                </div>
                <div className="flex flex-col gap-y-1 smd:gap-y-2">
                  <p className="text-gray-100">Amount Raised</p>
                  <p className="text-black-50">{selectedTab ? selectedTab.ar : "0"}</p>
                </div>
                <div className="flex flex-col gap-y-1 smd:gap-y-2">
                  <p className="text-gray-100">Number of Investers</p>
                  <p className="text-black-50">{selectedTab ? selectedTab.noi : "0"}</p>
                </div>
                <div className="flex flex-col gap-y-1 smd:gap-y-2">
                  <p className="text-gray-100">Money Returned</p>
                  <p className="text-black-50">{selectedTab ? selectedTab.mr : "0"}</p>
                </div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </main>
    </div>
  );
}

import { motion, useScroll, useTransform } from 'motion/react'

import { useInView } from 'react-intersection-observer';
import { CheckIcon } from '@heroicons/react/24/outline';

import bg from '../assets/images/slice/hero.png';
import Mobile from '../assets/images/slice/uown-mobile.webp';

export default function HeroSlice() {
    const { scrollY } = useScroll();

    const y = useTransform(scrollY, [0, 500], [0, -100]);

    const [ref] = useInView({
        threshold: 0.5,
        triggerOnce: false
    });

    return (
        <section className='p-6 md:p-10 max-w-7xl mx-auto'>
            <div className='flex flex-col lg:flex-row justify-center items-center gap-x-20 gap-y-10'>
                <motion.div ref={ref} className='relative flex justify-center items-center w-full lg:basis-1/2 max-w-[700px] lg:max-w-[553px] h-[600px] order-2 overflow-hidden'>
                    <div className='absolute lg:max-w-[584px] w-full h-full'>
                        <img loading='lazy' src={bg.src} className='object-cover rounded-clg w-full h-full opacity-60' alt="Background" />
                    </div>
                    <motion.div style={{ y }} initial={{ opacity: 0, y: 0 }}
                        whileInView={{ opacity: 1 }} transition={{ duration: 0.4, ease: "easeIn" }}
                        className='relative w-[261px] h-[520px] md:w-[300px] md:h-full py-20'>
                        <img loading='lazy' src={Mobile.src} className='absolute w-full h-full' alt="Mobile frame" />
                    </motion.div>
                    <motion.div initial={{ opacity: 0, y: 0 }}
                        whileInView={{ opacity: 1 }} transition={{ duration: 0.5, ease: "easeIn" }} className='absolute w-[280px] h-[40px] smd:w-[320px] smd:h-[57px] bottom-[20%] md:right-[2%] md:top-[25%]'>
                        <div className="relative flex gap-x-2 items-center rounded-cmd bg-white shadow-stake py-6 px-1 smd:px-3 w-full h-full">
                            <svg width="30" height="15" viewBox="0 0 48 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.3386 14.2694C20.3386 16.8492 19.3162 19.3234 17.4962 21.1476C15.6763 22.9718 13.2079 23.9966 10.6341 23.9966C8.06037 23.9966 5.592 22.9718 3.77206 21.1476C1.95212 19.3234 0.929688 16.8492 0.929688 14.2694L0.929688 0.845967C0.945298 0.626796 1.03922 0.420614 1.19423 0.265242C1.34924 0.10987 1.55494 0.0157264 1.7736 7.91782e-05H5.57065C5.6819 -0.00145697 5.79233 0.0193733 5.89541 0.0613392C5.99849 0.103305 6.09214 0.165555 6.17081 0.244411C6.24948 0.323267 6.31159 0.417129 6.35345 0.520451C6.39532 0.623773 6.4161 0.734458 6.41457 0.845967V14.2694C6.42974 15.3875 6.88492 16.4542 7.68101 17.2375C8.07287 17.6163 8.53537 17.9141 9.04209 18.1137C9.54881 18.3133 10.0898 18.4108 10.6341 18.4007C11.1785 18.4108 11.7195 18.3133 12.2262 18.1137C12.7329 17.9141 13.1954 17.6163 13.5873 17.2375C14.3834 16.4542 14.8386 15.3875 14.8537 14.2694V0.845967C14.8522 0.734556 14.8729 0.623965 14.9147 0.52072C14.9565 0.417475 15.0185 0.323665 15.0971 0.244825C15.1756 0.165984 15.2692 0.10371 15.3721 0.0616755C15.475 0.0196407 15.5854 -0.00130318 15.6965 7.91782e-05H19.4936C19.6048 -0.00145697 19.7152 0.0193733 19.8183 0.0613392C19.9214 0.103305 20.015 0.165555 20.0937 0.244411C20.1724 0.323267 20.2345 0.417129 20.2764 0.520451C20.3182 0.623773 20.339 0.734458 20.3375 0.845967L20.3386 14.2694Z" fill="#71E5BD"></path><path d="M38.3749 17.9737C37.374 18.5285 36.2489 18.8195 35.1052 18.8195C33.9614 18.8195 32.8363 18.5285 31.8354 17.9737C30.7773 17.392 29.8955 16.5349 29.2829 15.4928C28.6704 14.4506 28.3498 13.262 28.355 12.0525C28.355 10.2299 29.0773 8.48193 30.3631 7.19315C31.6488 5.90438 33.3927 5.18035 35.2111 5.18035C37.0294 5.18035 38.7733 5.90438 40.0591 7.19315C41.3448 8.48193 42.0672 10.2299 42.0672 12.0525C42.0093 13.2738 41.6394 14.4598 40.9928 15.4966C40.3463 16.5334 39.4448 17.3864 38.3749 17.9737ZM35.1052 6.61337e-06C32.7477 6.61337e-06 30.4431 0.700712 28.483 2.01351C26.5228 3.32632 24.995 5.19225 24.0929 7.37536C23.1907 9.55847 22.9547 11.9607 23.4146 14.2783C23.8745 16.5959 25.0097 18.7247 26.6767 20.3956C28.3437 22.0665 30.4676 23.2043 32.7798 23.6653C35.0919 24.1263 37.4886 23.8897 39.6666 22.9855C41.8446 22.0812 43.7062 20.5498 45.0159 18.5851C46.3257 16.6204 47.0248 14.3104 47.0248 11.9475C47.0264 10.378 46.7192 8.82369 46.1208 7.37341C45.5224 5.92313 44.6444 4.6054 43.5373 3.49565C42.4301 2.3859 41.1154 1.50592 39.6685 0.906087C38.2216 0.306257 36.6709 -0.00164549 35.1052 6.61337e-06Z" fill="#101010"></path></svg>
                            <div className="w-full flex flex-col gap-y-0.5 text-black-100">
                                <p className="flex justify-between uppercase text-xs smd:text-sm"><span className="font-bold">uown</span><span className="text-gray-400">4:10 PM</span></p>
                                <p className="text-xs smd:text-sm">It's pay day! You just recieved £45.68</p>
                            </div>
                        </div>
                    </motion.div>
                    <motion.div initial={{ opacity: 0, y: 0 }}
                        whileInView={{ opacity: 1 }} transition={{ duration: 0.6, ease: "easeIn" }} className='absolute hidden md:block smd:w-[340px] smd:h-[57px] left-[2%] top-[65%]'>
                        <div className="relative flex gap-x-2 items-center rounded-cmd bg-white shadow-stake py-6 px-1 smd:px-3 w-full h-full">
                            <svg width="30" height="15" viewBox="0 0 48 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.3386 14.2694C20.3386 16.8492 19.3162 19.3234 17.4962 21.1476C15.6763 22.9718 13.2079 23.9966 10.6341 23.9966C8.06037 23.9966 5.592 22.9718 3.77206 21.1476C1.95212 19.3234 0.929688 16.8492 0.929688 14.2694L0.929688 0.845967C0.945298 0.626796 1.03922 0.420614 1.19423 0.265242C1.34924 0.10987 1.55494 0.0157264 1.7736 7.91782e-05H5.57065C5.6819 -0.00145697 5.79233 0.0193733 5.89541 0.0613392C5.99849 0.103305 6.09214 0.165555 6.17081 0.244411C6.24948 0.323267 6.31159 0.417129 6.35345 0.520451C6.39532 0.623773 6.4161 0.734458 6.41457 0.845967V14.2694C6.42974 15.3875 6.88492 16.4542 7.68101 17.2375C8.07287 17.6163 8.53537 17.9141 9.04209 18.1137C9.54881 18.3133 10.0898 18.4108 10.6341 18.4007C11.1785 18.4108 11.7195 18.3133 12.2262 18.1137C12.7329 17.9141 13.1954 17.6163 13.5873 17.2375C14.3834 16.4542 14.8386 15.3875 14.8537 14.2694V0.845967C14.8522 0.734556 14.8729 0.623965 14.9147 0.52072C14.9565 0.417475 15.0185 0.323665 15.0971 0.244825C15.1756 0.165984 15.2692 0.10371 15.3721 0.0616755C15.475 0.0196407 15.5854 -0.00130318 15.6965 7.91782e-05H19.4936C19.6048 -0.00145697 19.7152 0.0193733 19.8183 0.0613392C19.9214 0.103305 20.015 0.165555 20.0937 0.244411C20.1724 0.323267 20.2345 0.417129 20.2764 0.520451C20.3182 0.623773 20.339 0.734458 20.3375 0.845967L20.3386 14.2694Z" fill="#71E5BD"></path><path d="M38.3749 17.9737C37.374 18.5285 36.2489 18.8195 35.1052 18.8195C33.9614 18.8195 32.8363 18.5285 31.8354 17.9737C30.7773 17.392 29.8955 16.5349 29.2829 15.4928C28.6704 14.4506 28.3498 13.262 28.355 12.0525C28.355 10.2299 29.0773 8.48193 30.3631 7.19315C31.6488 5.90438 33.3927 5.18035 35.2111 5.18035C37.0294 5.18035 38.7733 5.90438 40.0591 7.19315C41.3448 8.48193 42.0672 10.2299 42.0672 12.0525C42.0093 13.2738 41.6394 14.4598 40.9928 15.4966C40.3463 16.5334 39.4448 17.3864 38.3749 17.9737ZM35.1052 6.61337e-06C32.7477 6.61337e-06 30.4431 0.700712 28.483 2.01351C26.5228 3.32632 24.995 5.19225 24.0929 7.37536C23.1907 9.55847 22.9547 11.9607 23.4146 14.2783C23.8745 16.5959 25.0097 18.7247 26.6767 20.3956C28.3437 22.0665 30.4676 23.2043 32.7798 23.6653C35.0919 24.1263 37.4886 23.8897 39.6666 22.9855C41.8446 22.0812 43.7062 20.5498 45.0159 18.5851C46.3257 16.6204 47.0248 14.3104 47.0248 11.9475C47.0264 10.378 46.7192 8.82369 46.1208 7.37341C45.5224 5.92313 44.6444 4.6054 43.5373 3.49565C42.4301 2.3859 41.1154 1.50592 39.6685 0.906087C38.2216 0.306257 36.6709 -0.00164549 35.1052 6.61337e-06Z" fill="#101010"></path></svg>
                            <div className="w-full flex flex-col gap-y-0.5 text-black-100">
                                <p className="flex justify-between uppercase text-xs smd:text-sm"><span className="font-bold">uown</span><span className="text-gray-400">9:41 AM</span></p>
                                <p className="text-xs smd:text-sm">We've added a new project. Check it out.</p>
                            </div>
                        </div>
                    </motion.div>
                </motion.div>
                <div className='mlg:order-1 flex flex-col mlg:basis-1/2 pt-10 gap-y-6'>
                    <motion.h2 initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                        transition={{ duration: 0.4, ease: "easeIn" }} className="text-4xl md:text-5xl mlg:text-6xl text-black pb-6">
                        <span className="anchor-text">Earn</span> from property, without owning a house
                    </motion.h2>
                    <motion.div initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                        transition={{ duration: 0.5, ease: "easeIn" }} className='text-lg flex items-center'>
                        <div className='flex items-center shrink-0 justify-center font-bold bg-yellow-200 w-10 h-10 text-base rounded-full mr-3'>
                            <CheckIcon className="h-6 w-6 text-black-100" aria-hidden="true" />
                        </div>
                        Average return on investment of 17%
                    </motion.div>
                    <motion.div initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                        transition={{ duration: 0.5, ease: "easeIn" }} className='text-lg flex items-center'>
                        <div className='flex items-center shrink-0 justify-center font-bold bg-yellow-200 w-10 h-10 text-base rounded-full mr-3'>
                            <CheckIcon className="h-6 w-6 text-black-100" aria-hidden="true" />
                        </div>
                        Monthly dividends on selected projects
                    </motion.div>
                    <motion.div initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                        transition={{ duration: 0.5, ease: "easeIn" }} className='text-lg flex items-center'>
                        <div className='flex items-center shrink-0 justify-center font-bold bg-yellow-200 w-10 h-10 text-base rounded-full mr-3'>
                            <CheckIcon className="h-6 w-6 text-black-100" aria-hidden="true" />
                        </div>
                        Quality investments hand picked by experts
                    </motion.div>
                    <motion.button initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                        transition={{ duration: 0.6, ease: "easeIn" }} type="button" className="mt-10 btn-black max-w-[200px] lxl:text-xl rounded-full px-4 py-2.5 text-lg font-bold text-white shadow-sm transition duration-150 ease-in hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-mint-300">Get Started</motion.button>
                </div>
            </div>
        </section>
    )
}
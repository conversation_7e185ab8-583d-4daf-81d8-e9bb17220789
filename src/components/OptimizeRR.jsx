import { motion } from 'motion/react'

export default function OptimizeRR() {
    return (
        <section className="max-w-7xl mx-auto">
            <div className="flex flex-col justify-center items-center border border-dashed border-[#e9e9e9] p-10 m-6 rounded-clg">
                <div className='flex flex-col mlg:flex-row gap-x-6 items-center pb-20'>
                    <div className='flex flex-col justify-left items-left text-left gap-y-6 pb-6'>
                        <motion.div initial={{ opacity: 0, y: 50 }}
                            whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                            transition={{ duration: 0.3, ease: "easeIn" }} className='text-sm font-bold tracking-widest uppercase px-2.5 py-2 bg-mint-100 text-black-50 rounded-full w-fit'>Savings</motion.div>
                        <motion.h2 initial={{ opacity: 0, y: 50 }}
                            whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                            transition={{ duration: 0.4, ease: "easeIn" }} className="text-2xl md:text-5xl lxl:text-6xl text-black">
                            <span className='text-mint-500'>Optimise your returns</span> and get more from your investment
                        </motion.h2>
                    </div>
                    <motion.div initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                        transition={{ duration: 0.6, ease: "easeIn" }} className='text-base mlg:text-lg lxl:text-xl text-[#737373] mlg:max-w-lg'>
                        Secure premium profits. We seamlessly provide you with the highest returns. Keep up to an extra 17.6% after taxes. Some of your earnings could be exempt from local and city taxes.
                    </motion.div>
                </div>
                <div className='border border-dashed w-full border-[#e9e9e9]'></div>
                <div className='grid grid-cols-1 mlg:grid-cols-3 text-left gap-10 max-w-[1100px] pt-6'>
                    <motion.div initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                        transition={{ duration: 0.8, ease: "easeIn" }} className='flex flex-col gap-y-4 p-6 text-center'>
                        <p className='text-6xl'>£70m+</p>
                        <p className='text-base lg:text-lg text-[#737373]'>We have built more than £70m worth of property over the last 10 years. Delivering results for our investors.</p>
                    </motion.div>
                    <motion.div initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                        transition={{ duration: 0.8, ease: "easeIn" }} className='flex flex-col gap-y-4 p-6 text-center'>
                        <p className='text-6xl'>17.7%</p>
                        <p className='text-base  lg:text-lg text-[#737373]'>Earn an average of 17.7% on your investments.
                            This is our historical average return per project.</p>
                    </motion.div>
                    <motion.div initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                        transition={{ duration: 0.8, ease: "easeIn" }} className='flex flex-col gap-y-4 p-6 text-center'>
                        <p className='text-6xl'>2%</p>
                        <p className='text-base  lg:text-lg text-[#737373]'>We charge you a clear 2% fee and take none of your profits, menaing you keep more of your money.</p>
                    </motion.div>
                </div>
            </div>
        </section>
    )
}

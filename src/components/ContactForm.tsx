import { useState, type ChangeEvent } from "react";

import DialogOverlay from "../components/Dialog.jsx";

import LoadingState from "../assets/images/contact/three-dots.svg";
import CheckMark from "../assets/images/contact/check-1.svg";
import Cross from "../assets/images/contact/times-solid.svg";
import ReactDOM from "react-dom";
import { createRoot } from "react-dom/client";

export default function ContactForm() {

  const [formData, setFormData] = useState({ name: "", email: "", message: "" });

  const delay = (ms: number | undefined) => new Promise(
    resolve => setTimeout(resolve, ms)
  );

  const handleChange = (e: ChangeEvent<HTMLInputElement> | ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prevFormData) => ({ ...prevFormData, [name]: value }));
  };

  const submit = async (e: React.FormEvent<HTMLFormElement>) => {

    function showErrorDialog() {
      var dialogDiv = document.getElementById('dialog-container');
      if (dialogDiv) {
        const root = createRoot(dialogDiv);
        root.render(<DialogOverlay
          heading="Submission failed"
          content="Oops... Something has gone wrong. Please try again."
          button="Close"
          classes="bg-salmon-200 text-black hover:bg-salmon-300 focus-visible:outline-salmon-300"
          type="error"
        />);
      }
    }

    function showSuccessDialog() {
      var dialogDiv = document.getElementById('dialog-container');
      if (dialogDiv) {
        const root = createRoot(dialogDiv);
        root.render(<DialogOverlay
          heading="Submission Successful"
          content="Our team will get back to you soon!"
          button="Close"
          classes="bg-mint-300 text-black hover:bg-mint-500 focus-visible:outline-mint-500"
        />);
      }
    }

    e.preventDefault();
    const submitbtn = document.getElementById("submit");
    const loading = document.getElementById("loading");

    if (submitbtn && loading) {
      submitbtn.style.display = "none";
      loading.style.display = "flex";
    }

    const formData = new FormData(e.target as HTMLFormElement);
    const response = await fetch("/api/feedback", {
      method: "POST",
      body: formData,
    });

    const data = await response.json();

    if (data.message === "success" && loading && submitbtn) {
      loading.style.display = "none";
      await delay(1000);
      submitbtn.style.display = "inline";
      showSuccessDialog();


    } else if (data.message === "fail" && loading && submitbtn) {
      loading.style.display = "none";
      await delay(1000);
      submitbtn.style.display = "inline";
      showErrorDialog();

    }
  }

  return (
    <form method="POST" onSubmit={submit} id="contact-form" className="form max-w-6xl mx-auto grid gap-x-6 gap-y-8 grid-cols-1 lg:grid-cols-2 mt-16 px-8 lg:px-8 md:px-28 z-10 relative">
      <input
        type="text"
        id="name"
        name="name"
        value={formData.name}
        onChange={handleChange}
        placeholder="Name"
        required
        className="flex flex-col w-full text-lg py-3.5 px-8 border border-gray-300 placeholder-gray-500 shadow-sm focus:border-mint-200 focus:ring-mint-300 text-black-s1 rounded-full"
      />
      <input
        type="email"
        placeholder="Email"
        name="email"
        value={formData.email}
        onChange={handleChange}
        required
        id="email"
        className="flex flex-col w-full z-2 text-lg py-3.5 px-8 border border-gray-300 placeholder-gray-500 shadow-sm focus:border-mint-200 focus:ring-mint-300 text-black-s1 rounded-full"
      />
      <textarea
        cols={20}
        rows={5}
        placeholder="Enter message"
        name="message"
        value={formData.message}
        onChange={handleChange}
        required
        id="message"
        className="flex flex-col w-full h-[26rem] z-2 message-contact-form rounded-cmd text-lg py-3.5 px-8 border border-gray-300 placeholder-gray-500 shadow-sm focus:border-mint-200 focus:ring-mint-300 text-black-s1"
      ></textarea>
      <div className="flex flex-col w-full submit-contact-form items-center">
        <button type="submit" className="btn-black-1 btn-contact-submit rounded-full px-2.5 py-1 text-lg font-bold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2">
          <span id="submit" className="text-submit">Submit</span>
          <span id="loading" className="flex justify-center items-center loading-state hidden"><img src={LoadingState.src} alt="" className="absolute w-14" /></span>
          <span id="checkmark" className="flex justify-center items-center checkmark hidden"><img src={CheckMark.src} alt="" className="absolute w-6" /></span>
          <span id="cross" className="flex justify-center items-center cross hidden"><img src={Cross.src} alt="" className="absolute w-6" /></span>
        </button>
        <div id="dialog-container">
        </div>
      </div>
    </form>
  );
}
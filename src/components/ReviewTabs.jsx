import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";

const tabs = [
    {
        id: 1,
        name: "<PERSON>",
        description: "It’s really easy to use and understand and you can see all the pictures of the houses and even the floor plans. So I could see exactly what I was investing into and I could see how much it was funded and the fact that it doesn’t have to be fully funded before you generate an income is really good and I think quite unique.",
        videourl: 'https://www.youtube.com/embed/-etXv_UPr74?si=CW_UxRyA205dCJ_K',
    },
    {
        id: 2,
        name: "<PERSON>",
        description: "Really easy to use website. Money comes through very quickly and if you put some money in, you tend to get – you start seeing the rental income coming through within like a month or two whereas other sites I’ve used, six months until I started seeing any return and then they’ve got the amount wrong.",
        videourl: 'https://www.youtube.com/embed/LYzxcm5nw00?si=U2b-xXU5UiKT2GA2',
    },
    {
        id: 3,
        name: "<PERSON><PERSON>",
        description: "This is so much more simple. I don’t have to worry about tenants. I don’t have to worry about maintenance, insurance. It’s all done for me. All I need to worry about is the income that’s being generated.",
        videourl: 'https://www.youtube.com/embed/oP8F_vP-h-k',
    },
];

export default function ReviewTabs() {
    const [selectedTab, setSelectedTab] = useState(tabs[0]);

    return (
        <div className="max-w-7xl mx-auto rounded-csm pt-24 px-6 smd:px-12 bg-white">
            <div className="max-w-2xl text-black-100 flex flex-col items-center justify-center text-center gap-y-1.5 mx-auto pb-12">
                <p className="text-sm text-mint-500">Success Stories</p>
                <h2 className="text-5xl font-bold pb-4">Hear from our global investors</h2>
            </div>
            <nav className="flex w-full max-w-5xl mx-auto">
                <ul className="w-full flex">
                    {tabs.map((item) => (
                        <li key={`i_` + item.id} className={item === selectedTab ? "w-full text-black-50 cursor-pointer text-center text-base md:text-xl font-bold" : "w-full text-black-50 cursor-pointer text-center  text-base md:text-xl font-bold"} onClick={() => setSelectedTab(item)}>
                            {item === selectedTab ? (<motion.div className="w-full mb-8 h-[2px] bg-mint-300 rounded-full" layoutId="underline" />) : (<motion.div className="w-full mb-8 h-[2px] bg-gray-100 rounded-full" layoutId="underline" />)}
                            {`${item.name}`}
                        </li>
                    ))}
                </ul>
            </nav>
            <main className="mx-auto">
                <AnimatePresence mode="wait">
                    <motion.div
                        key={selectedTab ? selectedTab.title : "empty"}
                        initial={{ y: 10, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        exit={{ y: -10, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                        className="py-12 smd:py-24 mx-auto"
                    >
                        <div className="lg:h-[300px] lxl:h-[398px] flex flex-col lg:flex-row w-full gap-3 lxl:gap-x-6 justify-center items-center">
                            <iframe className='object-cover rounded-cmd h-[260px] smd:h-[400px] lg:h-full w-full flex lg:basis-1/2' title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerPolicy="strict-origin-when-cross-origin" allowFullScreen
                                src={selectedTab ? selectedTab.videourl : ""}>
                            </iframe>
                            <div className="border border-gray-100 rounded-cmd h-full w-full shrink-0 p-6 flex flex-col lg:basis-1/2 gap-y-6 justify-center lg:text-left text-center">
                                <div>
                                    <svg width="25" height="22" fill="none"><path fillRule="evenodd" clipRule="evenodd" d="M0 10.752v10.284L5.13 15.9l5.131-5.135V.47H0v10.283Zm14.582 0v10.284l5.131-5.135 5.13-5.135V.47h-10.26v10.283Z" fill="#41CE8E"
                                        style={{ "fill": "color(display-p3 .2549 .8078 .5569)", "fillOpacity": "1" }}></path></svg>
                                </div>
                                <div className="text-[#0d635e] text-lg lxl:text-xl pt-6">{selectedTab ? selectedTab.description : "😋"}</div>
                            </div>
                        </div>
                    </motion.div>
                </AnimatePresence>
            </main>
        </div>
    );
}

import { Button } from './ButtonA.jsx'
import { Container } from './ContainerA.jsx'
import { Pattern } from './Pattern.jsx'
import { motion } from 'motion/react'

import DialogOverlay from "./Dialog.jsx";

import { useState } from 'react'
import { createRoot } from "react-dom/client";
import 'react-phone-number-input/style.css'
import PhoneInput, { isPossiblePhoneNumber } from 'react-phone-number-input'

import PostHogNode from "../lib/posthog-node.js";

export function FreeChapters({ distinctId, variant, fbc }) {

  const [formData, setFormData] = useState({ firstName: "", lastName: "", email: "", phoneNo: "" });

  const delay = (ms) => new Promise(
    resolve => setTimeout(resolve, ms)
  );

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevFormData) => ({ ...prevFormData, [name]: value }));
  };

  var allowSubmit = true;
  const submit = async (e) => {
    e.preventDefault();

    grecaptcha.ready(function () {
      grecaptcha
        .execute("6LcoZIYpAAAAAMyNoRMU7qVWJmRde5eFgoWjh8xd", {
          action: "submit",
        })
        .then(function (token) {
          fetch("/recaptcha", {
            method: "POST",
            body: JSON.stringify({ recaptcha: token }),
          })
            .then((response) => response.json())
            .then((gResponse) => {
              console.log(gResponse);
              if (gResponse.success &&
                gResponse.score >= 0.9
              ) {
                if (allowSubmit == false) {
                  return false;
                }
                allowSubmit = false;

                function showErrorDialog(content) {
                  var dialogDiv = document.getElementById('dialog-container');
                  if (dialogDiv) {
                    const root = createRoot(dialogDiv);
                    root.render(<DialogOverlay
                      heading="Oops..."
                      content={content}
                      button="Close"
                      classes="bg-salmon-200 text-black hover:bg-salmon-300 focus-visible:outline-salmon-300"
                      type="error"
                    />);
                  }
                }

                const formData = new FormData(e.target);
                const number = formData.get('phoneNo');
                formData.set('gScore', gResponse.score);

                if (number !== null) {
                  if (!isPossiblePhoneNumber(number)) {
                    showErrorDialog('The phone number you entered is not valid. Please try again');
                  } else {
                    const response = fetch("/api/emailoctopus", {
                      method: "POST",
                      body: formData,
                    });

                    const data = response.json();

                    if (data.message === "success") {
                      delay(1000);
                      window.location.href = '/guide-sent';


                    } else if (data.message === "error") {
                      delay(1000);
                      showErrorDialog("Something went wrong. Please try again.");

                    }
                  }
                }
              } else {
                const formDataFail = new FormData(e.target);
                const firstname = formDataFail.get("firstName");
                const lastName = formDataFail.get("lastName");
                const email = formDataFail.get("email");
                const distinctid = formDataFail.get("distinctid");
                const userAgent = formDataFail.get("userAgent");
                const variant = formDataFail.get("variant");
                const fbc = formDataFail.get("fbc");
                var fbclid = '';

                if (fbc !== '') {
                  const fbcA = fbc.split('.');
                  fbclid = fbcA[3];
                }

                PostHogNode().capture({
                  distinctId: distinctid,
                  event: 'Recaptcha failed',
                  properties: {
                    em: email,
                    first_name: firstname,
                    ln: lastName,
                    distinct_id: distinctid,
                    client_user_agent: userAgent,
                    _fbc: fbc,
                    fbclid: fbclid,
                    recaptcha_score: gResponse.score,
                    '$feature/uk-investment-guide-landing-page-1': variant
                  },
                });
              }
            });
        });
    });
  }

  function PhoneNumber() {
    const [value, setValue] = useState();
    return (
      <PhoneInput
        required
        international
        defaultCountry="GB"
        name="phoneNo"
        id="phone-number"
        value={value}
        className=""
        onChange={setValue} />
    )
  }

  return (
    <section
      id="free-chapters"
      aria-label="Free preview"
      className="scroll-mt-14 bg-mint-500 sm:scroll-mt-32"
    >
      <div className="overflow-hidden lg:relative">
        <Container
          size="md"
          className="relative grid grid-cols-1 items-end gap-y-12 py-20 lg:static lg:grid-cols-2 lg:py-28 xl:py-32"
        >
          <Pattern className="absolute -top-32 left-0 w-full sm:-top-5 sm:left-3/4 sm:ml-8 sm:w-auto md:left-2/3 lg:left-auto lg:right-2 lg:ml-0 xl:left-2/3 xl:right-auto" />
          <div>
            <motion.h2 initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }} className="font-display text-5xl font-extrabold tracking-tight text-white sm:w-3/4 sm:text-6xl md:w-2/3 lg:w-auto">
              Get the free investment guide
            </motion.h2>
            <motion.p initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }} className="mt-4 text-lg tracking-tight text-mint-200">
              Enter your details and we’ll send you your copy of the guide.
            </motion.p>
            <motion.form initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }}
              className="pt-6"
              method="POST"
              onSubmit={submit}
              id="contact-form" >
              <h3 className="text-base font-medium tracking-tight text-white">
                Get the guide straight to your inbox{' '}
                <span aria-hidden="true">&rarr;</span>
              </h3>
              <div className="mt-4 sm:relative sm:flex sm:items-center sm:py-0.5 sm:pr-2.5">
                <div className="relative sm:static sm:flex-auto">
                  <input
                    onChange={handleChange}
                    value={formData.firstName}
                    type="text"
                    id="first-name"
                    name="firstName"
                    required
                    aria-label="First Name"
                    placeholder="First Name"
                    className="peer rounded-full border-0 relative z-10 w-full appearance-none bg-transparent ml-1 px-4 py-2 text-base text-white placeholder:text-white/70 focus:ring-transparent focus:outline-none sm:py-3"
                  />
                  <div className="absolute inset-0 rounded-full border border-white/80 peer-focus:border-mint-300 peer-focus:bg-mint-500 peer-focus:ring-1 peer-focus:ring-mint-300" />
                </div>
              </div>
              <div className="mt-4 sm:relative sm:flex sm:items-center sm:py-0.5 sm:pr-2.5">
                <div className="relative sm:static sm:flex-auto">
                  <input
                    onChange={handleChange}
                    value={formData.lastName}
                    type="text"
                    id="last-name"
                    name="lastName"
                    required
                    aria-label="Last Name"
                    placeholder="Last Name"
                    className="peer rounded-full border-0 relative z-10 w-full appearance-none bg-transparent ml-1 px-4 py-2 text-base text-white placeholder:text-white/70 focus:ring-transparent focus:outline-none sm:py-3"
                  />
                  <div className="absolute inset-0 rounded-full border border-white/80 peer-focus:border-mint-300 peer-focus:bg-mint-500 peer-focus:ring-1 peer-focus:ring-mint-300" />
                </div>
              </div>
              <div className="mt-4 sm:relative sm:flex sm:items-center sm:py-0.5 sm:pr-2.5">
                <div className="relative sm:static sm:flex-auto">
                  <input
                    onChange={handleChange}
                    value={formData.email}
                    type="email"
                    id="email-address"
                    name="email"
                    required
                    aria-label="Email address"
                    placeholder="Email Address"
                    className="peer rounded-full border-0 relative z-10 w-full appearance-none bg-transparent ml-1 px-4 py-2 text-base text-white placeholder:text-white/70 focus:ring-transparent focus:outline-none sm:py-3"
                  />
                  <div className="absolute inset-0 rounded-full border border-white/80 peer-focus:border-mint-300 peer-focus:bg-mint-500 peer-focus:ring-1 peer-focus:ring-mint-300" />
                </div>
              </div>
              <input
                value={distinctId}
                type="text"
                name="distinctid"
                className="hidden"
                readOnly
              />
              <input
                id="userAgent"
                type="text"
                name="userAgent"
                className="hidden"
                readOnly
              />
              <input
                value={variant}
                type="text"
                name="variant"
                className="hidden"
                readOnly
              />
              <input
                value={fbc !== '' ? fbc.value : ''}
                type="text"
                name="fbc"
                className="hidden"
                readOnly
              />
              <div className="mt-4 sm:relative sm:flex sm:items-center sm:py-0.5 sm:pr-2.5">
                <div className="relative sm:static sm:flex-auto">
                  <div className="absolute inset-0 rounded-full border border-white/80 peer-focus:border-mint-300 peer-focus:bg-mint-500 peer-focus:ring-1 peer-focus:ring-mint-300" />
                  {PhoneNumber()}
                </div>
              </div>
              <Button
                type="submit"
                color="white"
                className="submit-btn w-full sm:relative sm:z-10 sm:w-auto sm:flex- mt-4 leading-8"
                data-attr="submit"
                aria-label="Send me the guide"
              >
                Send me the guide
              </Button>
              <div id="dialog-container"></div>
            </motion.form>
          </div>
        </Container>
      </div>
    </section>
  )
}

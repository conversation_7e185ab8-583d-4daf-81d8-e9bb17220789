---
interface Props {
	color: string;
    text: string;
    type ?: 'submit' | 'reset' | 'button';
}

const { color, text, type } = Astro.props;
const className = color + ' rounded-full px-4 py-2.5 text-lg font-bold text-white shadow-sm transition duration-150 ease-in hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-mint-300';
---

<button type={type} class={className} aria-label={text}>{text}</button>

'use client';
import Card from '../components/SlideScreen';
import { useScroll, motion } from 'motion/react';
import { useEffect, useRef } from 'react';
import Lenis from '@studio-freight/lenis'

import Cardiff from "../assets/images/wicker-island/cardiff-main.webp";
import Table from "../assets/images/wicker-island/Setting-Dinner-Table.webp";

const projects = [
  {
    title: "Invest with the UK's leading property investment platform",
    description: "UOWN was created by a leading property group to open the doors to exclusive property deals. Using innovative technology, we make property investment accessible to everyday investors like you.",
    src: Cardiff,
    bullets: [
      "Over five decades of property experience.",
      "Access to deals normally unavailable to ordinary investors.",
      "One of the UK's first property crowdfudning platforms"
    ],
    bulletColor: "#EDEDED",
    checkMarkColor: "#827C6A",
    color: "#F4F2EF"
  },
  {
    title: "Invest your way—online or on the go.",
    description: "Invest with ease through our web platform or on the go with our mobile app. Get started today by connecting with your dedicated investment manager!",
    src: Table,
    bullets: [
      "Access to your own persoanl investment manager.",
      "Invest online or on the go in minutes using our app.",
      "Select the deals that suit your investment needs.",
    ],
    bulletColor: "#F7E79F",
    checkMarkColor: "#FFD008",
    color: "#FFFBEE"
  },
  {
    title: "Suitable for everyone",
    description: "No matter where you are in life, UOWN is here for you. We’ve made property investment accessible to everyone—whether you’re saving for the future or enjoying retirement after years of hard work.",
    src: Cardiff,
    bullets: [
      "Saving for retirement",
      "Saving for a big life event",
      "Just saving for a rainy day"
    ],
    bulletColor: "#A7F9D8",
    checkMarkColor: "#13B688",
    color: "#EEFFF8"
  },
  {
    title: "Invest your way—online or on the go.",
    description: "Invest with ease through our web platform or on the go with our mobile app. Get started today by connecting with your dedicated investment manager!",
    src: Table,
    bullets: [
      "Access to your own persoanl investment manager.",
      "Invest online or on the go in minutes using our app.",
      "Select the deals that suit your investment needs.",
    ],
    bulletColor: "#EEF7FF",
    checkMarkColor: "#5A6ECC",
    color: "#DCE4EA"
  },
]

export default function Home() {
  const container = useRef(null);
  const { scrollYProgress } = useScroll({
    target: container,
    offset: ['start start', 'end end']
  })

  useEffect(() => {
    const lenis = new Lenis()

    function raf(time) {
      lenis.raf(time)
      requestAnimationFrame(raf)
    }

    requestAnimationFrame(raf)
  })

  return (
    <div className="lg:px-28 smd:px-6 px-3">
      <div className="px-3 smd:px-6 slideContainer rounded-b-clg pb-16">
        <motion.div className="flex flex-col items-center justify-center text-center mt-36">
          <h2 className="text-3xl lg:text-4xl tracking- font-bold pb-4">A platform with experience</h2>
          <p className="max-w-lg text-lg tracking-normal pb-12">Our platform has been built by a team with over five decades of experience in the UK property market.  </p>
        </motion.div>
        <div ref={container} className="relative mt-12 lg:max-w-4xl mx-4 md:mx-10 lg:mx-auto">
          {
            projects.map((project, i) => {
              const targetScale = 1 - ((projects.length - i) * 0.05);
              return <Card key={`p_${i}`} i={i} {...project} progress={scrollYProgress} range={[i * .25, 1]} targetScale={targetScale} />
            })
          }
        </div>
        <div className='mt-24 smd:mt-0 lg:mt-8 mx-auto flex justify-center items-center'>
          <a href="#" className="rounded-full bg-black-100 px-5 py-4 text-lg font-light text-white shadow-sm hover:bg-black-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-black-100">
            View All Case Studies
          </a>
        </div>
      </div>
    </div>
  )
}
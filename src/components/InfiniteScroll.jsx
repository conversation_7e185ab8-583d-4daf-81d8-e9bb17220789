'use client'
import Card from "../components/Card";
import { animate, motion, useMotionValue } from "motion/react";
import { useEffect, useState } from "react";
import useMeasure from "react-use-measure";

import Bk from "../assets/images/roomzzz-glasgow/logo_booking.png";
import NGI from "../assets/images/roomzzz-glasgow/logo_ngi.png";
import IN from "../assets/images/roomzzz-glasgow/logo_inews.png";
import S from "../assets/images/roomzzz-glasgow/logo_scotsman.png";
import BN from "../assets/images/roomzzz-glasgow/logo_business_north.png";
import Tele from "../assets/images/roomzzz-glasgow/logo_telegraph.png";
import Times from "../assets/images/roomzzz-glasgow/logo_times.png";
import GrandDesigns from "../assets/images/roomzzz-glasgow/grand_designs.webp";
import IH from "../assets/images/stake-page/ideal-home.svg";

export default function Home() {
  const images = [IH, Bk, Tele, Times, GrandDesigns, NGI, IN, S, BN];
  const FAST_DURATION = 45;
  const SLOW_DURATION = 75;

  const [duration, setDuration] = useState(FAST_DURATION);
  let [ref, { width }] = useMeasure();

  const xTranslation = useMotionValue(0);

  const [mustFinish, setMustFinish] = useState(false);
  const [rerender, setRerender] = useState(false);

  useEffect(() => {
    let controls;
    let finalPosition = -width / 2 - 8;

    if (mustFinish) {
      controls = animate(xTranslation, [xTranslation.get(), finalPosition], {
        ease: "linear",
        duration: duration * (1 - xTranslation.get() / finalPosition),
        onComplete: () => {
          setMustFinish(false);
          setRerender(!rerender);
        },
      });
    } else {
      controls = animate(xTranslation, [0, finalPosition], {
        ease: "linear",
        duration: duration,
        repeat: Infinity,
        repeatType: "loop",
        repeatDelay: 0,
      });
    }

    return controls?.stop;
  }, [rerender, xTranslation, duration, width]);

  return (
    <div className="relative py-8 min-h-[300px] overflow-hidden">
      <motion.h2 initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, ease: "easeIn" }} className="text-center text-3xl font-semibold leading-8 text-gray-900 mb-12">As seen in..</motion.h2>
      <motion.div
        className="absolute left-0 flex gap-20"
        style={{ x: xTranslation }}
        ref={ref}
        onHoverStart={() => {
          setMustFinish(true);
          setDuration(SLOW_DURATION);
        }}
        onHoverEnd={() => {
          setMustFinish(true);
          setDuration(FAST_DURATION);
        }}
      >
        {[...images, ...images].map((item, idx) => (
          <Card image={`${item.src}`} key={idx} />
        ))}
      </motion.div>
    </div>
  );
}
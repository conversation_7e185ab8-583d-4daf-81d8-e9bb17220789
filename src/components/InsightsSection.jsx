'use client'
import { motion } from 'motion/react';
import { ArrowUpRightIcon } from '@heroicons/react/24/outline'

import Cardiff from "../assets/images/wicker-island/cardiff-main.webp";

export default function HeroSection() {
    return (
        <section className="flex flex-col bg-white py-12 md:py-24 px-6 md:px-12 gap-y-6">
            <motion.h1
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                transition={{ duration: 0.3, ease: "easeIn" }}
                className='text-3xl md:text-5xl lg:text-6xl max-w-3xl text-center mx-auto mb-12'>Insights and Updates</motion.h1>
                <div className='grid grid-cols-1 mlg:grid-cols-2 lg:grid-cols-3 gap-4 mx-auto'>
                    <div className='w-full mlg:w-[350px] h-[420px] flex flex-col bg-gray-20 rounded-clg p-6 max-w-2xl gap-y-4'>
                        <div className='relative'>
                            <img src={Cardiff.src} alt="Cardiff" className='w-full h-[200px] object-cover rounded-cmd' />
                            <div className='absolute rounded-full text-sm bg-mint-300 px-4 py-2 top-2 right-2'>Marketing</div>
                        </div>
                        <p className='text-gray-200 text-sm'>Jan 12, 2022</p>
                        <p className='text-2xl'>Maximizing Your Rental Property's Potential</p>
                    </div>
                    <div className='w-full mlg:w-[350px] h-[420px] flex flex-col bg-gray-20 rounded-clg p-6 max-w-2xl gap-y-4'>
                        <div className='relative'>
                            <img src={Cardiff.src} alt="Cardiff" className='w-full h-[200px] object-cover rounded-cmd' />
                            <div className='absolute rounded-full text-sm bg-mint-300 px-4 py-2 top-2 right-2'>Marketing</div>
                        </div>
                        <p className='text-gray-200 text-sm'>Jan 12, 2022</p>
                        <p className='text-2xl'>Maximizing Your Rental Property's Potential</p>
                    </div>
                    <div className='w-full mlg:w-[350px] h-[420px] flex flex-col bg-gray-20 rounded-clg p-6 max-w-2xl gap-y-4'>
                        <div className='relative'>
                            <img src={Cardiff.src} alt="Cardiff" className='w-full h-[200px] object-cover rounded-cmd' />
                            <div className='absolute rounded-full text-sm bg-mint-300 px-4 py-2 top-2 right-2'>Marketing</div>
                        </div>
                        <p className='text-gray-200 text-sm'>Jan 12, 2022</p>
                        <p className='text-2xl'>Maximizing Your Rental Property's Potential</p>
                    </div>
                </div>
            <motion.a initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                transition={{ duration: 0.7, ease: "easeIn" }}
                className='flex items-center justify-center'>
                <span className='py-3 px-6 bg-mint-300 rounded-full text-xl lxl:py-5 lxl:px-10 lxl:text-3xl'>Read More</span>
                <span className='bg-mint-300 rounded-full p-4 lxl:p-7'>
                    <ArrowUpRightIcon className="h-4 w-4 text-black-200" aria-hidden="true" />
                </span>
            </motion.a>
        </section>
    )
}
---
interface Props {
	isIpadHidden: boolean;
}

const { isIpadHidden } = Astro.props;

const sectionClass = "hero h-[88vh] bg-center bg-no-repeat bg-cover flex-col justify-center items-center text-center lg:px-0 px-11 " + ((isIpadHidden == true) ? 'hidden lg:flex' : 'flex');
---

<section
class={sectionClass}
>
<p
    class="max-w-4xl lg:text-7xl text-5xl tracking-normal font-extrabold pb-4"
>
    How can we help?
</p>
<p
    class="max-w-2xl md:text-2xl text-xl tracking-normal font-regular pb-12"
>
    Our Help Centre is built to help answer any questions you might
    have.
</p>
</section>

<style scoped>
    .hero {
        background-image: url("../assets/images/help/help_main_image.jpg");
    }

    /* next size - tablet landscape and it covers desktop */
    @media (max-width: 1024px) {
    .hero {
        background-image: url("../assets/images/help/help_main_image_ipad.jpg");
    }
    }

    /* next size - larger desktops */
    @media (max-width: 640px) {
    .hero {
        background-image: url("../assets/images/help/help_main_image_mobile.jpg");
    }
    }
</style>
import { motion } from 'motion/react'

export default function FAQs() {
    return (
        <section className="max-w-7xl mx-auto px-10 py-20 sm:py-28">
            <div className='flex flex-col justify-center items-center mx-auto text-center gap-y-8 max-w-3xl pb-12'>
                <motion.h2 initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.3, ease: "easeIn" }} className="text-5xl lxl:text-7xl text-black">FAQs</motion.h2>
                <motion.div initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.4, ease: "easeIn" }}>
                    <p className="text-lg lxl:text-xl text-[#737373]">Still got questions? We've got answers. </p>
                    <p className="text-lg lxl:text-xl text-[#737373]">Can't find the answer to your question? Reach out to our team!</p>
                </motion.div>
            </div>
            <div className='grid grid-cols-1 mlg:grid-cols-3 text-left gap-10'>
                <motion.div initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.8, ease: "easeIn" }} className='flex flex-col gap-y-4'>
                    <p className='text-xl'>Are you a regulated entity?</p>
                    <p className='text-base lxl:text-lg text-[#737373]'>Our current business model is not a regulated activity. We are in the process of gaining authorisation with the FCA to enhance our investment offering.</p>
                </motion.div>
                <motion.div initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.8, ease: "easeIn" }} className='flex  flex-col gap-y-4'>
                    <p className='text-xl'>How can you guarantee a 10% yield?</p>
                    <p className='text-base lxl:text-lg text-[#737373]'>We don't guarantee returns unless the project specifically states there is a corporate guarantee from the borrower. Beyond that we only select with most profitable projects. If you want to learn more about our process, contact us.</p>
                </motion.div>
                <motion.div initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.8, ease: "easeIn" }} className='flex  flex-col gap-y-4'>
                    <p className='text-xl'>Can I exit whenever I want?</p>
                    <p className='text-base lxl:text-lg text-[#737373]'>Development projects will exit at the end of the project, which can be delayed. Returns paid as monthly dividends are yours to reinvest or withdraw.</p>
                </motion.div>
                <motion.div initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.8, ease: "easeIn" }} className='flex  flex-col gap-y-4'>
                    <p className='text-xl'>Are there any fees?</p>
                    <p className='text-base lxl:text-lg text-[#737373]'>At UOWN we have one fee: 2% transaction fee - this fee is charged when you invest into any project on UOWN. For example if you invest £50 you will be charged a fee of £1.</p>
                </motion.div>
                <motion.div initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.8, ease: "easeIn" }} className='flex flex-col  gap-y-4'>
                    <p className='text-xl'>In what type of projects do you invest?</p>
                    <p className='text-base lxl:text-lg text-[#737373]'>We invest in development projects, either our own, or from heavily vetted developers that we trust have a track record of success.</p>
                </motion.div>
                <motion.div initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.8, ease: "easeIn" }} className='flex  flex-col gap-y-4'>
                    <p className='text-xl'>What tax implications are there?</p>
                    <p className='text-base lxl:text-lg text-[#737373]'>Every property investment in UOWN is owned by a UK limited company (SPV). Corporation tax is payable on the taxable profit made by each SPV. To see the Corporation Tax rate in the UK: <a href="https://www.gov.uk/government/publications/rates-and-allowances-corporation-tax/rates-and-allowances-corporation-tax">Click here.</a> <br></br>Please note that tax rates are subject to future change, and the above does not constitute tax advice. We do provide any tax advice and if you are not certain then you should consult an accountant.</p>
                </motion.div>
            </div>
        </section>
    )
}

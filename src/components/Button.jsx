import React  from 'react'
import '../styles/button.scss';

const Button = ({text, color, type}) => {
  return (
    <>
      <button
        type={type}
        className={color + " rounded-full px-2.5 py-1 text-lg font-bold text-white shadow-sm transition duration-150 ease-in hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"} aria-label={text}>
        {text}
      </button>
    </>
  );
};
export default Button;
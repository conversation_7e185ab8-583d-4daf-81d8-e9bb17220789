'use client'
import { motion } from 'motion/react';
import { ArrowUpRightIcon, ArrowRightIcon } from '@heroicons/react/24/outline'

import Cardiff from "../assets/images/wicker-island/cardiff-main.webp";

export default function HeroSection({ title, subtitle, btnTxt, imgTxt, isHero }) {
    return (
        <section className="bg-white flex mlg:flex-row flex-col gap-x-3 gap-y-4 py-12 md:py-24 px-6 md:px-12">
            <div className='flex flex-col gap-y-8'>
                <motion.h1 initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.5, ease: "easeIn" }}
                    className={'text-5xl ' + (isHero == 'true' ? 'font-semibold lg:text-7xl lxl:text-8xl' : 'lxl:text-6xl')}>{title}</motion.h1>
                <motion.p initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.6, ease: "easeIn" }}
                    className='lxl:text-2xl text-lg max-w-xl lxl:max-w-4xl'>{subtitle}</motion.p>
                <motion.a initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.7, ease: "easeIn" }}
                    className='flex items-center'><span className='py-3 px-6 bg-mint-300 rounded-full text-xl lxl:py-5 lxl:px-10 lxl:text-3xl'>{btnTxt}</span><span className='bg-mint-300 rounded-full p-4 lxl:p-7'><ArrowUpRightIcon className="h-4 w-4 text-black-200" aria-hidden="true" /></span></motion.a>
                <motion.div initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.8, ease: "easeIn" }}
                    className='flex lg:grow items-end justify-between'>
                    <p className='flex  text-lg lg:text-2xl gap-x-5 items-center'>Your Path Starts Here<ArrowRightIcon className="h-8 w-8 text-black-200 font-bold" aria-hidden="true" /></p>
                    <div className='relative lg:block hidden'>
                        <img src={Cardiff.src} alt='' className="h-[290px] max-w-[300px] rounded-cxxl object-cover" />
                        <div className='absolute text-xl top-4 right-4 p-3 rounded-csm w-[192px]'><mark className='bg-white p-2 mb-2 rounded-t-csm rounded-br-csm leading-10'>Unlock the true</mark><mark className='bg-white p-2 mb-2 rounded-br-csm leading-10'> potential with </mark><mark className='bg-white p-2 mb-2 rounded-b-csm leading-10'>our app</mark></div>
                    </div>
                </motion.div>
            </div>
            <motion.div initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                transition={{ duration: 1, ease: "easeIn" }}
                className='relative'>
                <img src={Cardiff.src} alt='' className="mlg:h-full rounded-cxxl object-cover w-full md:min-w-[400px] mlg:max-w-[500px] lxl:min-w-[500px] lxl:max-w-[700px]" />
                <div className='absolute flex top-4 right-4 md:top-6 md:right-6 p-2 md:p-4 gap-x-4 bg-mint-300 rounded-full text-base md:text-xl '>{imgTxt} <ArrowUpRightIcon className="h-6 w-6" aria-hidden="true" /></div>
                <div className='absolute'></div>
            </motion.div>
        </section>
    )
}

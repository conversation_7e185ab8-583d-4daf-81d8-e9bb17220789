'use client';
import { motion } from 'motion/react';
import React, { useState } from 'react'
import { Dialog, Disclosure } from '@headlessui/react'
import {
  Bars3Icon,
  XMarkIcon,
} from '@heroicons/react/24/outline'
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/20/solid'
import Button from '../components/Button.jsx';

const learn = [
  {
    name: 'The Hub',
    href: '/the-hub'
  },
  {
    name: 'Help',
    href: "/help-centre"
  },
  {
    name: 'Risk',
    href: '/risk-statement'
  },
  {
    name: 'Guides',
    href: '/guides/uk-property-investment-guide'
  },
]

function classNames(...classes) {
  return classes.filter(Boolean).join(' ')
}

export default function Example() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  return (
    <motion.header initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0, duration: 10 }} className=" isolate z-40 bg-white sticky top-0">
      <nav className="mx-auto h-20 flex items-center justify-between px-6 lg:px-8 shadow-nav" aria-label="Global">
        <div className="flex lg:flex-1">
          <a href="/" className="-m-1.5 p-1.5 hidden md:inline-flex lg:inline-flex w-[120px]">
            <span className="sr-only">UOWN</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="131" height="31" viewBox="0 0 131 31" fill="none">
              <path d="M102.029 0.382911H97.2713C97.1319 0.380991 96.9935 0.407029 96.8644 0.459487C96.7352 0.511944 96.6179 0.589757 96.5193 0.688327C96.4207 0.786896 96.3429 0.904224 96.2905 1.03338C96.238 1.16253 96.212 1.30089 96.2139 1.44027V18.2196C96.1949 19.6171 95.6246 20.9506 94.6272 21.9296C93.6214 22.8642 92.2993 23.3836 90.9264 23.3836C89.5535 23.3836 88.2314 22.8642 87.2256 21.9296C86.3112 21.0052 85.7502 19.7892 85.6403 18.4936V1.44027C85.6422 1.30089 85.6162 1.16253 85.5637 1.03338C85.5113 0.904224 85.4335 0.786896 85.3349 0.688327C85.2363 0.589757 85.119 0.511944 84.9898 0.459487C84.8607 0.407029 84.7223 0.380991 84.583 0.382911H79.9568C79.8174 0.380991 79.6791 0.407029 79.5499 0.459487C79.4208 0.511944 79.3035 0.589757 79.2049 0.688327C79.1063 0.786896 79.0285 0.904224 78.976 1.03338C78.9236 1.16253 78.8975 1.30089 78.8995 1.44027V18.4893C78.8887 19.1414 78.7422 19.7841 78.4692 20.3765C78.1963 20.9688 77.8029 21.4978 77.3141 21.9296C76.3084 22.8642 74.9863 23.3836 73.6134 23.3836C72.2405 23.3836 70.9184 22.8642 69.9126 21.9296C68.9157 20.9503 68.3459 19.6169 68.3273 18.2196V1.44027C68.3292 1.30089 68.3032 1.16253 68.2507 1.03338C68.1983 0.904224 68.1205 0.786896 68.0219 0.688327C67.9233 0.589757 67.806 0.511944 67.6768 0.459487C67.5477 0.407029 67.4093 0.380991 67.2699 0.382911H62.5125C62.3732 0.380991 62.2348 0.407029 62.1056 0.459487C61.9765 0.511944 61.8592 0.589757 61.7606 0.688327C61.662 0.786896 61.5842 0.904224 61.5318 1.03338C61.4793 1.16253 61.4533 1.30089 61.4552 1.44027V18.2196C61.4512 19.8174 61.763 21.4003 62.3727 22.8773C62.9823 24.3543 63.8778 25.6962 65.0076 26.8261C66.1375 27.9559 67.4794 28.8514 68.9564 29.461C70.4334 30.0707 72.0163 30.3825 73.6141 30.3785C75.2406 30.3698 76.8492 30.0383 78.3465 29.4031C79.8439 28.768 81.2003 27.8419 82.337 26.6785C83.4737 27.8419 84.83 28.768 86.3274 29.4031C87.8248 30.0383 89.4333 30.3698 91.0598 30.3785C92.6575 30.3823 94.2403 30.0704 95.7171 29.4606C97.1939 28.8509 98.5357 27.9554 99.6654 26.8256C100.795 25.6957 101.69 24.3538 102.3 22.877C102.91 21.4001 103.221 19.8173 103.217 18.2196V1.44027C103.149 1.16449 102.999 0.915788 102.787 0.727011C102.575 0.538233 102.31 0.418253 102.029 0.382911Z" fill="#101010" />
              <path d="M24.3178 18.2196C24.3178 21.4444 23.0368 24.537 20.7566 26.8173C18.4763 29.0975 15.3837 30.3785 12.1589 30.3785C8.93417 30.3785 5.84151 29.0975 3.56127 26.8173C1.28103 24.537 0 21.4444 0 18.2196L0 1.44027C0.019559 1.16631 0.137239 0.90858 0.331454 0.714365C0.525669 0.520151 0.783396 0.40247 1.05736 0.382911H5.81476C5.95415 0.380991 6.09251 0.407029 6.22166 0.459487C6.35081 0.511944 6.46814 0.589757 6.56671 0.688327C6.66528 0.786897 6.74309 0.904224 6.79555 1.03338C6.84801 1.16253 6.87404 1.30089 6.87212 1.44027V18.2196C6.89113 19.6171 7.46143 20.9506 8.45888 21.9296C8.94983 22.4032 9.52931 22.7754 10.1642 23.0249C10.7991 23.2744 11.4769 23.3963 12.1589 23.3837C12.8409 23.3963 13.5188 23.2744 14.1536 23.0249C14.7885 22.7754 15.368 22.4032 15.859 21.9296C16.8564 20.9506 17.4267 19.6171 17.4457 18.2196V1.44027C17.4438 1.30101 17.4698 1.16277 17.5222 1.03371C17.5745 0.904656 17.6522 0.787394 17.7506 0.688843C17.8491 0.590293 17.9662 0.51245 18.0952 0.459907C18.2242 0.407363 18.3624 0.381184 18.5016 0.382911H23.2591C23.3984 0.380991 23.5368 0.407029 23.666 0.459487C23.7951 0.511944 23.9124 0.589757 24.011 0.688327C24.1096 0.786897 24.1874 0.904224 24.2398 1.03338C24.2923 1.16253 24.3183 1.30089 24.3164 1.44027L24.3178 18.2196Z" fill="#71E5BD" />
              <path d="M106.656 12.5417C106.656 9.31699 107.937 6.22432 110.218 3.94408C112.498 1.66384 115.591 0.382813 118.815 0.382812C122.04 0.382813 125.133 1.66384 127.413 3.94408C129.693 6.22432 130.974 9.31699 130.974 12.5417V29.3254C130.976 29.4647 130.95 29.6031 130.898 29.7322C130.845 29.8614 130.767 29.9787 130.669 30.0773C130.57 30.1759 130.453 30.2537 130.324 30.3061C130.195 30.3586 130.056 30.3846 129.917 30.3827H125.158C125.019 30.3846 124.88 30.3586 124.751 30.3061C124.622 30.2537 124.505 30.1759 124.406 30.0773C124.307 29.9787 124.23 29.8614 124.177 29.7322C124.125 29.6031 124.099 29.4647 124.101 29.3254V12.5417C124.065 11.1529 123.497 9.83083 122.514 8.84841C121.532 7.866 120.21 7.29816 118.821 7.26207C118.139 7.24945 117.461 7.37137 116.826 7.62087C116.191 7.87036 115.612 8.24254 115.121 8.71612C114.126 9.69296 113.557 11.0223 113.536 12.4162V29.1941C113.538 29.3335 113.511 29.4718 113.459 29.601C113.407 29.7301 113.329 29.8475 113.23 29.946C113.132 30.0446 113.014 30.1224 112.885 30.1749C112.756 30.2273 112.618 30.2534 112.478 30.2514H107.714C107.574 30.2534 107.436 30.2273 107.307 30.1749C107.178 30.1224 107.06 30.0446 106.962 29.946C106.863 29.8475 106.785 29.7301 106.733 29.601C106.68 29.4718 106.654 29.3335 106.656 29.1941V12.5417Z" fill="#101010" />
              <path d="M46.9178 22.8499C45.6637 23.5434 44.2541 23.9072 42.821 23.9072C41.388 23.9072 39.9784 23.5434 38.7243 22.8499C37.3986 22.1228 36.2938 21.0514 35.5263 19.7488C34.7588 18.4461 34.3571 16.9604 34.3636 15.4484C34.3636 13.1702 35.2686 10.9852 36.8796 9.37425C38.4906 7.76329 40.6755 6.85826 42.9537 6.85826C45.232 6.85826 47.4169 7.76329 49.0279 9.37425C50.6389 10.9852 51.5439 13.1702 51.5439 15.4484C51.4714 16.9751 51.0079 18.4575 50.1978 19.7536C49.3878 21.0496 48.2583 22.1158 46.9178 22.8499ZM42.821 0.382821C39.8673 0.382821 36.9799 1.2587 34.524 2.89971C32.068 4.54071 30.1539 6.87313 29.0235 9.60202C27.8932 12.3309 27.5974 15.3337 28.1737 18.2307C28.7499 21.1276 30.1723 23.7887 32.2609 25.8773C34.3495 27.9659 37.0105 29.3882 39.9075 29.9645C42.8045 30.5407 45.8073 30.245 48.5361 29.1146C51.265 27.9843 53.5975 26.0701 55.2385 23.6142C56.8795 21.1583 57.7553 18.2709 57.7553 15.3171C57.7574 13.3553 57.3725 11.4124 56.6227 9.59958C55.873 7.78673 54.773 6.13956 53.3858 4.75237C51.9986 3.36518 50.3514 2.26521 48.5386 1.51542C46.7257 0.765634 44.7828 0.380756 42.821 0.382821Z" fill="#101010" />
            </svg>
          </a>
          <a href="/" className="-m-1.5 p-1.5 lg:hidden md:hidden">
            <span className="sr-only">UOWN</span>
            <svg width="48" height="24" viewBox="0 0 48 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20.3386 14.2694C20.3386 16.8492 19.3162 19.3234 17.4962 21.1476C15.6763 22.9718 13.2079 23.9966 10.6341 23.9966C8.06037 23.9966 5.592 22.9718 3.77206 21.1476C1.95212 19.3234 0.929688 16.8492 0.929688 14.2694L0.929688 0.845967C0.945298 0.626796 1.03922 0.420614 1.19423 0.265242C1.34924 0.10987 1.55494 0.0157264 1.7736 7.91782e-05H5.57065C5.6819 -0.00145697 5.79233 0.0193733 5.89541 0.0613392C5.99849 0.103305 6.09214 0.165555 6.17081 0.244411C6.24948 0.323267 6.31159 0.417129 6.35345 0.520451C6.39532 0.623773 6.4161 0.734458 6.41457 0.845967V14.2694C6.42974 15.3875 6.88492 16.4542 7.68101 17.2375C8.07287 17.6163 8.53537 17.9141 9.04209 18.1137C9.54881 18.3133 10.0898 18.4108 10.6341 18.4007C11.1785 18.4108 11.7195 18.3133 12.2262 18.1137C12.7329 17.9141 13.1954 17.6163 13.5873 17.2375C14.3834 16.4542 14.8386 15.3875 14.8537 14.2694V0.845967C14.8522 0.734556 14.8729 0.623965 14.9147 0.52072C14.9565 0.417475 15.0185 0.323665 15.0971 0.244825C15.1756 0.165984 15.2692 0.10371 15.3721 0.0616755C15.475 0.0196407 15.5854 -0.00130318 15.6965 7.91782e-05H19.4936C19.6048 -0.00145697 19.7152 0.0193733 19.8183 0.0613392C19.9214 0.103305 20.015 0.165555 20.0937 0.244411C20.1724 0.323267 20.2345 0.417129 20.2764 0.520451C20.3182 0.623773 20.339 0.734458 20.3375 0.845967L20.3386 14.2694Z" fill="#71E5BD" />
              <path d="M38.3749 17.9737C37.374 18.5285 36.2489 18.8195 35.1052 18.8195C33.9614 18.8195 32.8363 18.5285 31.8354 17.9737C30.7773 17.392 29.8955 16.5349 29.2829 15.4928C28.6704 14.4506 28.3498 13.262 28.355 12.0525C28.355 10.2299 29.0773 8.48193 30.3631 7.19315C31.6488 5.90438 33.3927 5.18035 35.2111 5.18035C37.0294 5.18035 38.7733 5.90438 40.0591 7.19315C41.3448 8.48193 42.0672 10.2299 42.0672 12.0525C42.0093 13.2738 41.6394 14.4598 40.9928 15.4966C40.3463 16.5334 39.4448 17.3864 38.3749 17.9737ZM35.1052 6.61337e-06C32.7477 6.61337e-06 30.4431 0.700712 28.483 2.01351C26.5228 3.32632 24.995 5.19225 24.0929 7.37536C23.1907 9.55847 22.9547 11.9607 23.4146 14.2783C23.8745 16.5959 25.0097 18.7247 26.6767 20.3956C28.3437 22.0665 30.4676 23.2043 32.7798 23.6653C35.0919 24.1263 37.4886 23.8897 39.6666 22.9855C41.8446 22.0812 43.7062 20.5498 45.0159 18.5851C46.3257 16.6204 47.0248 14.3104 47.0248 11.9475C47.0264 10.378 46.7192 8.82369 46.1208 7.37341C45.5224 5.92313 44.6444 4.6054 43.5373 3.49565C42.4301 2.3859 41.1154 1.50592 39.6685 0.906087C38.2216 0.306257 36.6709 -0.00164549 35.1052 6.61337e-06Z" fill="#101010" />
            </svg>

          </a>
        </div>

        <div className="flex lg:hidden">
          <button
            type="button"
            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700"
            onClick={() => setMobileMenuOpen(true)}
          >
            <span className="sr-only">Open main menu</span>
            <Bars3Icon className="h-8" aria-hidden="true" />
          </button>
        </div>
        <div className="hidden items-center lg:flex gap-x-16 h-full">

          <a href="/invest" className="text-lg font-bold leading-6 text-black tracking-wide">
            How it works?
          </a>
          <div className="group flex items-center gap-x-1 h-full">
            <button className="peer flex items-center text-lg font-bold leading-6 text-black h-full">
              Learn
              <ArrowUpIcon className="group-hover:rotate-180 transition group-hover:duration-200 delay-25 group-hover:delay-25 duration-200 h-5 w-5 flex-none text-black" aria-hidden="true" />
            </button>

            <motion.div initial={{ opacity: 0, y: 0 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }}
              className="hidden peer-hover:flex hover:flex transition peer-hover:duration-1000 delay-1000 peer-hover:delay-1000 absolute inset-x-0 top-0 -z-10 bg-white pt-14 shadow-nav">
              <div className="mx-auto grid max-w-md grid-cols-4 gap-x-10 mt-6 px-0 text-right">
                {learn.map((item) => (
                  <div key={item.name} className="group relative rounded-lg mb-6 text-lg leading-6">
                    <a href={item.href} className="mt-6 block font-bold text-black">
                      {item.name}
                      <span className="absolute inset-0" />
                    </a>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>

          <a href="https://app.uown.co/properties" className="text-lg font-bold leading-6 text-black tracking-wide">
            Invest
          </a>
        </div>
        <div className="hidden lg:flex lg:flex-1 lg:justify-end items-center">
          <a href="https://app.uown.co/login" className="text-lg font-bold leading-6 text-black pr-6">
            Login
          </a>
          <a href="https://app.uown.co/register" className="text-lg font-bold leading-6 text-black">
            <Button type="button" color="btn-black btn-sign-up" text="Sign Up" />
          </a>
        </div>
      </nav>
      <Dialog as="div" className="lg:hidden" open={mobileMenuOpen} onClose={setMobileMenuOpen}>
        <div className="fixed inset-0 z-30" />
        <Dialog.Panel className="fixed inset-y-0 right-0 z-30 w-full overflow-y-auto bg-white">
          <div className="flex items-center justify-between px-5 py-5 shadow-nav">
            <a href="/" className="-m-1.5 p-1.5 sm:hidden md:inline-flex">
              <span className="sr-only">UOWN</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="104" height="24" viewBox="0 0 131 31" fill="none">
                <path d="M102.029 0.382911H97.2713C97.1319 0.380991 96.9935 0.407029 96.8644 0.459487C96.7352 0.511944 96.6179 0.589757 96.5193 0.688327C96.4207 0.786896 96.3429 0.904224 96.2905 1.03338C96.238 1.16253 96.212 1.30089 96.2139 1.44027V18.2196C96.1949 19.6171 95.6246 20.9506 94.6272 21.9296C93.6214 22.8642 92.2993 23.3836 90.9264 23.3836C89.5535 23.3836 88.2314 22.8642 87.2256 21.9296C86.3112 21.0052 85.7502 19.7892 85.6403 18.4936V1.44027C85.6422 1.30089 85.6162 1.16253 85.5637 1.03338C85.5113 0.904224 85.4335 0.786896 85.3349 0.688327C85.2363 0.589757 85.119 0.511944 84.9898 0.459487C84.8607 0.407029 84.7223 0.380991 84.583 0.382911H79.9568C79.8174 0.380991 79.6791 0.407029 79.5499 0.459487C79.4208 0.511944 79.3035 0.589757 79.2049 0.688327C79.1063 0.786896 79.0285 0.904224 78.976 1.03338C78.9236 1.16253 78.8975 1.30089 78.8995 1.44027V18.4893C78.8887 19.1414 78.7422 19.7841 78.4692 20.3765C78.1963 20.9688 77.8029 21.4978 77.3141 21.9296C76.3084 22.8642 74.9863 23.3836 73.6134 23.3836C72.2405 23.3836 70.9184 22.8642 69.9126 21.9296C68.9157 20.9503 68.3459 19.6169 68.3273 18.2196V1.44027C68.3292 1.30089 68.3032 1.16253 68.2507 1.03338C68.1983 0.904224 68.1205 0.786896 68.0219 0.688327C67.9233 0.589757 67.806 0.511944 67.6768 0.459487C67.5477 0.407029 67.4093 0.380991 67.2699 0.382911H62.5125C62.3732 0.380991 62.2348 0.407029 62.1056 0.459487C61.9765 0.511944 61.8592 0.589757 61.7606 0.688327C61.662 0.786896 61.5842 0.904224 61.5318 1.03338C61.4793 1.16253 61.4533 1.30089 61.4552 1.44027V18.2196C61.4512 19.8174 61.763 21.4003 62.3727 22.8773C62.9823 24.3543 63.8778 25.6962 65.0076 26.8261C66.1375 27.9559 67.4794 28.8514 68.9564 29.461C70.4334 30.0707 72.0163 30.3825 73.6141 30.3785C75.2406 30.3698 76.8492 30.0383 78.3465 29.4031C79.8439 28.768 81.2003 27.8419 82.337 26.6785C83.4737 27.8419 84.83 28.768 86.3274 29.4031C87.8248 30.0383 89.4333 30.3698 91.0598 30.3785C92.6575 30.3823 94.2403 30.0704 95.7171 29.4606C97.1939 28.8509 98.5357 27.9554 99.6654 26.8256C100.795 25.6957 101.69 24.3538 102.3 22.877C102.91 21.4001 103.221 19.8173 103.217 18.2196V1.44027C103.149 1.16449 102.999 0.915788 102.787 0.727011C102.575 0.538233 102.31 0.418253 102.029 0.382911Z" fill="#101010" />
                <path d="M24.3178 18.2196C24.3178 21.4444 23.0368 24.537 20.7566 26.8173C18.4763 29.0975 15.3837 30.3785 12.1589 30.3785C8.93417 30.3785 5.84151 29.0975 3.56127 26.8173C1.28103 24.537 0 21.4444 0 18.2196L0 1.44027C0.019559 1.16631 0.137239 0.90858 0.331454 0.714365C0.525669 0.520151 0.783396 0.40247 1.05736 0.382911H5.81476C5.95415 0.380991 6.09251 0.407029 6.22166 0.459487C6.35081 0.511944 6.46814 0.589757 6.56671 0.688327C6.66528 0.786897 6.74309 0.904224 6.79555 1.03338C6.84801 1.16253 6.87404 1.30089 6.87212 1.44027V18.2196C6.89113 19.6171 7.46143 20.9506 8.45888 21.9296C8.94983 22.4032 9.52931 22.7754 10.1642 23.0249C10.7991 23.2744 11.4769 23.3963 12.1589 23.3837C12.8409 23.3963 13.5188 23.2744 14.1536 23.0249C14.7885 22.7754 15.368 22.4032 15.859 21.9296C16.8564 20.9506 17.4267 19.6171 17.4457 18.2196V1.44027C17.4438 1.30101 17.4698 1.16277 17.5222 1.03371C17.5745 0.904656 17.6522 0.787394 17.7506 0.688843C17.8491 0.590293 17.9662 0.51245 18.0952 0.459907C18.2242 0.407363 18.3624 0.381184 18.5016 0.382911H23.2591C23.3984 0.380991 23.5368 0.407029 23.666 0.459487C23.7951 0.511944 23.9124 0.589757 24.011 0.688327C24.1096 0.786897 24.1874 0.904224 24.2398 1.03338C24.2923 1.16253 24.3183 1.30089 24.3164 1.44027L24.3178 18.2196Z" fill="#71E5BD" />
                <path d="M106.656 12.5417C106.656 9.31699 107.937 6.22432 110.218 3.94408C112.498 1.66384 115.591 0.382813 118.815 0.382812C122.04 0.382813 125.133 1.66384 127.413 3.94408C129.693 6.22432 130.974 9.31699 130.974 12.5417V29.3254C130.976 29.4647 130.95 29.6031 130.898 29.7322C130.845 29.8614 130.767 29.9787 130.669 30.0773C130.57 30.1759 130.453 30.2537 130.324 30.3061C130.195 30.3586 130.056 30.3846 129.917 30.3827H125.158C125.019 30.3846 124.88 30.3586 124.751 30.3061C124.622 30.2537 124.505 30.1759 124.406 30.0773C124.307 29.9787 124.23 29.8614 124.177 29.7322C124.125 29.6031 124.099 29.4647 124.101 29.3254V12.5417C124.065 11.1529 123.497 9.83083 122.514 8.84841C121.532 7.866 120.21 7.29816 118.821 7.26207C118.139 7.24945 117.461 7.37137 116.826 7.62087C116.191 7.87036 115.612 8.24254 115.121 8.71612C114.126 9.69296 113.557 11.0223 113.536 12.4162V29.1941C113.538 29.3335 113.511 29.4718 113.459 29.601C113.407 29.7301 113.329 29.8475 113.23 29.946C113.132 30.0446 113.014 30.1224 112.885 30.1749C112.756 30.2273 112.618 30.2534 112.478 30.2514H107.714C107.574 30.2534 107.436 30.2273 107.307 30.1749C107.178 30.1224 107.06 30.0446 106.962 29.946C106.863 29.8475 106.785 29.7301 106.733 29.601C106.68 29.4718 106.654 29.3335 106.656 29.1941V12.5417Z" fill="#101010" />
                <path d="M46.9178 22.8499C45.6637 23.5434 44.2541 23.9072 42.821 23.9072C41.388 23.9072 39.9784 23.5434 38.7243 22.8499C37.3986 22.1228 36.2938 21.0514 35.5263 19.7488C34.7588 18.4461 34.3571 16.9604 34.3636 15.4484C34.3636 13.1702 35.2686 10.9852 36.8796 9.37425C38.4906 7.76329 40.6755 6.85826 42.9537 6.85826C45.232 6.85826 47.4169 7.76329 49.0279 9.37425C50.6389 10.9852 51.5439 13.1702 51.5439 15.4484C51.4714 16.9751 51.0079 18.4575 50.1978 19.7536C49.3878 21.0496 48.2583 22.1158 46.9178 22.8499ZM42.821 0.382821C39.8673 0.382821 36.9799 1.2587 34.524 2.89971C32.068 4.54071 30.1539 6.87313 29.0235 9.60202C27.8932 12.3309 27.5974 15.3337 28.1737 18.2307C28.7499 21.1276 30.1723 23.7887 32.2609 25.8773C34.3495 27.9659 37.0105 29.3882 39.9075 29.9645C42.8045 30.5407 45.8073 30.245 48.5361 29.1146C51.265 27.9843 53.5975 26.0701 55.2385 23.6142C56.8795 21.1583 57.7553 18.2709 57.7553 15.3171C57.7574 13.3553 57.3725 11.4124 56.6227 9.59958C55.873 7.78673 54.773 6.13956 53.3858 4.75237C51.9986 3.36518 50.3514 2.26521 48.5386 1.51542C46.7257 0.765634 44.7828 0.380756 42.821 0.382821Z" fill="#101010" />
              </svg>
            </a>
            <a href="/" className="-m-1.5 p-1.5 lg:hidden md:hidden">
              <span className="sr-only">UOWN</span>
              <svg width="48" height="24" viewBox="0 0 48 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20.3386 14.2694C20.3386 16.8492 19.3162 19.3234 17.4962 21.1476C15.6763 22.9718 13.2079 23.9966 10.6341 23.9966C8.06037 23.9966 5.592 22.9718 3.77206 21.1476C1.95212 19.3234 0.929688 16.8492 0.929688 14.2694L0.929688 0.845967C0.945298 0.626796 1.03922 0.420614 1.19423 0.265242C1.34924 0.10987 1.55494 0.0157264 1.7736 7.91782e-05H5.57065C5.6819 -0.00145697 5.79233 0.0193733 5.89541 0.0613392C5.99849 0.103305 6.09214 0.165555 6.17081 0.244411C6.24948 0.323267 6.31159 0.417129 6.35345 0.520451C6.39532 0.623773 6.4161 0.734458 6.41457 0.845967V14.2694C6.42974 15.3875 6.88492 16.4542 7.68101 17.2375C8.07287 17.6163 8.53537 17.9141 9.04209 18.1137C9.54881 18.3133 10.0898 18.4108 10.6341 18.4007C11.1785 18.4108 11.7195 18.3133 12.2262 18.1137C12.7329 17.9141 13.1954 17.6163 13.5873 17.2375C14.3834 16.4542 14.8386 15.3875 14.8537 14.2694V0.845967C14.8522 0.734556 14.8729 0.623965 14.9147 0.52072C14.9565 0.417475 15.0185 0.323665 15.0971 0.244825C15.1756 0.165984 15.2692 0.10371 15.3721 0.0616755C15.475 0.0196407 15.5854 -0.00130318 15.6965 7.91782e-05H19.4936C19.6048 -0.00145697 19.7152 0.0193733 19.8183 0.0613392C19.9214 0.103305 20.015 0.165555 20.0937 0.244411C20.1724 0.323267 20.2345 0.417129 20.2764 0.520451C20.3182 0.623773 20.339 0.734458 20.3375 0.845967L20.3386 14.2694Z" fill="#71E5BD" />
                <path d="M38.3749 17.9737C37.374 18.5285 36.2489 18.8195 35.1052 18.8195C33.9614 18.8195 32.8363 18.5285 31.8354 17.9737C30.7773 17.392 29.8955 16.5349 29.2829 15.4928C28.6704 14.4506 28.3498 13.262 28.355 12.0525C28.355 10.2299 29.0773 8.48193 30.3631 7.19315C31.6488 5.90438 33.3927 5.18035 35.2111 5.18035C37.0294 5.18035 38.7733 5.90438 40.0591 7.19315C41.3448 8.48193 42.0672 10.2299 42.0672 12.0525C42.0093 13.2738 41.6394 14.4598 40.9928 15.4966C40.3463 16.5334 39.4448 17.3864 38.3749 17.9737ZM35.1052 6.61337e-06C32.7477 6.61337e-06 30.4431 0.700712 28.483 2.01351C26.5228 3.32632 24.995 5.19225 24.0929 7.37536C23.1907 9.55847 22.9547 11.9607 23.4146 14.2783C23.8745 16.5959 25.0097 18.7247 26.6767 20.3956C28.3437 22.0665 30.4676 23.2043 32.7798 23.6653C35.0919 24.1263 37.4886 23.8897 39.6666 22.9855C41.8446 22.0812 43.7062 20.5498 45.0159 18.5851C46.3257 16.6204 47.0248 14.3104 47.0248 11.9475C47.0264 10.378 46.7192 8.82369 46.1208 7.37341C45.5224 5.92313 44.6444 4.6054 43.5373 3.49565C42.4301 2.3859 41.1154 1.50592 39.6685 0.906087C38.2216 0.306257 36.6709 -0.00164549 35.1052 6.61337e-06Z" fill="#101010" />
              </svg>
            </a>
            <button
              type="button"
              className="-m-2.5 rounded-md p-2.5 text-gray-700"
              onClick={() => setMobileMenuOpen(false)}
            >
              <span className="sr-only">Close menu</span>
              <XMarkIcon className="h-8 w-8" aria-hidden="true" />
            </button>
          </div>
          <div className="flex flex-col px-8 md:px-20 py-6 h-[88%]">
            <div className="space-y-2 py-6">
              <a
                href="/invest"
                className="-mx-3 block px-3 py-8 text-lg font-bold leading-7 text-black nav-opt-border"
              >
                How it works?
              </a>
              <Disclosure as="div" className="-mx-3 py-5 nav-opt-border">
                {({ open }) => (
                  <>
                    <Disclosure.Button className="flex w-full items-center justify-between py-2 pl-3 pr-3.5 text-lg font-bold leading-7 text-black">
                      Learn
                      <ArrowDownIcon
                        className={classNames(open ? 'rotate-180' : '', 'h-5 w-5 flex-none')}
                        aria-hidden="true"
                      />
                    </Disclosure.Button>
                    <Disclosure.Panel className="mt-2 space-y-2">
                      {[...learn].map((item) => (
                        <Disclosure.Button
                          key={item.name}
                          as="a"
                          href={item.href}
                          className="block py-2 pl-3 pr-3 text-sm font-medium leading-7 text-black"
                        >
                          {item.name}
                        </Disclosure.Button>
                      ))}
                    </Disclosure.Panel>
                  </>
                )}
              </Disclosure>
              <a
                href="https://app.uown.co/properties"
                className="-mx-3 block px-3 py-8 text-lg font-bold leading-7 text-black nav-opt-border"
              >
                Invest
              </a>
            </div>
            <div className="flex items-end justify-center grow pb-2.5">
              <div className="pt-4 pr-6">
                <a
                  href="https://app.uown.co/login"
                  className="-mx-3 block rounded-lg px-3 pb-3.5 md:text-lg text-lg font-bold leading-7 text-black"
                >
                  Login
                </a>
              </div>
              <div className="pt-6">
                <a
                  href="https://app.uown.co/register"
                  className="-mx-3 block rounded-lg px-3 font-bold leading-7 text-black"
                >
                  <Button type="button" color="btn-black btn-sign-up" text="Sign Up" />
                </a>
              </div>
            </div>

          </div>
        </Dialog.Panel>
      </Dialog>
    </motion.header>
  )
}

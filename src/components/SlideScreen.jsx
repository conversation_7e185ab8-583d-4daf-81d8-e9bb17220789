'use client'
import { useTransform, motion, useScroll } from 'motion/react';
import { useRef } from 'react';
import { CheckCircleIcon } from '@heroicons/react/24/solid';

const Card = ({ i, title, description, src, bullets, bulletColor, checkMarkColor, color, progress, range, targetScale }) => {

  const container = useRef(null);
  const { scrollYProgress } = useScroll({
    target: container,
    offset: ['start end', 'start start']
  })

  const imageScale = useTransform(scrollYProgress, [0, 1], [2, 1])
  const scale = useTransform(progress, range, [1, targetScale]);

  return (
    <div ref={container} className="h-screen flex items-normal center justify-center sticky top-[150px]">
      <motion.div
        style={{ backgroundColor: color, scale, top: `calc(-5vh + ${i * 25}px)` }}
        className="flex flex-col relative top-[-25%] h-[620px] smd:h-[500px] lg:h-[600px] max-w-[1000px] rounded-[25px] pt-0 md:p-[50px] p-[20px] origin-top"
      >
        <div className="flex h-full mt-[50px] gap-[50px]">
          <div className="lg:w-[50%] relative flex flex-col">
            <h3 className="text-left m-0 text-2xl md:text-[28px] mb-4">{title}</h3>
            <p className="text-sm md:text-base text-gray-700">{description}</p>
            <div className="pt-4 flex flex-col gap-y-2 text-black-50">
              <div className="md:w-max md:text-sm text-xs flex gap-x-2 rounded-full p-2 pr-4 items-center" style={{ backgroundColor: bulletColor }}><span><CheckCircleIcon className="h-6 w-6" style={{ color: checkMarkColor }} aria-hidden="true" /></span>{bullets[0]}</div>
              <div className="md:w-max md:text-sm text-xs flex gap-x-2 rounded-full p-2 pr-4 items-center" style={{ backgroundColor: bulletColor }}><span><CheckCircleIcon className="h-6 w-6" style={{ color: checkMarkColor }} aria-hidden="true" /></span>{bullets[1]}</div>
              <div className="md:w-max md:text-sm text-xs flex gap-x-2 rounded-full p-2 pr-4 items-center" style={{ backgroundColor: bulletColor }}><span><CheckCircleIcon className="h-6 w-6" style={{ color: checkMarkColor }} aria-hidden="true" /></span>{bullets[2]}</div>
            </div>
            <span className='flex items-center gap-[10px] mt-10 text-black-50'>
              <a className="cursor-pointer text-sm font-semibold " href={''} target="_blank">View Projects</a>
              <svg width="22" height="12" viewBox="0 0 22 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21.5303 6.53033C21.8232 6.23744 21.8232 5.76256 21.5303 5.46967L16.7574 0.696699C16.4645 0.403806 15.9896 0.403806 15.6967 0.696699C15.4038 0.989592 15.4038 1.46447 15.6967 1.75736L19.9393 6L15.6967 10.2426C15.4038 10.5355 15.4038 11.0104 15.6967 11.3033C15.9896 11.5962 16.4645 11.5962 16.7574 11.3033L21.5303 6.53033ZM0 6.75L21 6.75V5.25L0 5.25L0 6.75Z" fill="black" />
              </svg>
            </span>
          </div>

          <div className="relative w-[50%] h-full rounded-[25px] overflow-hidden lg:block hidden">
            <motion.div
              className="w-full h-full" 
              style={{ scale: imageScale }}
            >
              <img
                src={src.src}
                alt="image"
                className='object-cover'
              />
            </motion.div>
          </div>

        </div>
      </motion.div>
    </div>
  )
}

export default Card
import clsx from 'clsx'

import {motion} from 'motion/react';
import { Container } from '../components/ContainerA.jsx'
import {
  Expandable,
  ExpandableButton,
  ExpandableItems,
} from '../components/Expandable.jsx'

const testimonials = [
  [
    {
      content:
        'I have been thoroughly impressed with my experience using UOWN.co. ]as easy to follow every step of the way.',
      author: {
        name: '<PERSON><PERSON>',
        role: 'Frontend Developer',
      },
    },
    {
      content:
        'UOWN allows me to invest in property through an expert team, without the time commitment of doing it all myself.',
      author: {
        name: '<PERSON>',
        role: 'Growth Marketer',
      },
    },
    {
      content:
        'This company is an active business and this has given me confidence in using the platform for the future.',
      author: {
        name: '<PERSON><PERSON><PERSON>',
        role: 'Professional Designer',
      },
    },
  ],
  [
    {
      content:
        'I used uown for 3-4 years, and earned a very solid return. When it came time to withdraw basically all my portfolio, I was able to do so very quickly, with no hassle.',
      author: {
        name: '<PERSON><PERSON><PERSON>',
        role: 'Entrepreneur',
      },
    },
    {
      content:
        'What a great company. Had an issue with one of my bank transfers, it was sorted out by one of the owners of the company.',
      author: {
        name: '<PERSON>',
        role: 'Design Student',
      },
    },
    {
      content:
        'Great place to invest on properties.I have joined them long time and had great returns.',
      author: {
        name: 'Shamoon Mushtaq',
        role: 'UI Engineer',

      },
    },
  ],
  [
    {
      content:
        'Fantastic opportunities and these guys have always delivered.',
      author: {
        name: 'Naveen Ahmed',
        role: 'Bootcamp Instructor',
      },
    },
    {
      content:
        'UOWN is a fantastic place to invest. They offer several properties which allows you to diversify, but also new properties enter their portfolio every couple of months. Well worth it!',
      author: {
        name: 'Swissmat',
        role: 'Startup Founder',
      },
    },
    {
      content:
        "I've made multiple investments through the UOWN platform, and every single time they've delivered what they said they would.",
      author: {
        name: 'Matt Lord',
        role: 'Creative Director',
      },
    },
  ],
]

function Testimonial({ author, children }) {
  return (
    <figure className="rounded-4xl p-8 shadow-md ring-1 ring-slate-900/5">
      <blockquote>
        <p className="text-lg tracking-tight text-slate-900 before:content-['“'] after:content-['”']">
          {children}
        </p>
      </blockquote>
      <figcaption className="mt-6 flex items-center">
        <div className="">
          <div className="text-base font-medium leading-6 tracking-tight text-slate-900">
            {author.name}
          </div>
          <div className="mt-1 text-sm text-slate-600"></div>
        </div>
      </figcaption>
    </figure>
  )
}

export function Testimonials() {
  return (
    <section className="py-8 sm:py-10 lg:py-16">
      <Container className="text-center">
        <motion.h2 initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }} className="font-display text-4xl font-bold tracking-tight text-slate-900">
          Some kind words from early customers...
        </motion.h2>
        <motion.p initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }} className="mt-4 text-lg tracking-tight text-slate-600">
          I worked with a small group of early access customers to make sure all
          of the content in the book was exactly what they needed. Hears what
          they had to say about the finished product.
        </motion.p>
      </Container>
      <Expandable className="group mt-16">
        <ul
          role="list"
          className="mx-auto grid max-w-2xl grid-cols-1 gap-8 px-4 lg:max-w-7xl lg:grid-cols-3 lg:px-8"
        >
          {testimonials
            .map((column) => column[0])
            .map((testimonial, testimonialIndex) => (
              <motion.li initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
              transition={{ duration: 0.5, ease: "easeIn" }} key={testimonialIndex} className="lg:hidden">
                <Testimonial author={testimonial.author}>
                  {testimonial.content}
                </Testimonial>
              </motion.li>
            ))}
          {testimonials.map((column, columnIndex) => (
            <li
              key={columnIndex}
              className="hidden group-data-[expanded]:list-item lg:list-item"
            >
              <ul role="list">
                <ExpandableItems>
                  {column.map((testimonial, testimonialIndex) => (
                    <motion.li initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                    transition={{ duration: 0.5, ease: "easeIn" }}
                      key={testimonialIndex}
                      className={clsx(
                        testimonialIndex === 0 && 'hidden lg:list-item',
                        testimonialIndex === 1 && 'lg:mt-8',
                        testimonialIndex > 1 && 'mt-8',
                      )}
                    >
                      <Testimonial author={testimonial.author}>
                        {testimonial.content}
                      </Testimonial>
                    </motion.li>
                  ))}
                </ExpandableItems>
              </ul>
            </li>
          ))}
        </ul>
        <ExpandableButton>Read more testimonials</ExpandableButton>
      </Expandable>
    </section>
  )
}

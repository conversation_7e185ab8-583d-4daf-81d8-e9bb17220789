---
import Return from "../assets/images/home/<USER>";
import Uowners from "../assets/images/home/<USER>";
import Value from "../assets/images/home/<USER>";

import SInOpt from "../components/SInOption.astro";
---

<div class="hidden container md:flex items-center justify-evenly flex-wrap mx-auto pt-24">
    <SInOpt
        textH5="£70m+"
        text2xl="Of  Project Value"
        textp="We've handled big projects and delivered big results."
        imgUrl={Value}
        btnText="View Projects"
        btnUrl="https://app.uown.co/properties"
    />
    <SInOpt
        textH5="17.7%"
        text2xl="Average Project Return"
        textp="With our most successful project returning a huge 26%."
        imgUrl={Return}
        btnText="View Projects"
        btnUrl="https://app.uown.co/properties"
    />
    <SInOpt
        textH5="11,000+"
        text2xl="UOWNers"
        textp="The UOWN crowd is growing by the day..."
        imgUrl={Uowners}
        btnText="Get Started"
        btnUrl="https://app.uown.co/register"
    />
</div>

<style scoped>
    .container {
        max-width: 1508px;
    }
</style>

import React from "react";
import {PortableText} from '@portabletext/react'

let count=0;
const components = {
  types: {
    image: ({children}) => '',
    youtube: ({node}) => ''
  },
  marks: {
    // Ex. 1: custom renderer for the em / italics decorator
    em: ({children}) => '',

    internalLink: ({value, children}) => {},

    // Ex. 2: rendering a custom `link` annotation
    link: ({value, children}) => {},
  },
  block: {
    // Ex. 1: customizing common block types
    h1: ({children}) => '',
    h2: ({children}) => <a href={"#h" + count++} className="block text-lg tracking-wide font-regular pb-3">{children}</a>,
    h3: ({children}) => '',
    h4: ({children}) => '',
    h5: ({children}) => '',
    h6: ({children}) => '',
    normal: ({children}) => '',
    blockquote: ({children}) => '',

    // Ex. 2: rendering custom styles
    customHeading: ({children}) => (
      ''
    ),
  },
  list: {
    // Ex. 1: customizing common list types
    bullet: ({children}) => '',
    number: ({children}) => '',

    // Ex. 2: rendering custom lists
    checkmarks: ({children}) => '',
  },
  listItem: {
    // Ex. 1: customizing common list types
    bullet: ({children}) => '',
    number: ({children}) => '',

    // Ex. 2: rendering custom list items
    checkmarks: ({children}) => '',
  },
}

const content = (post) => {
  let value, postContent = post.post;
  
  if (postContent.bodyText) {
    value = postContent.bodyText;
  } else {
    value = postContent.contentHtml
  }
  count=0;
  return (
    <div>
      <PortableText value={value} components={components} />
    </div>
  );
}

export default content;

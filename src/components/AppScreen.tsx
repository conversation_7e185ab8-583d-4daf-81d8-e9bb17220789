import { forwardRef } from 'react'
import clsx from 'clsx'

function Logo(props: React.ComponentPropsWithoutRef<'svg'>) {
  return (
    <svg width="48" height="24" viewBox="0 0 48 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M20.3391 14.2694C20.3391 16.8492 19.3167 19.3234 17.4967 21.1476C15.6768 22.9718 13.2084 23.9966 10.6346 23.9966C8.06085 23.9966 5.59249 22.9718 3.77255 21.1476C1.95261 19.3234 0.930176 16.8492 0.930176 14.2694L0.930176 0.845967C0.945786 0.626796 1.03971 0.420614 1.19472 0.265242C1.34973 0.10987 1.55543 0.0157264 1.77409 7.91782e-05H5.57114C5.68239 -0.00145697 5.79282 0.0193733 5.8959 0.0613392C5.99898 0.103305 6.09262 0.165555 6.1713 0.244411C6.24997 0.323267 6.31207 0.417129 6.35394 0.520451C6.39581 0.623773 6.41659 0.734458 6.41506 0.845967V14.2694C6.43023 15.3875 6.88541 16.4542 7.6815 17.2375C8.07335 17.6163 8.53586 17.9141 9.04258 18.1137C9.54929 18.3133 10.0903 18.4108 10.6346 18.4007C11.179 18.4108 11.72 18.3133 12.2267 18.1137C12.7334 17.9141 13.1959 17.6163 13.5878 17.2375C14.3839 16.4542 14.839 15.3875 14.8542 14.2694V0.845967C14.8527 0.734556 14.8734 0.623965 14.9152 0.52072C14.957 0.417475 15.019 0.323665 15.0976 0.244825C15.1761 0.165984 15.2696 0.10371 15.3726 0.0616755C15.4755 0.0196407 15.5858 -0.00130318 15.697 7.91782e-05H19.494C19.6053 -0.00145697 19.7157 0.0193733 19.8188 0.0613392C19.9219 0.103305 20.0155 0.165555 20.0942 0.244411C20.1729 0.323267 20.235 0.417129 20.2768 0.520451C20.3187 0.623773 20.3395 0.734458 20.338 0.845967L20.3391 14.2694Z" fill="#71E5BD" />
      <path d="M38.3759 17.9737C37.3749 18.5285 36.2499 18.8195 35.1061 18.8195C33.9624 18.8195 32.8373 18.5285 31.8364 17.9737C30.7783 17.392 29.8965 16.5349 29.2839 15.4928C28.6713 14.4506 28.3507 13.262 28.3559 12.0525C28.3559 10.2299 29.0783 8.48193 30.364 7.19315C31.6498 5.90438 33.3937 5.18035 35.212 5.18035C37.0304 5.18035 38.7743 5.90438 40.06 7.19315C41.3458 8.48193 42.0681 10.2299 42.0681 12.0525C42.0103 13.2738 41.6403 14.4598 40.9938 15.4966C40.3473 16.5334 39.4458 17.3864 38.3759 17.9737ZM35.1061 6.61337e-06C32.7487 6.61337e-06 30.4441 0.700712 28.484 2.01351C26.5238 3.32632 24.996 5.19225 24.0939 7.37536C23.1917 9.55847 22.9556 11.9607 23.4156 14.2783C23.8755 16.5959 25.0107 18.7247 26.6777 20.3956C28.3447 22.0665 30.4686 23.2043 32.7807 23.6653C35.0929 24.1263 37.4895 23.8897 39.6676 22.9855C41.8456 22.0812 43.7072 20.5498 45.0169 18.5851C46.3267 16.6204 47.0257 14.3104 47.0257 11.9475C47.0274 10.378 46.7202 8.82369 46.1218 7.37341C45.5233 5.92313 44.6454 4.6054 43.5382 3.49565C42.4311 2.3859 41.1164 1.50592 39.6695 0.906087C38.2226 0.306257 36.6719 -0.00164549 35.1061 6.61337e-06Z" fill="#101010" />
    </svg>
  )
}

function MenuIcon(props: React.ComponentPropsWithoutRef<'svg'>) {
  return (
    <svg viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
      <path
        d="M5 6h14M5 18h14M5 12h14"
        stroke="#000"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

function UserIcon(props: React.ComponentPropsWithoutRef<'svg'>) {
  return (
    <svg viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
      <path
        d="M15 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0ZM6.696 19h10.608c1.175 0 2.08-.935 1.532-1.897C18.028 15.69 16.187 14 12 14s-6.028 1.689-6.836 3.103C4.616 18.065 5.521 19 6.696 19Z"
        stroke="#fff"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export function AppScreen({
  children,
  className,
  ...props
}: React.ComponentPropsWithoutRef<'div'>) {
  return (
    <div className={clsx('flex flex-col bg-white', className)} {...props}>
      <div className="flex justify-between px-4 pt-4">
        <Logo className="h-6 flex-none" />
        <MenuIcon className="h-6 w-6 flex-none" />
      </div>
      {children}
    </div>
  )
}

AppScreen.Header = forwardRef<
  React.ElementRef<'div'>,
  { children: React.ReactNode }
>(function AppScreenHeader({ children }, ref) {
  return (
    <div ref={ref} className="mt-6 px-4 text-white bg-white">
      {children}
    </div>
  )
})

AppScreen.Title = forwardRef<
  React.ElementRef<'div'>,
  { children: React.ReactNode }
>(function AppScreenTitle({ children }, ref) {
  return (
    <div ref={ref} className="text-2xl text-black-50 font-bold">
      {children}
    </div>
  )
})

AppScreen.Subtitle = forwardRef<
  React.ElementRef<'div'>,
  { children: React.ReactNode }
>(function AppScreenSubtitle({ children }, ref) {
  return (
    <div ref={ref} className="text-sm text-gray-500">
      {children}
    </div>
  )
})

AppScreen.Body = forwardRef<
  React.ElementRef<'div'>,
  { className?: string; children: React.ReactNode }
>(function AppScreenBody({ children, className }, ref) {
  return (
    <div
      ref={ref}
      className={clsx('mt-6 flex-auto rounded-t-2xl bg-white', className)}
    >
      {children}
    </div>
  )
})

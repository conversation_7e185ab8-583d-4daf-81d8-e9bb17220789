import React from "react";
import {
  StackedCarousel,
  ResponsiveContainer
} from "react-stacked-center-carousel";
import { Slide } from "./StackedCarouselSlide";


const data = [
  {
    name: '<PERSON>ak',
    text: '“ Good online service with clear information about investments and risk. great platform as you can invest from small to large amounts. The team at UOWN are incredibly useful, professional and switched on, The team at UOWN are incredibly useful.”'
  },
  {
    name: '<PERSON>',
    text: "“Excellent customer service, and clear that the company is aiming to be transparent and enhtusiastic.”"
  },
  {
    name: '<PERSON>', 
    text: "“Easy for the “little people” to  the invest. Rents always on time. Word Portfolio growing.”"
  }
];

const StackedCarouselex = () => {
  const ref = React.useRef(StackedCarousel);
  return (
    <div className="hidden card md:block">
      <div style={{ width: "100%", position: "relative"}}>
        <ResponsiveContainer
          carouselRef={ref}
          render={(width, carouselRef) => {
            return (
              <StackedCarousel
                ref={carouselRef}
                slideComponent={Slide}
                slideWidth={360}
                carouselWidth={width}
                data={data}
                maxVisibleSlide={3}
                disableSwipe
                customScales={[1, 0.85, 0.7]}
                transitionTime={450}
               height={550}
              />
            );
          }}
        />
        <div
          className="card-button left"
          size="small"
          onClick={() => ref.current?.goBack()}
        >
          <button
            type="button"
            className="absolute top-0 left-0 z-30 flex items-center justify-center h-full pl-[2%] lg:pl-[7%] cursor-pointer group focus:outline-none"
            aria-label="Previous"
            data-carousel-prev
          >
            <svg
              width="40"
              height="40"
              viewBox="0 0 40 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_826_14695)">
                <circle
                  cx="20"
                  cy="20"
                  r="20"
                  transform="rotate(-180 20 20)"
                  fill="black"></circle>
                <path
                  d="M29.9453 18.4714L29.9453 22.0254L16.4645 22.0254L22.5002 28.582L17.7513 28.582L10.6432 21.1063L10.6432 19.4518L17.7513 11.9148L22.5002 11.9148L16.4951 18.4714L29.9453 18.4714Z"
                  fill="white"></path>
              </g>
              <defs>
                <clipPath id="clip0_826_14695">
                  <rect
                    width="1920"
                    height="9511"
                    fill="white"
                    transform="translate(-528 -5776)"></rect>
                </clipPath>
              </defs>
            </svg>
          </button>
        </div>
        <div
          className="card-button right"
          size="small"
          onClick={() => ref.current?.goNext()}
        >
          <button
            type="button"
            className="absolute top-0 right-0 z-30 flex items-center justify-center h-full pr-[2%] lg:pr-[7%] cursor-pointer group focus:outline-none"
            aria-label="Next"
            data-carousel-next
          >
            <svg
              width="40"
              height="40"
              viewBox="0 0 40 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="20" cy="20" r="20" fill="black"></circle>
              <path
                d="M10.0547 21.5286L10.0547 17.9746L23.5355 17.9746L17.4998 11.418L22.2487 11.418L29.3568 18.8937L29.3568 20.5482L22.2487 28.0852L17.4998 28.0852L23.5049 21.5286L10.0547 21.5286Z"
                fill="white"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default StackedCarouselex;

import LoadingState from "../assets/images/wicker-island/three-dots-wicker.svg";
import CheckMark from "../assets/images/wicker-island/check-wicker.svg";
import Cross from "../assets/images/wicker-island/times-solid-wicker.svg";

export default function EmailForm(isRoomz: any) {

    if (!isRoomz) {
       isRoomz = false;
    }

    return (
        <form method="POST" id="email-form" className={"mt-12 sm:flex sm:w-full sm:max-w-lg " + (isRoomz ? 'ml-0' : 'mx-auto')}>
            <div className="min-w-0 flex-1">
                <label htmlFor="hero-name" className="sr-only">Name</label>
                <input
                    required
                    id="e-name"
                    name="name"
                    type="text"
                    className="block w-full rounded-md border border-gray-300 px-5 py-3 text-base text-gray-900 placeholder-gray-100 shadow-sm focus:border-rose-500 focus:ring-rose-500"
                    placeholder="Enter your full name"
                    minLength={6}
                />
                <label htmlFor="hero-email" className="sr-only">Email</label>
                <div className="relative mt-2 rounded-md shadow-sm">
                    <input
                        required
                        type="email"
                        name="email"
                        id="email"
                        className="block w-full rounded-md border border-gray-300 px-5 py-3 text-base text-gray-900 placeholder-gray-100 shadow-sm focus:border-rose-500 focus:ring-rose-500"
                        placeholder="Enter Email Address"
                    />
                </div>
                <button
                    id="recaptcha1"
                    type="submit"
                    className={"email-btn block w-full rounded-md border border-transparent px-5 py-3 text-base font-medium shadow focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 sm:px-10 mt-3 " + (isRoomz ? ' bg-mint-300 text-black hover:bg-mint-500  focus-visible:outline-mint-300' : 'text-white hover:bg-rose-950 bg-rose-900 focus:ring-offset-rose-500')}
                >
                    <span id="esubmit" className="text-submit">Email Me</span>
                    <span id="eloading" className="flex h-[25px] justify-center items-center loading-state hidden"><img src={LoadingState.src} alt="" className="absolute w-14" /></span>
                    <span id="echeckmark" className="flex h-[25px] justify-center items-center checkmark hidden"><img src={CheckMark.src} alt="" className="absolute w-6" /></span>
                    <span id="ecross" className="flex h-[25px] justify-center items-center cross hidden"><img src={Cross.src} alt="" className="absolute w-6" /></span>

                </button>
            </div>
        </form>
    );
}
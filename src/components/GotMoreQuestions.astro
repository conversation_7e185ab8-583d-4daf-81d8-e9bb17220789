---
import Button from "../components/Button.astro";

import gotMoreQuestions from "../assets/images/help/got_more_questions.png";

interface Props {
	isTopicPage: boolean;
}

const { isTopicPage } = Astro.props;
const sectionClass = "bg-gray-pages lg:py-52 py-12 " + ((isTopicPage == true ) ? 'hidden lg:block' : '');
---
<section class={sectionClass}>
    <div
        class="max-w-7xl flex flex-wrap flex-row items-center mx-auto px-11"
    >
        <div
            class="flex flex-col grow bg-white shadow-tile rounded-clg text-center lg:text-left mx-auto py-12 px-10 lg:pl-12 lg:pr-20 order-2 lg:order-1"
        >
            <h5
                class="text-3xl md:text-4xl lg:text-5xl tracking-normal font-extrabold pb-4"
            >
                Got any more questions?
            </h5>
            <p
                class="text-base md:text-xl lg:text-2xl tracking-normal font-regular pb-12"
            >
                Dont hestitate to get in touch about your queries.
            </p>
            <a href="https://app.uown.co/contact"
                ><Button
                    type="button"
                    color="btn-black btn-email"
                    text="Email us"
                /></a
            >
        </div>
        <img
            class="mx-auto lg:ml-0 order-1 lg:order-2 md:py-24 "
            src={gotMoreQuestions.src}
            alt=""
        />
    </div>
</section>
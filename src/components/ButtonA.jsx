import clsx from 'clsx'

const baseStyles = {
  solid:
    'inline-flex justify-center rounded-full py-3 px-4 text-base font-semibold tracking-tight shadow-sm focus:outline-none',
  outline:
    'inline-flex justify-center rounded-full border py-3 px-4 text-base font-semibold tracking-tight focus:outline-none',
}

const variantStyles = {
  solid: {
    slate:
      'bg-slate-900 text-white hover:bg-slate-700 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-slate-900 active:bg-slate-700 active:text-white/80 disabled:opacity-30 disabled:hover:bg-slate-900',
    mint: 'bg-mint-500 text-white hover:bg-mint-300 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-mint-500 active:bg-mint-500 active:text-white/80 disabled:opacity-30 disabled:hover:bg-mint-500',
    white:
      'bg-white text-mint-500 hover:text-mint-500 focus-visible:text-mint-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white active:bg-mint-50 active:text-mint-500/80 disabled:opacity-40 disabled:hover:text-mint-500',
  },
  outline: {
    slate:
      'border-slate-200 text-slate-900 hover:border-slate-300 hover:bg-slate-50 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-slate-600 active:border-slate-200 active:bg-slate-50 active:text-slate-900/70 disabled:opacity-40 disabled:hover:border-slate-200 disabled:hover:bg-transparent',
    blue: 'border-mint-300 text-mint-500 hover:border-mint-200 hover:bg-mint-50 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-mint-500 active:text-mint-500/70 disabled:opacity-40 disabled:hover:border-mint-300 disabled:hover:bg-transparent',
  },
}

export function Button({ className, ...props }) {
  props.variant ??= 'solid'
  props.color ??= 'slate'

  className = clsx(
    baseStyles[props.variant],
    props.variant === 'outline'
      ? variantStyles.outline[props.color]
      : props.variant === 'solid'
        ? variantStyles.solid[props.color]
        : undefined,
    className,
  )

  return typeof props.href === 'undefined' ? (
    <button className={className} {...props} />
  ) : (
    <a className={className} {...props} />
  )
}

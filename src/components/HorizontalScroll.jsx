'use client'
import { useRef } from 'react';
import { useScroll, motion, useTransform } from 'motion/react';
import ReviewContainer from './ReviewContainer';

const reviews = [
    {
        id: 1,
        reviewTitle: "UOWN has delivered so far.",
        reviewLine1: "I have trial investments with seven property crowdfunding platforms. UOWN is the only one that has delivered on time and on budget. All the others, in addition to using COVID as an excuse, have had delays and dubious excuses some bordering on the slimy.",
        reviewLine2: "When UOWN comes up with another development project if it looks interesting I'll be in (unlike four of the other platforms that I won't touch again.)",
        author: "Dr. <PERSON>",
    },
    {
        id: 2,
        reviewTitle: "UOWN allows me to invest in property through an expert team.",
        reviewLine1: "I have been investing with UOWN for several years now and they have never let me down. I have had good returns on my investments in line with what was projected. It is easy to withdraw cash my wallet when needed.",
        reviewLine2: "If there are delays which is uncommon in projects I am remunerated fairly for this. I think communication is good and the website simple and intuitive.",
        author: "<PERSON>",
    },
    {
        id: 3,
        reviewTitle: "Used Uown for years, great experience.",
        reviewLine1: "I used uown for 3-4 years, and earned a very solid return. When it came time to withdraw basically all my portfolio, I was able to do so very quickly, with no hassle.",
        reviewLine2: "The regular rental returns were rock-solid, exactly as advertised, and were topped off nicely by capital appreciation when selling shares (i.e. withdrawing) or when the property is sold.",
        author: "Jasim",
    },
    {
        id: 4,
        reviewTitle: "Invested an inheritance and I'm happy…",
        reviewLine1: "Invested an inheritance and I'm happy with my investment so far. They deliver the returns as listed. However don't expect to be able to sell out of your investment quickly at the moment.",
        reviewLine2: "This is a long term investment looked after by honest people that will help you build and grow your wealth over the long term in my opinion.",
        author: "Jonathan",
    },
    {
        id: 5,
        reviewTitle: "Excellent Service",
        reviewLine1: "Excellent service. The right platform to invest your savings and get value back. You don't have to be an investment expert to get started and that's what I love",
        reviewLine2: "Especially good customer service in terms of getting in touch regarding any concerns and helping out. Must try.",
        author: "Hugo Shepard",
    },
    {
        id: 6,
        reviewTitle: "I'm a big fan of UOWN",
        reviewLine1: "I am a big fan of UOwn - I have been investing with them for more than 6 months and not once have they been late with dividend payments or updating investors with matters that concern them.",
        reviewLine2: "I personally would like to stay with UOwn for a long time coming simply because of their consistency and timely manner.",
        author: "Sharose",
    },
    {
        id: 7,
        reviewTitle: "Very pleased and will continue to invest with UOWN!",
        reviewLine1: "I’ve invested with UOWN for some years now, and have always been very pleased with both the investments and the service from UOWN.",
        reviewLine2: "Property crowdfunding has become more difficult recently due to new regulations but UOWN have managed to quickly make new arrangements to deal with this and offer new projects still.",
        author: "Joe Dooley"
    },
]

const HorizontalScroll = () => {

    const targetRef = useRef(null);
    const {scrollYProgress} = useScroll({target: targetRef});
    const x =  useTransform(scrollYProgress, [0, 1], ["0%", "-50%"]);

    return (
        <div className="carouselContainer bg-[#F9F7F6] py-24 mt-28">
            <div className="flex flex-col justify-center items-center text-center gap-y-3 max-w-4xl mx-auto px-6 text-black-50">
                <h1 className="text-5xl tracking-tight">Trusted by Thousands of Investors</h1>
                <p className="text-xl">Established in 2016, our platform has been the premier investment choice for thousands of people looking to invest in UK property. See what they have to say about us.</p>
            </div>
            <div className="carousel h-[500vh]" ref={targetRef}>
                <div className="contentContainer h-[100vh] sticky top-[50px] flex items-center justify-start overflow-hidden">
                    <motion.div className="reviews flex gap-x-8 px-10" style={{x}}>
                        {
                            reviews.map((review) => {
                                return <ReviewContainer key={review.id} review={review} />
                            })
                        }

                    </motion.div>
                </div>

            </div>
        </div>
    );
};

export default HorizontalScroll;
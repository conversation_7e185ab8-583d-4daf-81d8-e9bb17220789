'use client'
import { motion } from 'motion/react';
import { PlayCircleIcon } from '@heroicons/react/24/outline'

import Prop1 from '../assets/images/stake-page/withdraw.png';
import Prop2 from '../assets/images/stake-page/register.png';
import S from '../assets/images/wicker-island/Setting-Dinner-Table.webp';

import GreenG from '../assets/images/stake-page/green-glow.avif';

export default function GlobalLeader() {
    return (
        <section className="relative lg:pt-24 px-3 smd:px-6 md:px-12 overflow-hidden">
            <img className="absolute z-0 left-[-40%] top-0 lg:top-[-15%] opacity-30" src={GreenG.src} alt="" />
            <div className="max-w-4xl text-black-100 flex flex-col items-center justify-center text-center gap-y-1.5 mx-auto pb-12">
                <p className="text-sm lxl:text-base text-mint-500">The global leader in digital real estate investing</p>
                <h2 className="text-4xl lxl:text-5xl pb-4">Diversify across different projects and investment types</h2>
            </div>
            <div className="max-w-5xl flex lg:flex-row flex-col gap-10 justify-between items-center mx-auto">
                <div className="flex flex-col text-black-100 justify-center items-center">
                    <h3 className="uppercase text-3xl lg:text-5xl">18<span className="text-mint-500">k+</span></h3>
                    <p className="text-xs lg:text-base text-gray-500">Registered users</p>
                </div>
                <div className="flex flex-col text-black-100 justify-center items-center">
                    <h3 className="uppercase text-3xl lg:text-5xl">GBP 115<span className="text-mint-500">m+</span></h3>
                    <p className="text-xs lg:text-base text-gray-500">Property value</p>
                </div>
                <div className="flex flex-col text-black-100 justify-center items-center">
                    <h3 className="uppercase text-3xl lg:text-5xl">125</h3>
                    <p className="text-xs lg:text-base text-gray-500">User nationalities</p>
                </div>
                <div className="flex flex-col text-black-100 justify-center items-center">
                    <h3 className="uppercase text-3xl lg:text-5xl">17.7<span className="text-mint-500">%</span></h3>
                    <p className="text-xs lg:text-base text-gray-500">Average project return</p>
                </div>
            </div>

            <div className="z-10 relative mx-auto max-w-7xl bg-white shadow-stake rounded-cmd p-4 md:p-6 flex flex-col mlg:flex-row mlg:basis-1/2 items-center justify-center gap-x-10 my-12">
                <div className="relative overflow-hidden flex shrink-0  mlg:basis-1/2 items-center justify-center w-full mlg:w-[548px] h-[456px] bg-mint-300 rounded-cmd">
                    <img src={Prop1.src} className="absolute w-[295px] h-[590px] top-[10%]" alt="" />
                    <div className="flex flex-col gap-y-2 rounded-csm absolute bg-white w-[150px] p-2 top-[60px] smd:top-[40%] left-[10%] md:left-[16%] mlg:left-[8%] lg:left-[15%] shadow-stake rotate-[-15deg]">
                        <img className="rounded-csm" src={S.src} alt="" />
                        <p className="text-lg text-black-100">Wicker Island</p>
                        <p className="text-xl font-bold text-mint-500">+14.2%</p>
                    </div>
                </div>
                <div className="flex flex-col gap-y-4 text-black-200 pt-6">
                    <p className="w-fit rounded-full py-1 px-2 text-black-100 border border-black-100 text-sm lxl:text-base">United Kingdom</p>
                    <h1 className="text-3xl lxl:text-4xl">Invest in the world’s most stable property market</h1>
                    <p className="text-base lxl:text-lg">Purchase shares in high income properties in the UK and start building a new income stream today</p>
                    <div className="flex flex-col md:flex-row w-full text-lg gap-3">
                        <a href="/" className="bg-navyblue-300 text-white py-1.5 px-3 rounded-full text-center md:text-left">Learn more</a>
                        <a className="flex items-center justify-center md:justify-start  gap-x-1 border border-gray-200 text-black-100 py-1.5 px-3 rounded-full" href="/">
                            <PlayCircleIcon className="w-4 h-4 text-mint-500" />
                            <p>Watch how it works</p>
                        </a>
                    </div>
                </div>
            </div>
            <div className="z-10 relative mx-auto max-w-7xl bg-white shadow-stake rounded-cmd p-4 md:p-6 flex flex-col mlg:flex-row items-center justify-center gap-x-10 my-12">
                <div className="flex flex-col order-2 mlg:order-1 gap-y-4 text-black-200 pt-6">
                    <p className="w-fit rounded-full py-1 px-2 text-black-100 border border-black-100 text-sm  lxl:text-base">Hand picked projects</p>
                    <h1 className="text-3xl lxl:text-4xl">Invest in the best handpicked deals</h1>
                    <p className="text-base lxl:text-lg">Invest in property that pay you monthly or give you your returns at the end of the project</p>
                    <div className="flex w-full md:w-fit text-lg gap-x-3">
                        <a href="/" className="w-full md:text-left text-center bg-navyblue-300 text-white py-1.5 px-3 rounded-full">Learn more</a>
                    </div>
                </div>
                <div className="relative overflow-hidden flex  order-1 mlg:order-2 shrink-0  mlg:basis-1/2 items-center justify-center w-full mlg:w-[548px] h-[456px] bg-navyblue-300 rounded-cmd">
                    <img src={Prop2.src} className="absolute w-[295px] h-[590px] top-[10%]" alt="" />
                    <motion.div initial={{ opacity: 0, y: 0 }}
                        whileInView={{ opacity: 1 }} transition={{ duration: 0.5, ease: "easeIn" }} className='absolute w-[280px] h-[40px] smd:w-[320px] smd:h-[57px] bottom-[20%] md:right-[10%] md:top-[35%]'>
                        <div className="relative flex gap-x-2 items-center rounded-cmd bg-white shadow-stake py-6 px-2 smd:px-3 w-full h-full">
                            <svg width="30" height="15" viewBox="0 0 48 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.3386 14.2694C20.3386 16.8492 19.3162 19.3234 17.4962 21.1476C15.6763 22.9718 13.2079 23.9966 10.6341 23.9966C8.06037 23.9966 5.592 22.9718 3.77206 21.1476C1.95212 19.3234 0.929688 16.8492 0.929688 14.2694L0.929688 0.845967C0.945298 0.626796 1.03922 0.420614 1.19423 0.265242C1.34924 0.10987 1.55494 0.0157264 1.7736 7.91782e-05H5.57065C5.6819 -0.00145697 5.79233 0.0193733 5.89541 0.0613392C5.99849 0.103305 6.09214 0.165555 6.17081 0.244411C6.24948 0.323267 6.31159 0.417129 6.35345 0.520451C6.39532 0.623773 6.4161 0.734458 6.41457 0.845967V14.2694C6.42974 15.3875 6.88492 16.4542 7.68101 17.2375C8.07287 17.6163 8.53537 17.9141 9.04209 18.1137C9.54881 18.3133 10.0898 18.4108 10.6341 18.4007C11.1785 18.4108 11.7195 18.3133 12.2262 18.1137C12.7329 17.9141 13.1954 17.6163 13.5873 17.2375C14.3834 16.4542 14.8386 15.3875 14.8537 14.2694V0.845967C14.8522 0.734556 14.8729 0.623965 14.9147 0.52072C14.9565 0.417475 15.0185 0.323665 15.0971 0.244825C15.1756 0.165984 15.2692 0.10371 15.3721 0.0616755C15.475 0.0196407 15.5854 -0.00130318 15.6965 7.91782e-05H19.4936C19.6048 -0.00145697 19.7152 0.0193733 19.8183 0.0613392C19.9214 0.103305 20.015 0.165555 20.0937 0.244411C20.1724 0.323267 20.2345 0.417129 20.2764 0.520451C20.3182 0.623773 20.339 0.734458 20.3375 0.845967L20.3386 14.2694Z" fill="#71E5BD"></path><path d="M38.3749 17.9737C37.374 18.5285 36.2489 18.8195 35.1052 18.8195C33.9614 18.8195 32.8363 18.5285 31.8354 17.9737C30.7773 17.392 29.8955 16.5349 29.2829 15.4928C28.6704 14.4506 28.3498 13.262 28.355 12.0525C28.355 10.2299 29.0773 8.48193 30.3631 7.19315C31.6488 5.90438 33.3927 5.18035 35.2111 5.18035C37.0294 5.18035 38.7733 5.90438 40.0591 7.19315C41.3448 8.48193 42.0672 10.2299 42.0672 12.0525C42.0093 13.2738 41.6394 14.4598 40.9928 15.4966C40.3463 16.5334 39.4448 17.3864 38.3749 17.9737ZM35.1052 6.61337e-06C32.7477 6.61337e-06 30.4431 0.700712 28.483 2.01351C26.5228 3.32632 24.995 5.19225 24.0929 7.37536C23.1907 9.55847 22.9547 11.9607 23.4146 14.2783C23.8745 16.5959 25.0097 18.7247 26.6767 20.3956C28.3437 22.0665 30.4676 23.2043 32.7798 23.6653C35.0919 24.1263 37.4886 23.8897 39.6666 22.9855C41.8446 22.0812 43.7062 20.5498 45.0159 18.5851C46.3257 16.6204 47.0248 14.3104 47.0248 11.9475C47.0264 10.378 46.7192 8.82369 46.1208 7.37341C45.5224 5.92313 44.6444 4.6054 43.5373 3.49565C42.4301 2.3859 41.1154 1.50592 39.6685 0.906087C38.2216 0.306257 36.6709 -0.00164549 35.1052 6.61337e-06Z" fill="#101010"></path></svg>
                            <div className="w-full flex flex-col gap-y-0.5 text-black-100">
                                <p className="flex justify-between uppercase text-xs smd:text-sm"><span className="font-bold">uown</span><span className="text-gray-400">4:10 PM</span></p>
                                <p className="text-xs smd:text-sm">It's pay day! You just recieved £50.68</p>
                            </div>
                        </div>
                    </motion.div>
                </div>
            </div>
        </section>
    )

}

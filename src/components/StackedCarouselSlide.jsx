import React from "react";
import '../styles/stackedCarousel.scss';
import Stars from '../assets/images/home/<USER>';

export const Slide = React.memo(function (StackedCarouselSlideProps) {
  const {
    data,
    dataIndex,
    isCenterSlide,
    swipeTo,
    slideIndex
  } = StackedCarouselSlideProps;

  const text = data[dataIndex].text;
  const name = data[dataIndex].name;

  return (
    <div  className=" card-card" draggable={false}>
      <div className={`cover fill ${isCenterSlide ? "off" : "on"}`}>
        <div
          className="fill"
          onClick={() => {
            if (!isCenterSlide) swipeTo(slideIndex);
          }}
        />
      </div>
      <div className="detail fill flex flex-col p-3 md:p-6 w-full card-height">
        <div className="header flex items-center pb-12">
          <p className="text-2xl tracking-normal font-extrabold">{name}</p>
        </div>
        <p className="text-base md:text-lg tracking-wide font-regular">{text}</p>
        <img
            style={{ width: 118,height:"22px" }}
            alt="stars"
            className="mt-auto"
            src={Stars.src}
          />
      </div>
    </div>
  );
});

export default Slide;
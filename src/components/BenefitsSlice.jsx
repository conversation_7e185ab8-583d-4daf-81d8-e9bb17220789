import { motion } from 'motion/react'

import Benefit from '../assets/images/slice/benefits.webp';
import Checkmark from '../assets/images/slice/checkmark.png';
import Home from '../assets/images/slice/home.png';
import Shield from '../assets/images/slice/shield.png';
import Lock from '../assets/images/slice/lock.png';


export default function Benefits() {
    return (
        <section className='max-w-7xl mx-auto'>
            <div className='p-6 md:p-10 mlg:py-24'>
                <div className='flex flex-col justify-center items-center text-center max-w-2xl mx-auto gap-y-4 pb-20'>
                    <motion.div initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                        transition={{ duration: 0.3, ease: "easeIn" }} className='text-sm font-bold tracking-widest uppercase px-2.5 py-2 bg-mint-100 text-black-50 rounded-full'>Benefits</motion.div>
                    <motion.h2 initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                        transition={{ duration: 0.4, ease: "easeIn" }} className="text-4xl md:text-5xl">
                        More accessible and secure than going solo
                    </motion.h2>
                </div>
                <div className='flex flex-col mlg:flex-row justify-center items-center gap-x-20'>
                    <img loading="lazy" src={Benefit.src} className='mlg:order-2 flex mlg:basis-1/2 rounded-clg object-cover mlg:max-w-[500px]' alt=""/>
                    <div className='mlg:order-1 flex flex-col mlg:basis-1/2 pt-10 gap-y-10'>
                        <div className='text-lg flex items-center'>
                            <div className='flex items-center justify-center shrink-0 font-bold bg-yellow-200 w-10 h-10 text-base rounded-full mr-3'>
                                <svg width="22" height="20" viewBox="-1 -1 22 20" fill="none">
                                    <path d="M2.99993 10C2.99993 11.6484 3.66466 13.1415 4.74067 14.226C4.84445 14.3305 4.89633 14.3828 4.92696 14.4331C4.95619 14.4811 4.9732 14.5224 4.98625 14.5771C4.99993 14.6343 4.99993 14.6995 4.99993 14.8298V17.2C4.99993 17.48 4.99993 17.62 5.05443 17.727C5.10236 17.8211 5.17885 17.8976 5.27293 17.9455C5.37989 18 5.5199 18 5.79993 18H7.69993C7.97996 18 8.11997 18 8.22693 17.9455C8.32101 17.8976 8.3975 17.8211 8.44543 17.727C8.49993 17.62 8.49993 17.48 8.49993 17.2V16.8C8.49993 16.52 8.49993 16.38 8.55443 16.273C8.60236 16.1789 8.67885 16.1024 8.77293 16.0545C8.87989 16 9.0199 16 9.29993 16H10.6999C10.98 16 11.12 16 11.2269 16.0545C11.321 16.1024 11.3975 16.1789 11.4454 16.273C11.4999 16.38 11.4999 16.52 11.4999 16.8V17.2C11.4999 17.48 11.4999 17.62 11.5544 17.727C11.6024 17.8211 11.6789 17.8976 11.7729 17.9455C11.8799 18 12.0199 18 12.2999 18H14.2C14.48 18 14.62 18 14.727 17.9455C14.8211 17.8976 14.8976 17.8211 14.9455 17.727C15 17.62 15 17.48 15 17.2V16.2243C15 16.0223 15 15.9212 15.0288 15.8401C15.0563 15.7624 15.0911 15.708 15.15 15.6502C15.2114 15.59 15.3155 15.5417 15.5237 15.445C16.5059 14.989 17.344 14.2751 17.9511 13.3902C18.0579 13.2346 18.1112 13.1568 18.1683 13.1108C18.2228 13.0668 18.2717 13.0411 18.3387 13.021C18.4089 13 18.4922 13 18.6587 13H19.2C19.48 13 19.62 13 19.727 12.9455C19.8211 12.8976 19.8976 12.8211 19.9455 12.727C20 12.62 20 12.48 20 12.2V8.78575C20 8.51916 20 8.38586 19.9505 8.28303C19.9013 8.181 19.819 8.09867 19.717 8.04953C19.6141 8 19.4808 8 19.2143 8C19.0213 8 18.9248 8 18.8471 7.9738C18.7633 7.94556 18.7045 7.90798 18.6437 7.84377C18.5874 7.78422 18.5413 7.68464 18.4493 7.48547C18.1538 6.84622 17.7492 6.26777 17.2593 5.77404C17.1555 5.66945 17.1036 5.61716 17.073 5.56687C17.0437 5.51889 17.0267 5.47759 17.0137 5.42294C17 5.36567 17 5.30051 17 5.17018V4.06058C17 3.70053 17 3.52051 16.925 3.39951C16.8593 3.29351 16.7564 3.21588 16.6365 3.18184C16.4995 3.14299 16.3264 3.19245 15.9802 3.29136L13.6077 3.96922C13.5673 3.98074 13.5472 3.9865 13.5267 3.99054C13.5085 3.99414 13.4901 3.99671 13.4716 3.99826C13.4508 4 13.4298 4 13.3879 4H12.959M2.99993 10C2.99993 7.69594 4.29864 5.6952 6.20397 4.6899M2.99993 10H2C0.89543 10 0 9.10457 0 8C0 7.25972 0.402199 6.61337 1 6.26756M13 3.5C13 5.433 11.433 7 9.5 7C7.567 7 6 5.433 6 3.5C6 1.567 7.567 0 9.5 0C11.433 0 13 1.567 13 3.5Z" stroke="black" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"></path>
                                </svg>
                            </div>
                            <div className='flex flex-col gap-y-3'>
                                <div className='font-bold text-lg lxl:text-xl'>Start without the need for large sums</div>
                                <div className='text-base text-[#737373]'>Begin with just £50, instead of purchasing an entire property.</div>
                            </div>
                        </div>
                        <div className='text-lg flex items-center'>
                            <div className='flex items-center justify-center shrink-0 font-bold bg-yellow-200 w-10 h-10 text-base rounded-full mr-3'>
                                <svg width="20" height="22" viewBox="-1 -1 20 22" fill="none">
                                    <path d="M5.59 11.51L12.42 15.49M12.41 4.51L5.59 8.49M18 3C18 4.65685 16.6569 6 15 6C13.3431 6 12 4.65685 12 3C12 1.34315 13.3431 0 15 0C16.6569 0 18 1.34315 18 3ZM6 10C6 11.6569 4.65685 13 3 13C1.34315 13 0 11.6569 0 10C0 8.34315 1.34315 7 3 7C4.65685 7 6 8.34315 6 10ZM18 17C18 18.6569 16.6569 20 15 20C13.3431 20 12 18.6569 12 17C12 15.3431 13.3431 14 15 14C16.6569 14 18 15.3431 18 17Z" stroke="black" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"></path>
                                </svg>
                            </div>
                            <div className='flex flex-col gap-y-3'>
                                <div className='font-bold text-lg'>Forget about operational management</div>
                                <div className='text-base text-[#737373]'>No tenant management or unexpected site issues. We take care of it.</div>
                            </div>
                        </div>
                        <div className='text-lg flex items-center'>
                            <div className='flex items-center justify-center shrink-0 font-bold bg-yellow-200 w-10 h-10 text-base rounded-full mr-3'>
                                <svg width="22" height="20" viewBox="-1 -1 22 20" fill="none">
                                    <path d="M0 11C0 11 0.12132 11.8492 3.63604 15.364C7.15076 18.8787 12.8492 18.8787 16.364 15.364C17.6092 14.1187 18.4133 12.5993 18.7762 11M0 11V17M0 11H6M20 7C20 7 19.8787 6.15076 16.364 2.63604C12.8492 -0.87868 7.15076 -0.87868 3.63604 2.63604C2.39076 3.88131 1.58669 5.40072 1.22383 7M20 7V1M20 7H14" stroke="black" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"></path>
                                </svg>
                            </div>
                            <div className='flex flex-col gap-y-3'>
                                <div className='font-bold text-lg'>Get returns from day one</div>
                                <div className='text-base text-[#737373]'>Many projects pay out monthly, so you don't have to wait around.</div>
                            </div>
                        </div>
                        <div className='text-lg flex items-center'>
                            <div className='flex items-center justify-center shrink-0 font-bold bg-yellow-200 w-10 h-10 text-base rounded-full mr-3'>
                                <svg width="22" height="22" viewBox="-1 -1 22 22" fill="none">
                                    <path d="M6.3767 13.6172L0.719849 19.274M9.69439 4.64267L8.1335 6.20356C8.00618 6.33088 7.94252 6.39454 7.86999 6.44513C7.80561 6.49003 7.73616 6.52719 7.66309 6.55585C7.58077 6.58814 7.49249 6.6058 7.31592 6.64111L3.65145 7.37401C2.69915 7.56447 2.223 7.6597 2.00024 7.91075C1.80617 8.12946 1.71755 8.42215 1.75771 8.71178C1.8038 9.04423 2.14715 9.38759 2.83387 10.0743L9.91961 17.16C10.6063 17.8468 10.9497 18.1901 11.2821 18.2362C11.5718 18.2764 11.8645 18.1877 12.0832 17.9937C12.3342 17.7709 12.4294 17.2948 12.6199 16.3425L13.3528 12.678C13.3881 12.5014 13.4058 12.4131 13.4381 12.3308C13.4667 12.2578 13.5039 12.1883 13.5488 12.1239C13.5994 12.0514 13.663 11.9877 13.7904 11.8604L15.3512 10.2995C15.4326 10.2181 15.4734 10.1774 15.5181 10.1419C15.5578 10.1103 15.5999 10.0818 15.644 10.0566C15.6936 10.0283 15.7465 10.0056 15.8523 9.96026L18.3467 8.89125C19.0744 8.57938 19.4383 8.42344 19.6035 8.17146C19.7481 7.9511 19.7998 7.6826 19.7474 7.42433C19.6875 7.12899 19.4076 6.84907 18.8478 6.28925L13.7047 1.14611C13.1448 0.586286 12.8649 0.306372 12.5696 0.246501C12.3113 0.194145 12.0428 0.245853 11.8225 0.390385C11.5705 0.555662 11.4145 0.919512 11.1027 1.64721L10.0337 4.14157C9.9883 4.24739 9.96563 4.30029 9.93728 4.34991C9.9121 4.39398 9.88361 4.43607 9.85204 4.47582C9.8165 4.52056 9.7758 4.56126 9.69439 4.64267Z" stroke="black" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"></path>
                                </svg>
                            </div>
                            <div className='flex flex-col gap-y-3'>
                                <div className='font-bold text-lg'>Benefit from diversification</div>
                                <div className='text-base text-[#737373]'>Avoid concentrating your capital in a single project.</div>
                            </div>
                        </div>
                        <button type="button" className="mt-10 btn-black max-w-[200px] lxl:text-xl rounded-full px-4 py-2.5 text-lg font-bold text-white shadow-sm transition duration-150 ease-in hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-mint-300">Get Started</button>
                    </div>
                </div>
                <div className='grid lg:grid-cols-4 md:grid-cols-2 grid-cols-1 gap-6 pt-40'>
                    <div className='flex flex-col justify-between items-start rounded-clg bg-yellow-10 p-6 h-[338px]'>
                        <img loading="lazy" src={Home.src} className='h-[97px]'  alt="home" />
                        <div className='flex flex-col gap-y-3'>
                            <div className='text-2xl'>Impact</div>
                            <div className='text-base text-[#737373]'>We want to enable everyone to build wealth through real estate, not just the rich.</div>
                        </div>
                    </div>
                    <div className='flex flex-col justify-between items-start rounded-clg bg-yellow-10 p-6 h-[338px]'>
                        <img loading="lazy" src={Shield.src} className='h-[97px]'  alt="shield" />
                        <div className='flex flex-col gap-y-3'>
                            <div className='text-2xl'>Transparency</div>
                            <div className='text-base text-[#737373]'>We operate with utmost transparency with our investors. No surprises.</div>
                        </div>
                    </div>
                    <div className='flex flex-col justify-between items-start rounded-clg bg-yellow-10 p-6 h-[338px]'>
                        <img loading="lazy" src={Lock.src} className='h-[97px]'  alt="lock"/>
                        <div className='flex flex-col gap-y-3'>
                            <div className='text-2xl'>Simplicity</div>
                            <div className='text-base text-[#737373] pr-4'>We focus our energies on making the complex world of property investment simple.</div>
                        </div>
                    </div>
                    <div className='flex flex-col justify-between items-start rounded-clg bg-yellow-10 p-6 h-[338px]'>
                        <img loading="lazy" src={Checkmark.src} className='h-[97px]'  alt="checkmark"/>
                        <div className='flex flex-col gap-y-3'>
                            <div className='text-2xl'>Reliability</div>
                            <div className='text-base text-[#737373]'>We work hard to provide you with the best and safest investments.</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

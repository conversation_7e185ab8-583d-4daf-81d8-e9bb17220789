import { motion } from 'motion/react'

import ThumbsUp from '../assets/images/thumbs-up.png';


export default function GuideSent() {
    return (
        <section className='py-16 p-10 md:p-16'>
            <div className="text-black-200 flex flex-col gap-y-3 md:gap-y-6 justify-center items-center">
            <img className="w-[150px] h-[150px] md:w-[200px] md:h-[200px]" src={ThumbsUp.src} />
            <h1 className="uppercase text-4xl md:text-6xl mlg:text-8xl font-extrabold">Thank you!</h1>
            <p className="text-md md:text-xl mlg:text-4xl font-extrabold">Your guide is on it's way</p>
            <p className="text-base mlg:text-lg pb-3">We have sent the guide to your inbox.</p>
            <a href="/" className="cursor-pointer py-1.5 px-4 text-base mlg:text-lg text-white bg-mint-300 rounded-full">Go back to homepage</a>
            </div>
        </section>
    )
}

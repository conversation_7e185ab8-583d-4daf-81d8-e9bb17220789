import { motion } from 'motion/react'
import { useState } from 'react'

export default function PRSlice() {

    const [value, setValue] = useState(1000);
    const [final, setFinal] = useState(0);
    const [interest, setInterest] = useState(0);

    function compoundWithPMT(P, PMT, annualRate, year) {
        if (annualRate === 0) {
            // No interest scenario
            return P + PMT * (12 * year);
        } else {
            const n = 12; // monthly compounding
            const r = annualRate; // e.g. 0.05 for 5%
            const exponent = n * year; // total # of months over 'year' years
            const growthFactor = Math.pow(1 + r / n, exponent);

            // Standard formula: P*(1 + r/n)^(nt) + PMT(( (1 + r/n)^(n*t) - 1 ) / (r/n))
            return P * growthFactor + PMT * ((growthFactor - 1) / (r / n));
        }
    }

    function CalculateInterest(e) {
        setValue(e.target.value.toLocaleString());

        const P = Number(document.getElementById("balance").value);
        // 2. Define interest rates for each scenario
        const rUown = 0.10; // 10%
        const t = 5;


        // 3. Build dataset from year = 0..t
        const data = [];
        var uownValue = 0;
        var finalUown = 0;
        for (let year = 0; year <= t; year++) {
            // UOWN
            uownValue = compoundWithPMT(P, 0, rUown, year);
            // 4. Final results after 't' years
            data.push({
                x: `Year ${year}`,
                uown: uownValue,
            });
        }
        finalUown = data[5].uown.toFixed(2);
        setFinal(finalUown.toLocaleString());
        setInterest((finalUown - P).toLocaleString());
    }
    return (
        <section className="p-6 md:p-10 max-w-7xl mx-auto">
            <div className="flex flex-col justify-center items-center bg-yellow-50 px-3 md:px-6 mlg:px-12 py-16 rounded-clg">
                <div className='w-full flex flex-col lg:flex-row gap-x-6 lg:gap-x-10 items-center justify-between'>
                    <div className='flex flex-col lg:basis-1/3 justify-left items-left text-left gap-y-4 pb-6'>
                        <motion.h2 initial={{ opacity: 0, y: 50 }}
                            whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                            transition={{ duration: 0.4, ease: "easeIn" }} className="text-2xl md:text-4xl lxl:text-5xl text-black">
                            Calculate your potential returns
                        </motion.h2>
                        <motion.div initial={{ opacity: 0, y: 50 }}
                            whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                            transition={{ duration: 0.6, ease: "easeIn" }} className='text-base mlg:text-lg lxl:text-xl text-[#737373] mlg:max-w-lg'>
                            Whether it's building a passive income stream through monthly dividends or capitalising on property appreciation.
                        </motion.div>
                    </div>
                    <motion.div initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0, threshold: 0.99 }}
                        transition={{ duration: 0.6, ease: "easeIn" }} className='flex flex-col w-full lg:basis-2/3 rounded-clg bg-white border border-[#737373] p-10 gap-y-8'>
                        <div id="initial-balance" className='flex justify-between text-lg font-extrabold'>
                            <label className='text-sm md:text-lg'>Initial Investment</label>
                            <span>£ {value}</span>
                        </div>
                        <input className="" style={{
                            WebkitAppearance: 'none',
                            width: '100%',
                            height: '24px',
                            background: '#e8eff0',
                            outline: 'none',
                            borderRadius: '50px',
                            marginTop: '24px',
                            marginBottom: '24px',
                            position: 'relative',

                        }} id="balance" type="range" min="1000" max="100000" step="1000" value={value} onChange={CalculateInterest} />
                        <div style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center'
                        }}>
                            <div id="earned" className="text-sm md:text-lg">Your interest in 5 years</div>
                            <div className='flex font-extrabold text-2xl md:text-5xl gap-x-2'>
                                <span className="text-gray-50">£</span>
                                <span id="earned-value">{interest}</span>
                            </div>
                        </div>
                        <div id="result" className='flex justify-between text-lg pt-4'>
                            <label className='text-sm md:text-lg font-extrabold'>Future value</label>
                            <span>£ {final}</span>
                        </div>
                    </motion.div>
                </div>
            </div>
        </section>
    )
}

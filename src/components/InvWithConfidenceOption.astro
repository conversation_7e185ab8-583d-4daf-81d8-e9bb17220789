---
import <PERSON><PERSON> from "./Button.astro";

interface Props {
    textH5: string;
    textp: string;
    imgUrl: string;
    btnText: string;
    anchorUrl: string;
}

const { textH5, imgUrl, textp, btnText, anchorUrl } = Astro.props;
---

<div
    class="max-w-xs lg:max-w-md px-6 text-center rounded-cxl bg-white mx-auto py-12 shadow-light mb-12 h-[470px] lg:h-[520px] "
>
    <h5 class="text-3xl lg:text-4xl tracking-normal font-extrabold pb-12">
        {textH5}
    </h5>
    <div
        class="flex items-center justify-center h-[114px] md:h-[130px] lg:h-[150px] mx-auto"
    >
        <img src={imgUrl} alt="" />
    </div>
    <p
        class="text-base lg:text-xl tracking-normal font-regular pb-6 pt-6 md:pt-4 lg:pt-12"
    >
        {textp}
    </p>
    <a href={anchorUrl}
        ><Button
            type="button"
            color="btn-black btn-get-started"
            text={btnText}
        /></a
    >
</div>

<style scoped>
    .shadow-light {
        box-shadow: 0px 4px 4px 0px #00000040;
    }
</style>

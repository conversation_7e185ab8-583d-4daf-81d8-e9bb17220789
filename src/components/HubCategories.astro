---
import { Image } from 'astro:assets';

import Causes from "../assets/images/the-hub/hub-causes.png";
import Property from "../assets/images/the-hub/hub-property.png";
import Money from "../assets/images/the-hub/hub-money.png";
import Hl from "../assets/images/the-hub/hub-hl.png";

const anchorClass = "h-full w-full flex flex-col justify-between p-3 lg:pl-10 lg:pt-12 lg:pb-6";
const itemContainerClass = "flex shrink-0 w-[174px] h-[135px] lg:w-[389px] lg:h-[400px] mb-6 text-white rounded-cmd";
const itemTitle = "lg:text-5xl text-2xl tracking-normal font-extrabold";

---
<section class="px-3 py-24 md:pb-12 lg:pt-40">
    <p
        class="lg:text-5xl text-3xl tracking-normal font-extrabold pb-8 md:pb-12 text-center mx-auto"
    >
    Take a look at our other articles
    </p>
    <div class="flex flex-wrap justify-center gap-x-4 md:gap-x-6 lg:px-20">

        <div class={itemContainerClass +  " bg-mint-300"}>
            <a class={anchorClass} href={Astro.url.origin + '/category/property'}>
                <p class={itemTitle}>Property</p>
                <Image class="relative right-[30px] w-[240px] hidden lg:block" src={Property} alt="" />
                <p class="lg:hidden block text-sm tracking-widest font-medium">See articles  →</p>
            </a>
        </div>
        <div class={itemContainerClass +  " bg-navyblue-300"}>
            <a class={anchorClass} href={Astro.url.origin + '/category/money'}>
                <p class={itemTitle}>Money</p>
                <Image class="relative right-[30px] w-[240px] hidden lg:block" src={Money} alt="" />
                <p class="lg:hidden block text-sm tracking-widest font-medium">See articles  →</p>
            </a>
        </div>
        <div class={itemContainerClass +  " bg-yellow-300"}>
            <a class={anchorClass} href={Astro.url.origin + '/category/home-lifestyle'}>
                <p class={itemTitle}>Home & Lifestyle</p>
                <Image class="relative right-[30px] w-[240px] hidden lg:block" src={Hl} alt="" />
                <p class="lg:hidden block text-sm tracking-widest font-medium">See articles  →</p>
            </a>
        </div>
        <div class={itemContainerClass +  " bg-salmon-200"}>
            <a class={anchorClass} href={Astro.url.origin + '/category/causes'}>
                <p class={itemTitle}>Causes</p>
                <Image class="relative right-[30px] w-[240px] hidden lg:block" src={Causes} alt="" />
                <p class="lg:hidden block text-sm tracking-widest font-medium">See articles  →</p>
            </a>
        </div>
    </div>
</section>
---
import { ViewTransitions } from "astro:transitions";

import "../styles/global.css";
import "../styles/button.scss";

import Nav from "../components/Nav.jsx";
import Banner from "../components/Banner.jsx";
import Footer from "../components/Footer.astro";
import PostHog from "../components/posthog.astro";

import OrganizationJSONLD from "../components/StructuredData/OrganizationJSONLD.astro";

interface Props {
	title: string;
	description: string;
	classes: string;
	purl: string;
}

const { title, description, classes, purl } = Astro.props;
const defaultPreviewUrl = Astro.url.origin + "/own.png";

const previewUrl = purl != null ? purl : defaultPreviewUrl;
---

<!doctype html>
<html lang="en">
	<head>
		<link rel="sitemap" href="/sitemap-index.xml" />
		<meta charset="UTF-8" />
		<meta name="title" content={title} />
		<meta property="og:title" content={title} />
		<meta name="description" content={description} />
		<meta property="og:description" content={description} />
		<meta name="viewport" content="width=device-width" />
		<meta property="og:image" content={previewUrl} />
		<meta property="og:image:secure_url" content={previewUrl} />
		<meta property="og:image:url" content={previewUrl} />
		<link rel="canonical" href={Astro.url.href} />
		<link rel="icon" type="image/x-icon" href="/favicon.ico" />
		<meta name="generator" content={Astro.generator} />
		<title>{title}</title>
		<script is:inline>
			window.dataLayer = window.dataLayer || [];
			function gtag() {
				dataLayer.push(arguments);
			}

			// Google Tag Manager TOP OF HEAD TAG
			(function (w, d, s, l, i) {
				w[l] = w[l] || [];
				w[l].push({
					"gtm.start": new Date().getTime(),
					event: "gtm.js",
				});
				var f = d.getElementsByTagName(s)[0],
					j = d.createElement(s),
					dl = l != "dataLayer" ? "&l=" + l : "";
				j.async = true;
				j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
				f.parentNode.insertBefore(j, f);
			})(window, document, "script", "dataLayer", "GTM-WXD5GDR");
		</script>
		<!-- End Google Tag Manager -->
		<!-- Structured Data slot -->
		<slot name="structuredData" />
		<OrganizationJSONLD />
		<PostHog />
		<ViewTransitions />
	</head>
	<body class={classes}>
		<noscript>
			<iframe
				src="https://www.googletagmanager.com/ns.html?id=GTM-WXD5GDR"
				height="0"
				width="0"
				style="display:none;visibility:hidden"
			>
			</iframe>
		</noscript>
		<Nav client:load />
		<Banner client:load />
		<slot />
		<Footer />
		<slot name="addJs" />
		<slot name="addJs2" />
	</body>
</html>
<style is:global>
	:root {
		--accent: 136, 58, 234;
		--accent-light: 224, 204, 250;
		--accent-dark: 49, 10, 101;
		--accent-gradient: linear-gradient(
			45deg,
			rgb(var(--accent)),
			rgb(var(--accent-light)) 30%,
			white 60%
		);
	}
	html {
		font-size: 16px;
		background: #ffffff;
		background-size: 224px;
	}
	code {
		font-family:
			Menlo,
			Monaco,
			Lucida Console,
			Liberation Mono,
			DejaV cxjmc u Sans Mono,
			Bitstream Vera Sans Mono,
			Courier New,
			monospace;
	}
</style>

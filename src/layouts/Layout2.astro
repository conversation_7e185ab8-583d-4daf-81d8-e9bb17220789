---
import { ViewTransitions } from "astro:transitions";

import "../styles/global.css";
import "../styles/button.scss";

import Footer from "../components/Footer.astro";
import PostHog from "../components/posthog.astro";
import OrganizationJSONLD from "../components/StructuredData/OrganizationJSONLD.astro";

interface Props {
	title: string;
	description: string;
	classes: string;
}

const { title, description, classes } = Astro.props;
---

<!doctype html>
<html lang="en">
	<head>
		<link rel="sitemap" href="/sitemap-index.xml" />
		<meta charset="UTF-8" />
		<meta name="description" content={description} />
		<meta name="viewport" content="width=device-width" />
		<link rel="canonical" href={Astro.url.href} />
		<link rel="icon" type="image/x-icon" href="/favicon.ico" />
		<meta name="generator" content={Astro.generator} />
		<title>{title}</title>
		<script
			src="https://www.google.com/recaptcha/api.js?render=6LcoZIYpAAAAAMyNoRMU7qVWJmRde5eFgoWjh8xd"
			async
			defer></script>
		<script is:inline>
			window.dataLayer = window.dataLayer || [];
			function gtag() {
				dataLayer.push(arguments);
			}
			// Google Tag Manager TOP OF HEAD TAG
			(function (w, d, s, l, i) {
				w[l] = w[l] || [];
				w[l].push({
					"gtm.start": new Date().getTime(),
					event: "gtm.js",
				});
				var f = d.getElementsByTagName(s)[0],
					j = d.createElement(s),
					dl = l != "dataLayer" ? "&l=" + l : "";
				j.async = true;
				j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
				f.parentNode.insertBefore(j, f);
			})(window, document, "script", "dataLayer", "GTM-WXD5GDR");
		</script>
		<!-- End Google Tag Manager -->
		<!-- Structured Data slot -->
		<slot name="structuredData" />
		<OrganizationJSONLD />
		<PostHog />
		<ViewTransitions />
	</head>
	<body class={classes}>
		<noscript>
			<iframe
				src="https://www.googletagmanager.com/ns.html?id=GTM-WXD5GDR"
				height="0"
				width="0"
				style="display:none;visibility:hidden"
			>
			</iframe>
		</noscript>
		<slot />
		<Footer />
		<slot name="addJs" />
		<script is:inline>
			var allowSubmit = true;
			var emailForm = document.getElementById("recaptcha1");

			var eform = document.getElementById("email-form");

			if (eform) {
				eform.addEventListener("submit", (event) => {
					submit(event);
				});
			}
			emailForm.onclick = function (e) {
				e.preventDefault();
				if (allowSubmit == false) {
					return false;
				}
				allowSubmit = false;
				grecaptchaF("email-form");
			};

			var phoneForm = document.getElementById("recaptcha2");
			var pform = document.getElementById("phone-form");
			pform.addEventListener("submit", (event) => {
				submit(event);
			});
			phoneForm.onclick = function (e) {
				e.preventDefault();
				if (allowSubmit == false) {
					return false;
				}
				allowSubmit = false;
				grecaptchaF("phone-form");
			};

			function grecaptchaF(formID) {
				grecaptcha.ready(function () {
					grecaptcha
						.execute("6LcoZIYpAAAAAMyNoRMU7qVWJmRde5eFgoWjh8xd", {
							action: "submit",
						})
						.then(function (token) {
							fetch("/recaptcha", {
								method: "POST",
								body: JSON.stringify({ recaptcha: token }),
							})
								.then((response) => response.json())
								.then((gResponse) => {
									if (
										gResponse.success &&
										gResponse.score >= 0.9
									) {
										var form =
											document.getElementById(formID);
										form.requestSubmit();
										allowSubmit = true;
									}
								});
						});
				});
			}

			function delay(ms) {
				return new Promise((res) => setTimeout(res, ms));
			}
			async function submit(e) {
				e.preventDefault();

				var prefix = "";
				if (e.target.id == "email-form") {
					prefix = "e";
				}
				const submitbtn = document.getElementById(prefix + "submit");
				const loading = document.getElementById(prefix + "loading");
				const checkmark = document.getElementById(prefix + "checkmark");
				const ename = document.getElementById("e-name");
				const pname = document.getElementById("p-name");
				const email = document.getElementById("email");
				const number = document.getElementById("phone-number");

				if (submitbtn && loading) {
					submitbtn.style.display = "none";
					loading.style.display = "flex";
				}

				const formData = new FormData(e.target);
				const response = await fetch("/api/ArrangeCall", {
					method: "POST",
					body: formData,
				});
				const data = await response.json();
				if (
					data.message === "success" &&
					checkmark &&
					loading &&
					submitbtn
				) {
					loading.style.display = "none";
					checkmark.style.display = "flex";

					if (ename && email) {
						ename.value = "";
						email.value = "";
					}

					if (pname && number) {
						pname.value = "";
						number.value = "";
					}

					await delay(1000);
					submitbtn.style.display = "inline";
					checkmark.style.display = "none";
				} else if (
					data.message === "fail" &&
					cross &&
					loading &&
					submitbtn
				) {
					loading.style.display = "none";
					cross.style.display = "flex";
					await delay(1000);
					submitbtn.style.display = "inline";
					cross.style.display = "none";
				}
			}
		</script>
		<style is:global>
			html {
				font-size: 16px;
				background: #ffffff;
				background-size: 224px;
			}
		</style>
	</body>
</html>

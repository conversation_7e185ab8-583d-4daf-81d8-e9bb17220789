---
import Layout from "../layouts/Layout.astro";
import HeroSection from "../components/HeroSection.jsx";
import InfiniteScroll from "../components/InfiniteScrollSlice";
import SlideContainer from "../components/SlideContainer.jsx";
import SlideSection from "../components/SlideSection.jsx";
import HorizontalScroll from "../components/HorizontalScroll.jsx";
import Instructions from "../components/Instructions.jsx";
import Tabs from "../components/Tabs.jsx";
---

<Layout title="" classes="bg-framer" description="" purl="">
  <HeroSection client:visible />
  <InfiniteScroll bgClass="bg-framer" title="As seen in" client:visible />
  <SlideContainer />
  <HorizontalScroll client:visible />
  <Tabs client:load />
  <Instructions client:visible />
  <SlideSection />
</Layout>

<style scoped>
  .slideContainer {
    background: linear-gradient(
      179.99999999999997deg,
      rgba(247, 244, 242, 0) 27.325332164764404%,
      rgb(249, 247, 246) 100%
    );
  }

  .bg-hero-section {
    background: linear-gradient(
      180deg,
      #f9f7f6 0%,
      rgba(247, 244, 242, 0) 83.55469107627869%
    );
  }
</style>

import type { APIRoute } from "astro";

export const post: APIRoute = async ({ request }) => {

    const data = await request.formData();
    const r = data.get("number") as string;
    var recipient = '';

    if (r) {
        recipient = r.replace(/\s/g, '');
    }
  
  // Do something with the data, then return a success response
  const response = await fetch("https://api.d7networks.com/whatsapp/v2/send", {
    method: "POST",
    headers: {
        "Content-Type" : "application/json",
        "Accept" : "application/json",
        "Authorization" : `Bearer ${import.meta.env.WHATSAPP_AUTH_BEARER}`,
    },
    body: JSON.stringify({
        "messages": [
          {
            "originator": "+442035045678",
            "content": {
              "message_type": "TEMPLATE",
              "template": {
                "template_id": "roomzzz_image_brochure",
                "language": "en",
                "media": {
                  "media_type": "image",
                  "media_url": "https://d8nxin2dl65ja.cloudfront.net/roomzzz-glasgow/Roomzzz_Glasgow.png"
                }
              }
            },
            "recipients": [
              {
                "recipient": recipient,
                "recipient_type": "individual"
              }
            ]
          }
        ]
      })
    });
  
  return response;
};  
import type { APIRoute } from "astro";

export const post: APIRoute = async ({ request }) => {
  
  const data = await request.formData();
  const name = data.get("name");
  const email = data.get("email");
  const message = data.get("message");
  // Validate the data - you'll probably want to do more than this
  
  // Do something with the data, then return a success response
  const response = await fetch("https://niyf2wgsp6.execute-api.eu-west-1.amazonaws.com/prod", {
    method: "POST",
    body: JSON.stringify({
      "email":email,
      "name":name,
      "message":message
    }),
  });
  
  return new Response(
    JSON.stringify({
      message: "success"
    }),
    { status: 200 }
  );
};
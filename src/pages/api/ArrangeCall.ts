import type { APIRoute } from "astro";

export const post: APIRoute = async ({ request }) => {
  
  const data = await request.formData();
  
  const name = data.get("name");
  const phoneNumber = data.get("phone-number");
  const email = data.get("email");

  var xmlData = '<?xml version="1.0" encoding="UTF-8"?><data><lead><key>YRqlF9vREMrlbqmjTZJOOHntHQ9EfKuh</key><leadgroup>61560</leadgroup><site>21162</site><introducer>0</introducer>';
  if (name !== null) {
    const fullName = name.toString();

    var i = fullName.indexOf(' ');

    const firstName = fullName.substring(0, i);
    const lastName = fullName.substring(i+1);

    xmlData += '<firstname>' + firstName + '</firstname><lastname>' + lastName + '</lastname>';
  }

  if (phoneNumber !== null || phoneNumber !== "+44") {
    xmlData += '<phone1>' + phoneNumber + '</phone1>';
  }

  if (phoneNumber == "+44") {
    return new Response(
      JSON.stringify({
        message: "fail"
      }),
      { status: 500 }
    );
  }

  if (email !== null) {
    xmlData += '<email>' + email +'</email>';
  }
  
  xmlData += "<contactphone>Yes</contactphone><contactsms>Yes</contactsms><contactemail>Yes</contactemail><data2>Wicker Island</data2></lead></data>";
  // Validate the data - you'll probably want to do more than this
  
 
  // Do something with the data, then return a success response
  const response = await fetch("https://parklane.flg360.co.uk/api/APILeadCreateUpdate.php", {
    method: "POST",
    body: xmlData
  });

  if (response.status === 200) {
    return new Response(
    JSON.stringify({
      message: "success"
    }),
    { status: 200 }
  );
  }

  return new Response(
    JSON.stringify({
      message: "success"
    }),
    { status: 200 }
  );
  
};
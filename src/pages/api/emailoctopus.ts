import type { APIRoute } from "astro";

import PostHogNode from "../../lib/posthog-node.js"; 

export const POST: APIRoute = async ({ request }) => {

    const data = await request.formData();
    const firstname = data.get("firstName");
    const lastName = data.get("lastName");
    const email = data.get("email");
    const phone = data.get("phoneNo");
    const distinctid = data.get("distinctid");
    const userAgent = data.get("userAgent");
    const variant = data.get("variant");
    const gScore = data.get("gScore");
    const fbc = data.get("fbc");
    var fbclid = '';
    
    if (fbc !== '') {
        const fbcA = fbc.split('.');
        fbclid = fbcA[3];
    }
    // Validate the data - you'll probably want to do more than this

    const response = await fetch(`https://api.emailoctopus.com/lists/${import.meta.env.EMAIL_OCTOPUS_LIST_ID}/contacts`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": `Bearer ${import.meta.env.EMAIL_OCTOPUS_AUTH}`,
        },
        body: JSON.stringify({
            "email_address": email,
            "fields": {
                "FirstName": firstname,
                "LastName": lastName,
                "PhoneNumber": phone
            },
            "tags": {},
            "status": "subscribed"
        })
    })

    console.log(response);
    if (response.status == 201) {

        await PostHogNode().capture({
            distinctId: distinctid,
            event: 'Successful form submission',
            properties: {
                em: email,
                first_name: firstname,
                ln: lastName,
                distinct_id: distinctid,
                client_user_agent: userAgent,
                recaptcha_score: gScore,
                _fbc: fbc,
                fbclid: fbclid,
                '$feature/uk-investment-guide-landing-page-1': variant
            },
        });
        return new Response(
            JSON.stringify({
                message: "success"
            }),
            { status: 200 }
        );
    } else {
        return new Response(
            JSON.stringify({
                message: "error"
                }),
                { status: 500 }
                );
    }
};
---
import Layout from "../layouts/Layout.astro";

import StartJourneyCTA from "../components/StartJourneyCTA.astro";
import HeroSection from "../components/HelpHeroSection.astro";
import MoreQuestions from "../components/GotMoreQuestions.astro";
import TopicsBar from "../components/AllHelpTopicsBar.astro";
import ArticleTile from "../components/ArticleTile.astro";
import PopularArticles from "../components/PopularArticles.astro";

import {
    getHelpArticlesByTopic,
    getAllTopics,
    getFullUrl,
    getReadingTime,
    urlForImage
} from "../lib/sanity-client.ts";

const helpArticles = await getHelpArticlesByTopic(
    "8d593721-544f-48da-8204-1cd00f634900",
);

const topics = await getAllTopics();
const purl = Astro.url.origin + '/previews/help-center.png'
---

<Layout
    title="Help Centre | Support & FAQs | UOWN"
    classes=""
    description="Find answers to your questions at the UOWN Help Centre. Get support and answers about our crowdfunding and investment platform."
    purl={purl}
>
    <main>
        <HeroSection isIpadHidden={true} />
        <section class="hidden lg:block bg-gray-pages text-center">
            <p
                class="max-w-4xl lg:text-5xl md:text-4xl tracking-normal font-extrabold py-24 mx-auto"
            >
                Getting Started
            </p>
            <div class="flex px-28 pb-24 lxl:px-36 xl:px-60 ">
                <TopicsBar  isSticky={false} hasBorder={false} selectedTopic="Getting Started" />
                <div class="hidden lg:flex flex-wrap mb-auto justify-left">
                    {
                        helpArticles.map((article: any) => (
                        <ArticleTile readingTime={getReadingTime("article", article.slug.current, )} postHref={getFullUrl(Astro.url.origin, article.fullSlug)} title={article.title} sectionType="topic" />
                        ))
                    }
                </div>
            </div>
        </section>
        <section class="block lg:hidden bg-gray-pages text-center">
            <p
                class="max-w-4xl md:text-4xl text-3xl tracking-normal font-extrabold md:pt-24 md:pb-4 py-12 mx-auto"
            >
                Help Centre
            </p>
            <p
                class="max-w-4xl hidden md:block md:text-xl tracking-normal font-regular pb-24 mx-auto"
            >
            Take a look through our helpful articles?
            </p>
            <div class="flex px-7 md:px-20 pb-24">
                <div class="flex w-full lg:hidden flex-wrap mb-auto justify-center gap-x-7">
                    {
                        topics.map((topic: any) => (
                            <a
                                class="grow bg-white rounded-csm flex justify-between items-center text-left text-lg tracking-wide font-bold px-6 mb-6 w-full md:w-[306px] h-[100px] shadow-tile"
                                href={getFullUrl(Astro.url.origin, topic.full_slug)}
                            >
                                <span>{topic.name}</span>
                                <img class="max-h-[42px]" src={urlForImage(topic.icon)
                                        .url()} />
                            </a>
                        ))
                    }
                </div>
            </div>
        </section>
        <PopularArticles isHelpCentreMainPage={true} />
        <MoreQuestions isTopicPage={false} />
        <StartJourneyCTA colorClass="" />
    </main>
</Layout>
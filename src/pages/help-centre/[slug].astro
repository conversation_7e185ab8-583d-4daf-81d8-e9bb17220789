---
import Layout from "../../layouts/Layout.astro";
import Button from "../../components/Button.astro";
import HeroSection from "../../components/HelpHeroSection.astro";
import TopicsBar from "../../components/AllHelpTopicsBar.astro";
import ArticleTile from "../../components/ArticleTile.astro";
import Serializer from "../../components/BlockSerializer.jsx";

import ContactUs from "../../assets/images/help/contact-us.png";

const { slug } = Astro.params;

import {
    getRelatedHelpArticles,
    getFullUrl,
    getHelpArticle,
    getReadingTime,
    getHelpCentreTopic,
} from "../../lib/sanity-client.ts";

const helpArticle = await getHelpArticle(slug);

if (!helpArticle) {
    return Astro.redirect("/404");
}

const helpTopic = await getHelpCentreTopic(helpArticle.topic._ref);
const relatedArticles = await getRelatedHelpArticles(
    helpArticle.topic._ref,
    slug,
);

const purl = Astro.url.origin + '/previews/help-center.png'
---

<Layout
    title="Help Centre | Support & FAQs | UOWN"
    classes=""
    description={helpArticle.seoDescription}
    purl={purl}
>
    <main class="">
        <section class="pb-6">
            <div class="text-center max-w-7xl mx-auto">
                <p
                    class="hidden md:block max-w-4xl lg:text-5xl md:text-4xl text-3xl tracking-normal font-extrabold py-24 mx-auto"
                >
                    {helpTopic.name}
                </p>
                <div class="flex px-6 md:px-20 lg:px-36 pb-12 pt-12 md:pt-0 lg:pb-40 gap-x-10">
                    <TopicsBar
                        isSticky={true}
                        hasBorder={true}
                        selectedTopic={helpTopic.name}
                    />
                    <div
                        class="flex flex-col justify-left mb-auto lg:max-w-[78%] gap-y-6"
                    >
                        <div class="text-left text-lg lg:text-2xl font-bold">
                            {helpArticle.title}
                        </div>
                        <Serializer client:load post={helpArticle} />
                    </div>
                </div>
            </div>
        </section>
        <section
            class="text-left px-6 md:px-20 lg:px-36 pb-24 pt-12 md:pt-24 lg:pb-40 bg-gray"
        >
            <div class="max-w-7xl mx-auto">
                <p
                    class="hidden lg:block max-w-4xl text-3xl tracking-normal font-extrabold pb-24"
                >
                    Related help articles:
                </p>
                <p
                    class="lg:hidden max-w-4xl text-2xl tracking-normal font-extrabold pb-12 mx-auto"
                >
                    Take a look at some of our other articles:
                </p>
                <div class="flex flex-wrap justify-left pb-12">
                    {
                        relatedArticles.map((relatedArticle: any) => (
                            <ArticleTile
                                readingTime={getReadingTime(
                                    "article",
                                    relatedArticle.slug.current,
                                )}
                                postHref={getFullUrl(
                                    Astro.url.origin,
                                    relatedArticle.fullSlug,
                                )}
                                title={relatedArticle.title}
                                sectionType="topic"
                            />
                        ))
                    }
                </div>
                <div
                    class="flex flex-col md:flex-row items-center justify-between w-full md:h-[100px] bg-mint-500 rounded-cmd text-white py-6 px-5 lg:px-12"
                >
                    <div class="flex flex-col md:flex-row items-center">
                        <img
                            class="md:mr-5 lg:mr-14 mb-8 md:mb-0 md:h-[60px] md:w-[60px]"
                            src={ContactUs.src}
                            width="100"
                            height="100"
                            alt=""
                        />
                        <div
                            class="lg:text-2xl text-xl tracking-normal font-bold pb-6 md:pb-0 text-center"
                        >
                            Have more questions?
                            <p class="lg:text-xl text-base font-light">
                                Don't hesitate to get in touch
                            </p>
                        </div>
                    </div>

                    <a href="https://app.uown.co/contact"
                        ><Button
                            type="button"
                            color="btn-white btn-email"
                            text="Email us"
                        /></a
                    >
                </div>
            </div>
        </section>
    </main>
</Layout>

<style scoped>
    .hero {
        background-image: url("/assets/images/help/help_main_image.png");
        height: 63vh;
    }
</style>

---
import { getHubPost } from "../../lib/sanity-client.ts";
import Serializer from "../../components/BlockSerializer.jsx";
import H2Serializer from "../../components/H2Serializer.jsx";
import SocialShareIcon from "../../components/SocialShare.jsx";
import TheHubJSONLD from "../../components/StructuredData/TheHubJSONLD.astro";

import Layout from "../../layouts/Layout.astro";
import Button from "../../components/Button.astro";
import TogetherWeAchieveCTA from "../../components/TogetherWeAchieveCTA.astro";
import HubCategories from "../../components/HubCategories.astro";

const { slug } = Astro.params;

const hubPost = await getHubPost(slug);

if (!hubPost) {
    return Astro.redirect("/404");
}

const title = `${hubPost?.title} - UOWN`;
const heading = hubPost.title;
const summary = hubPost?.summary || "";
const author = hubPost?.author.name || "";
const category = hubPost?.category || "";
const publishedAt = hubPost?.firstPublishedAt || "";

const formattedPublishDate = new Date(publishedAt).toLocaleDateString("en-US", {
    year: "2-digit",
    month: "numeric",
    day: "numeric",
});

const purl = Astro.url.origin + '/previews/hub.png'
---

<Layout
    title={title}
    classes=""
    description="Stay updated with UOWN's investment news and articles. Visit The Hub for the latest insights."
    purl={purl}
>
<TheHubJSONLD title={title} summary={summary} publishedAt={publishedAt} author={author} category={category} slot="structuredData" />
    <main>
        <section
            class="flex lg:flex-row flex-col lg:justify-around lg:items-center justify-start text-left lg:px-20 md:px-16 px-10 lg:py-64 md:pt-40 pt-24 lg:gap-x-20"
        >
            <div class="max-w-2xl lg:mx-auto">
                <p
                    class="lg:text-5xl text-3xl tracking-normal font-extrabold pb-6"
                >
                    {heading}
                </p>
                <p
                    class="lg:text-2xl text-lg tracking-normal font-regular pb-4"
                >
                    {summary}
                </p>
                <p class="lg:text-xl text-lg tracking-wide font-bold">
                    By {author}
                    <span class="text-gray-100 font-light pl-3">
                        {formattedPublishDate}</span
                    >
                </p>
            </div>
            <img
                class="block lg:py-0 lg:mx-auto lg:max-w-[500px] max-w-[339px] py-12"
                src={category.mainImage}
                alt=""
            />
        </section>
        <section
            class="flex justify-start lg:justify-center lg:mx-20 lg:gap-x-20 lg:px-0 md:px-16 px-10"
        >
            <div class="lg:max-w-5xl max-w-3xl">
                <!-- {JSON.stringify(hubPost)} -->
                <Serializer client:load post={hubPost} />
            </div>
            <aside class="hidden lg:block">
                <div
                    class="flex flex-col shrink-0 w-[389px] sticky top-44 p-6 rounded-cmd border-blk"
                >
                    <p class="text-2xl tracking-normal font-extrabold pb-6">
                        Article Contents
                    </p>
                    <div><H2Serializer client:load post={hubPost} /></div>
                    <div class="flex gap-x-2 py-12">
                        <!-- <SocialShareIcon client:load  /> -->
                    </div>
                    <a href="https://app.uown.co/signup"
                        ><Button
                            type="button"
                            color="btn-black w-full"
                            text="Get Investing"
                        /></a
                    >
                </div>
            </aside>
            </section>
            <HubCategories />
            <TogetherWeAchieveCTA colorClass="bg-grey-gradient" />
    </main>

    <style scoped></style>
</Layout>

---
import Layout from "../layouts/Layout.astro";
---

<Layout
    title="Track Record | UOWN"
    classes="font-sans bg-white text-black"
    description="Our successfulreal estate developments across various regions."
>
    <section class="px-4 sm:px-6 lg:px-20 py-12" x-data="tableData">
        <h2 class="text-3xl font-bold mb-2">Our Track Record</h2>
        <p class="text-gray-200 mb-6 max-w-3xl">
            At Evergreen Properties, we pride ourselves on a history of
            successful developments. Our commitment to quality and innovation is
            reflected in every project we undertake.
        </p>

        <div
            class="mb-4 flex flex-col md:flex-row md:items-center md:justify-between gap-2"
        >
            <input
                x-model="search"
                type="text"
                placeholder="Search projects..."
                class="border border-gray-100 rounded-md px-4 py-2 text-sm w-full md:w-1/3"
            />
            <div class="text-sm text-gray-200">Click on headers to sort</div>
        </div>

        <div class="w-full overflow-x-auto">
            <div
                class="overflow-hidden rounded-cmd border border-gray-100 shadow-md"
            >
                <table class="min-w-full text-sm" x-init="init()">
                    <thead class="bg-gray-10 text-left cursor-pointer">
                        <tr>
                            <th
                                @click="sortKey = 'Project Name'; sortAsc = !sortAsc"
                                class="p-4 font-semibold">Project Name</th
                            >
                            <th
                                @click="sortKey = 'Type'; sortAsc = !sortAsc"
                                class="p-4 font-semibold hidden sm:table-cell"
                                >Type</th
                            >
                            <th
                                @click="sortKey = 'Funds Raised'; sortAsc = !sortAsc"
                                class="p-4 font-semibold hidden md:table-cell"
                                >Funds Raised</th
                            >
                            <th
                                @click="sortKey = 'GDV'; sortAsc = !sortAsc"
                                class="p-4 font-semibold hidden md:table-cell"
                                >GDV</th
                            >
                            <th
                                @click="sortKey = 'Achieved Return'; sortAsc = !sortAsc"
                                class="p-4 font-semibold">Achieved Return</th
                            >
                            <th
                                @click="sortKey = 'Achieved Annual Return'; sortAsc = !sortAsc"
                                class="p-4 font-semibold hidden lg:table-cell"
                                >Achieved Annual Return</th
                            >
                            <th
                                @click="sortKey = 'Completion Date'; sortAsc = !sortAsc"
                                class="p-4 font-semibold hidden lg:table-cell"
                                >Completion Date</th
                            >
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-50">
                        <template
                            x-for="row in filteredRows"
                            :key="row['Project Name']"
                        >
                            <tr class="hover:bg-gray-10">
                                <td class="p-4" x-text="row['Project Name']"
                                ></td>
                                <td
                                    class="p-4 hidden sm:table-cell"
                                    x-text="row['Type']"></td>
                                <td
                                    class="p-4 hidden md:table-cell"
                                    x-text="row['Funds Raised']"></td>
                                <td
                                    class="p-4 hidden md:table-cell"
                                    x-text="row['GDV']"></td>
                                <td
                                    :class="highlightCell('Achieved Return', row)"
                                    class="p-4"
                                    x-text="row['Achieved Return']"></td>
                                <td
                                    :class="highlightCell('Achieved Annual Return', row)"
                                    class="p-4 hidden lg:table-cell"
                                    x-text="row['Achieved Annual Return']"></td>
                                <td
                                    class="p-4 hidden lg:table-cell"
                                    x-text="row['Completion Date']"></td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <script is:inline>
        document.addEventListener("alpine:init", () => {
            Alpine.data("tableData", () => ({
                headers: [
                    "Project Name",
                    "Type",
                    "Funds Raised",
                    "GDV",
                    "Projected Return",
                    "Achieved Return",
                    "Projected Length",
                    "Achieved Length",
                    "Completion Date",
                    "Projected Annual Return",
                    "Achieved Annual Return",
                ],
                sortKey: "Project Name",
                sortAsc: true,
                search: "",
                rows: [],
                get filteredRows() {
                    let filtered = this.rows.filter((row) =>
                        Object.values(row).some((val) =>
                            val
                                .toLowerCase()
                                .includes(this.search.toLowerCase()),
                        ),
                    );
                    return filtered.sort((a, b) => {
                        let valA = a[this.sortKey] || "",
                            valB = b[this.sortKey] || "";
                        return this.sortAsc
                            ? valA.localeCompare(valB)
                            : valB.localeCompare(valA);
                    });
                },
                highlightCell(key, row) {
                    const keys = ["Achieved Return", "Achieved Annual Return"];
                    if (keys.includes(key)) {
                        const achieved = parseFloat(row[key]) || 0;
                        const projected =
                            parseFloat(
                                row[key.replace("Achieved", "Projected")],
                            ) || 0;
                        if (achieved >= projected) return "bg-mint-100";
                    }
                    return "";
                },
                init() {
                    this.rows = [
                        {
                            "Project Name": "Eden Gardens",
                            Type: "Buy-to-let",
                            "Funds Raised": "£272,149",
                            GDV: "£230,000",
                            "Projected Return": "0",
                            "Achieved Return": "54.10%",
                            "Projected Length": "100",
                            "Achieved Length": "53",
                            "Completion Date": "Jul-2021",
                            "Projected Annual Return": "5.00%",
                            "Achieved Annual Return": "12.25%",
                        },
                        {
                            "Project Name": "Stanmore Street",
                            Type: "Buy-to-let",
                            "Funds Raised": "£485,510",
                            GDV: "£319,950",
                            "Projected Return": "0",
                            "Achieved Return": "48.92%",
                            "Projected Length": "100",
                            "Achieved Length": "52",
                            "Completion Date": "Jun-2021",
                            "Projected Annual Return": "5.00%",
                            "Achieved Annual Return": "11.29%",
                        },
                        {
                            "Project Name": "Beechwood Mount",
                            Type: "Buy-to-let",
                            "Funds Raised": "£287,150",
                            GDV: "£250,000",
                            "Projected Return": "0",
                            "Achieved Return": "71.15%",
                            "Projected Length": "100",
                            "Achieved Length": "46",
                            "Completion Date": "May-2020",
                            "Projected Annual Return": "5.00%",
                            "Achieved Annual Return": "18.56%",
                        },
                        {
                            "Project Name": "Lumley Avenue",
                            Type: "Buy-to-let",
                            "Funds Raised": "£335,938",
                            GDV: "£330,000",
                            "Projected Return": "0",
                            "Achieved Return": "15.33%",
                            "Projected Length": "100",
                            "Achieved Length": "23",
                            "Completion Date": "Jun-2021",
                            "Projected Annual Return": "5.00%",
                            "Achieved Annual Return": "8.00%",
                        },
                        {
                            "Project Name": "Village Street",
                            Type: "Development",
                            "Funds Raised": "£65,188",
                            GDV: "£310,000",
                            "Projected Return": "14.99%",
                            "Achieved Return": "21.22%",
                            "Projected Length": "6",
                            "Achieved Length": "7",
                            "Completion Date": "Aug-2019",
                            "Projected Annual Return": "29.98%",
                            "Achieved Annual Return": "36.38%",
                        },
                        {
                            "Project Name": "The Bakery",
                            Type: "Development",
                            "Funds Raised": "£400,000",
                            GDV: "£1,500,000",
                            "Projected Return": "15.00%",
                            "Achieved Return": "15.00%",
                            "Projected Length": "11",
                            "Achieved Length": "22",
                            "Completion Date": "Apr-2021",
                            "Projected Annual Return": "16.36%",
                            "Achieved Annual Return": "8.18%",
                        },
                        {
                            "Project Name": "Burley Refurbs",
                            Type: "Development",
                            "Funds Raised": "£105,000",
                            GDV: "£585,000",
                            "Projected Return": "14.10%",
                            "Achieved Return": "14.10%",
                            "Projected Length": "11",
                            "Achieved Length": "16",
                            "Completion Date": "Oct-2020",
                            "Projected Annual Return": "15.38%",
                            "Achieved Annual Return": "10.58%",
                        },
                        {
                            "Project Name": "Harold Place",
                            Type: "Development",
                            "Funds Raised": "£470,600",
                            GDV: "£643,500",
                            "Projected Return": "14.05%",
                            "Achieved Return": "15.10%",
                            "Projected Length": "12",
                            "Achieved Length": "18",
                            "Completion Date": "Sep-2021",
                            "Projected Annual Return": "14.05%",
                            "Achieved Annual Return": "10.07%",
                        },
                        {
                            "Project Name": "Holbeck Haus",
                            Type: "Development",
                            "Funds Raised": "£200,000",
                            GDV: "£2,086,359",
                            "Projected Return": "12.96%",
                            "Achieved Return": "12.96%",
                            "Projected Length": "6",
                            "Achieved Length": "15",
                            "Completion Date": "Jun-2021",
                            "Projected Annual Return": "25.92%",
                            "Achieved Annual Return": "10.37%",
                        },
                        {
                            "Project Name": "Todson House Phase One",
                            Type: "Development Loan",
                            "Funds Raised": "£250,000",
                            GDV: "£4,716,400",
                            "Projected Return": "27.50%",
                            "Achieved Return": "25.67%",
                            "Projected Length": "30",
                            "Achieved Length": "28",
                            "Completion Date": "Aug-2023",
                            "Projected Annual Return": "11.00%",
                            "Achieved Annual Return": "11.00%",
                        },
                        {
                            "Project Name": "Todson House Phase Two",
                            Type: "Development Loan",
                            "Funds Raised": "£500,000",
                            GDV: "£4,716,400",
                            "Projected Return": "24.17%",
                            "Achieved Return": "22.50%",
                            "Projected Length": "29",
                            "Achieved Length": "27",
                            "Completion Date": "Aug-2023",
                            "Projected Annual Return": "10.00%",
                            "Achieved Annual Return": "10.00%",
                        },
                        {
                            "Project Name": "Todson House Phase Three",
                            Type: "Development Loan",
                            "Funds Raised": "£250,000",
                            GDV: "£4,716,400",
                            "Projected Return": "20.00%",
                            "Achieved Return": "20.67%",
                            "Projected Length": "30",
                            "Achieved Length": "31",
                            "Completion Date": "Apr-2024",
                            "Projected Annual Return": "8.00%",
                            "Achieved Annual Return": "8.00%",
                        },
                        {
                            "Project Name": "Todson House Phase Four",
                            Type: "Development Loan",
                            "Funds Raised": "£300,000",
                            GDV: "£4,716,400",
                            "Projected Return": "18.67%",
                            "Achieved Return": "19.33%",
                            "Projected Length": "28",
                            "Achieved Length": "29",
                            "Completion Date": "Apr-2024",
                            "Projected Annual Return": "8.00%",
                            "Achieved Annual Return": "8.00%",
                        },
                        {
                            "Project Name": "Burley Refurbs 2",
                            Type: "Development Loan",
                            "Funds Raised": "£160,000",
                            GDV: "£690,000",
                            "Projected Return": "10.00%",
                            "Achieved Return": "12.00%",
                            "Projected Length": "10",
                            "Achieved Length": "12",
                            "Completion Date": "Jul-2023",
                            "Projected Annual Return": "12.00%",
                            "Achieved Annual Return": "12.00%",
                        },
                        {
                            "Project Name": "West London Coliving Conversion",
                            Type: "Development Loan",
                            "Funds Raised": "£650,000",
                            GDV: "£69,735,839",
                            "Projected Return": "20.00%",
                            "Achieved Return": "NA",
                            "Projected Length": "20",
                            "Achieved Length": "",
                            "Completion Date": "",
                            "Projected Annual Return": "12.00%",
                            "Achieved Annual Return": "",
                        },
                        {
                            "Project Name": "West London Coliving 2",
                            Type: "Development Loan",
                            "Funds Raised": "£250,000",
                            GDV: "£69,735,839",
                            "Projected Return": "15.00%",
                            "Achieved Return": "NA",
                            "Projected Length": "20",
                            "Achieved Length": "",
                            "Completion Date": "",
                            "Projected Annual Return": "9.00%",
                            "Achieved Annual Return": "",
                        },
                        {
                            "Project Name": "Ainscow",
                            Type: "Development Loan",
                            "Funds Raised": "£500,000",
                            GDV: "£9,000,000",
                            "Projected Return": "20.00%",
                            "Achieved Return": "NA",
                            "Projected Length": "24",
                            "Achieved Length": "",
                            "Completion Date": "",
                            "Projected Annual Return": "10.00%",
                            "Achieved Annual Return": "",
                        },
                        {
                            "Project Name": "Wicker Island BTR",
                            Type: "Development Loan",
                            "Funds Raised": "£1,000,000",
                            GDV: "£21,053,400",
                            "Projected Return": "26.00%",
                            "Achieved Return": "NA",
                            "Projected Length": "34",
                            "Achieved Length": "",
                            "Completion Date": "",
                            "Projected Annual Return": "9.18%",
                            "Achieved Annual Return": "",
                        },
                        {
                            "Project Name": "Ainscow 2",
                            Type: "Development Loan",
                            "Funds Raised": "£450,000",
                            GDV: "£9,000,000",
                            "Projected Return": "20.00%",
                            "Achieved Return": "NA",
                            "Projected Length": "24",
                            "Achieved Length": "",
                            "Completion Date": "",
                            "Projected Annual Return": "10.00%",
                            "Achieved Annual Return": "",
                        },
                        {
                            "Project Name": "Wicker Island 2",
                            Type: "Development Loan",
                            "Funds Raised": "£500,000",
                            GDV: "£21,053,400",
                            "Projected Return": "26.00%",
                            "Achieved Return": "NA",
                            "Projected Length": "26",
                            "Achieved Length": "",
                            "Completion Date": "",
                            "Projected Annual Return": "12.00%",
                            "Achieved Annual Return": "",
                        },
                    ];
                },
            }));
        });
    </script>
    <script
        defer
        src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"
    ></script>
</Layout>

---
import Layout from "../../layouts/Layout.astro";
import StartJourneyCTA from "../../components/StartJourneyCTA.astro";
import PostTile from "../../components/PostTile.astro";

import { getCategoryPosts, urlForImage, getFullUrl, getReadingTime } from "../../lib/sanity-client.ts";

const categoryPosts = await getCategoryPosts('e17e0246-9f28-4830-b7f3-1b3385557704');


const purl = Astro.url.origin + '/previews/causes.png'
---

<Layout
	title="Property Crowdfunding for Good Causes | The Hub | UOWN"
	classes=""
	description="Discover how UOWN supports various causes through crowdfunding. Make a difference with your money."
	purl={purl}
>
	<main>
		<section
			class="hero bg-no-repeat bg-cover bg-salmon-200 flex flex-col justify-center items-center text-center text-white lg:px-0 px-11"
		>
			<p
				class="max-w-4xl lg:text-7xl md:text-6xl text-5xl tracking-normal font-extrabold pb-4"
			>
				Causes Hub
			</p>
			<p
				class="max-w-2xl md:text-2xl text-xl tracking-normal font-regular pb-12"
			>
				The Hub is our knowledge center featuring useful and
				inspirational articles.
			</p>
		</section>
		<section class="flex flex-wrap gap-x-7 justify-start md:pt-12 lg:pt-24 lg:px-32 md:px-16 px-8 pb-24 pt-8">
			{categoryPosts.map((post: any) => (
				<PostTile readingTime={getReadingTime("post", post.slug.current)} postHref={getFullUrl(Astro.url.origin, post.fullSlug)} imgSrc={urlForImage(post.thumbnail).width(350).height(200).url()} title={post.title} color="border-salmon-300" />
			   ))}
		</section>
		<StartJourneyCTA colorClass="bg-salmon-gradient" />
	</main>
</Layout>

<style scoped>
	.hero {
		background-image: url("/src/assets/images/category/causes_main_image.png");
		height: 63vh;
	}
</style>

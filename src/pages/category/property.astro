---
import Layout from "../../layouts/Layout.astro";
import StartJourneyCTA from "../../components/StartJourneyCTA.astro";
import PostTile from "../../components/PostTile.astro";

import { getCategoryPosts, urlForImage, getFullUrl, getReadingTime } from "../../lib/sanity-client.ts";

const categoryPosts = await getCategoryPosts('0a5d1c34-4085-4fab-83e7-19c4884a1251');

const purl = Astro.url.origin + '/previews/property.png'
---

<Layout
	title="Property Insights | The Hub | UOWN"
	classes=""
	description="Explore property insights and investment opportunities with UOWN. Start your property crowdfunding journey today."
	purl={purl}
>
	<main>
		<section
			class="bg-mint-300 hero bg-no-repeat bg-cover flex flex-col justify-center items-center text-center lg:px-0 px-11 text-white"
		>
			<p
				class="max-w-4xl lg:text-7xl md:text-6xl text-5xl tracking-normal font-extrabold pb-4"
			>
				The Property Hub
			</p>
			<p
				class="max-w-2xl md:text-2xl text-xl tracking-normal font-regular pb-12"
			>
				The Hub is our knowledge center featuring useful and
				inspirational articles.
			</p>
		</section>
		<section class="flex flex-wrap gap-x-7 justify-center md:pt-12 lg:pt-24 lg:px-32 md:px-16 px-8 pb-24 pt-8">
			{categoryPosts.map((post: any) => (
				<PostTile readingTime={getReadingTime("post", post.slug.current)} postHref={getFullUrl(Astro.url.origin, post.fullSlug)} imgSrc={urlForImage(post.thumbnail).width(350).height(200).url()} title={post.title} color="border-mint-300" />
			   ))}
		</section>
		<StartJourneyCTA colorClass="bg-mint-gradient" />
	</main>
</Layout>

<style scoped>
	.hero {
		background-image: url("/src/assets/images/category/property_main_image.png");
		height: 63vh;
	}
</style>

import type { APIRoute } from 'astro';

const robotsTxt = `
User-agent: *

Disallow: /wicker-island-2
Disallow: /demoPage3
Disallow: /demoPage2
Disallow: /demoPage
Disallow: /guide-sent

Allow: /

Sitemap: ${new URL('sitemap-index.xml', import.meta.env.SITE).href}
`.trim();

export const GET: APIRoute = () => {
  return new Response(robotsTxt, {
    headers: {
      'Content-Type': 'text/plain; charset=utf-8',
    },
  });
};
---
import Layout from "../layouts/Layout.astro";
import UnleashThePowerCTA from "../components/UnleashThePowerCTA.astro";
import PostTile from "../components/PostTile.astro";
import HubCard from "../components/HubCard.astro";

import {
    getCategoryPostsWithLimit,
    urlForImage,
    getFullUrl,
    getReadingTime,
} from "../lib/sanity-client.ts";

import SphereWhite from "../assets/images/the-hub/img-large-sphere-white.png";
import SphereMetal from "../assets/images/the-hub/img-large-sphere-metal.png";
import CylinderWood from "../assets/images/the-hub/img-large-cylinder-wood.png";

import Causes from "../assets/images/the-hub/hub-causes.png";
import Property from "../assets/images/the-hub/hub-property.png";
import Money from "../assets/images/the-hub/hub-money.png";
import Hl from "../assets/images/the-hub/hub-hl.png";

const propertyPosts = await getCategoryPostsWithLimit(
    "0a5d1c34-4085-4fab-83e7-19c4884a1251",
);
const moneyPosts = await getCategoryPostsWithLimit(
    "7ce6209c-ea5c-4081-95b5-b7b0fa1133e1",
);
const hlPosts = await getCategoryPostsWithLimit(
    "faba8071-9de2-408f-8529-e412ce8ed4c2",
);
const causesPosts = await getCategoryPostsWithLimit(
    "e17e0246-9f28-4830-b7f3-1b3385557704",
);

const itemContainerClass =
    "hidden md:flex justify-start gap-x-7 overflow-x-scroll hide-scrollbar [&>*:first-child]:ml-7";

const purl = Astro.url.origin + '/previews/hub.png';
---

<Layout
    title="Investment News & Articles | UOWN | The Hub"
    classes=""
    description="Stay updated with UOWN's investment news and articles. Visit The Hub for the latest insights."
    purl={purl}
>
    <main>
        <section
            class="relative hero bg-no-repeat bg-cover bg-bottom flex flex-col justify-center items-center text-center overflow-hidden lg:px-0 px-11 py-40 lg:py-64"
        >
            <img
                class="absolute z-0 w-[116px] md:w-[153px] lg:w-[200px] top-[65%] left-[74%]"
                width="486"
                height="384"
                src={SphereWhite.src}
                alt=""
            />
            <img
                class="absolute z-0 w-[70px] md:w-[93px] lg:w-[119px] top-[10%] left-[62%]"
                width="121"
                height="100"
                src={SphereMetal.src}
                alt=""
            />
            <img
                class="absolute z-0 w-[128px] md:w-[176px] lg:w-[224px] top-[60%] right-[75%] lg:right-[75%] md:right-[90%] md:top-[45%]"
                width="354"
                height="273"
                src={CylinderWood.src}
                alt=""
            />
            <p
                class="opacity-0 heading max-w-4xl md:text-5xl text-4xl tracking-normal font-extrabold pb-4"
            >
                The Hub
            </p>
            <p
                class="opacity-0 subheading max-w-xl md:text-2xl text-xl tracking-normal font-regular pb-12"
            >
                The Hub is our powerful knowledge centre featuring useful and
                informative articles covering everything from money matters, to
                lifestyle choices, to all things property related.
            </p>
        </section>
        <section class="md:pl-[70px] lg:pl-[140px]">
            <div class="flex overflow-hidden mt-6 md:mt-12 px-7 md:px-0 scrolling-section opacity-0">
                <HubCard
                    title="Property"
                    postHref={getFullUrl(Astro.url.origin, "category/property")}
                    ,
                    imgSrc={Property.src}
                    ,
                    colorClass="bg-mint-300"
                />
                <div class={itemContainerClass + ' scrolling-cards'}>
                    {
                        propertyPosts.map((post: any) => (
                            <PostTile
                                readingTime={getReadingTime(
                                    "post",
                                    post.slug.current,
                                )}
                                postHref={getFullUrl(
                                    Astro.url.origin,
                                    post.fullSlug,
                                )}
                                imgSrc={urlForImage(post.thumbnail)
                                    .width(350)
                                    .height(200)
                                    .url()}
                                title={post.title}
                                color="border-mint-300"
                            />
                        ))
                    }
                </div>
            </div>
            <div class="flex overflow-hidden mt-6 md:mt-12 px-7 md:px-0 scrolling-section opacity-0">
                <HubCard
                    title="Money"
                    postHref={getFullUrl(Astro.url.origin, "category/money")}
                    ,
                    imgSrc={Money.src}
                    ,
                    colorClass="bg-navyblue-300"
                />
                <div class={itemContainerClass + ' scrolling-cards'}>
                    {
                        moneyPosts.map((post: any) => (
                            <PostTile
                                readingTime={getReadingTime(
                                    "post",
                                    post.slug.current,
                                )}
                                postHref={getFullUrl(
                                    Astro.url.origin,
                                    post.fullSlug,
                                )}
                                imgSrc={urlForImage(post.thumbnail)
                                    .width(350)
                                    .height(200)
                                    .url()}
                                title={post.title}
                                color="border-navyblue-300"
                            />
                        ))
                    }
                </div>
            </div>
            <div class="flex overflow-hidden mt-6 md:mt-12 px-7 md:px-0 scrolling-section opacity-0">
                <HubCard
                    title="Home & Lifestyle"
                    postHref={getFullUrl(
                        Astro.url.origin,
                        "category/home-lifestyle",
                    )}
                    ,
                    imgSrc={Hl.src}
                    ,
                    colorClass="bg-yellow-300"
                />
                <div class={itemContainerClass + ' scrolling-cards'}>
                    {
                        hlPosts.map((post: any) => (
                            <PostTile
                                readingTime={getReadingTime(
                                    "post",
                                    post.slug.current,
                                )}
                                postHref={getFullUrl(
                                    Astro.url.origin,
                                    post.fullSlug,
                                )}
                                imgSrc={urlForImage(post.thumbnail)
                                    .width(350)
                                    .height(200)
                                    .url()}
                                title={post.title}
                                color="border-yellow-300"
                            />
                        ))
                    }
                </div>
            </div>
            <div class="flex overflow-hidden mt-6 md:mt-12 px-7 md:px-0 scrolling-section opacity-0">
                <HubCard
                    title="Causes"
                    postHref={getFullUrl(Astro.url.origin, "category/causes")}
                    ,
                    imgSrc={Causes.src}
                    ,
                    colorClass="bg-salmon-200"
                />
                <div class={itemContainerClass + ' scrolling-cards'}>
                    {
                        causesPosts.map((post: any) => (
                            <PostTile
                                readingTime={getReadingTime(
                                    "post",
                                    post.slug.current,
                                )}
                                postHref={getFullUrl(
                                    Astro.url.origin,
                                    post.fullSlug,
                                )}
                                imgSrc={urlForImage(post.thumbnail)
                                    .width(350)
                                    .height(200)
                                    .url()}
                                title={post.title}
                                color="border-salmon-300"
                            />
                        ))
                    }
                </div>
            </div>
        </section>
        <UnleashThePowerCTA colorClass="bg-grey-gradient" />
    </main>
</Layout>

<style scoped>
    .hide-scrollbar {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }

    .hide-scrollbar::-webkit-scrollbar {
        display: none;
    }
</style>
<script>
    import { animate, stagger, inView } from "motion";
  
    inView(
      ".scrolling-section",
      (element) => {
        animate(
          element,
          { opacity: [0, 1], x: [-50, 0] },
          { ease: [0.39, 0.24, 0.3, 1], duration: 0.4 }
        );
      },
      { amount: 0.25 }
    );

    inView(
      ".scrolling-cards",
      (element) => {
        animate(
          element,
          { opacity: [0, 1], x: [-100, 0] },
          { ease: [0.39, 0.24, 0.3, 1], duration: 1.4 }
        );
      },
      { amount: 0.25 }
    );

    inView(
      ".heading",
      (element) => {
        animate(
          element,
          { opacity: [0, 1], y: [-50, 0] },
          { ease: [0.39, 0.24, 0.3, 1], duration: 1 }
        );
      },
      { amount: 0.25 }
    );

    inView(
      ".subheading",
      (element) => {
        animate(
          element,
          { opacity: [0, 1], y: [-50, 0] },
          { ease: [0.39, 0.24, 0.3, 1], duration: 1 }
        );
      },
      { amount: 0.25 }
    );
  </script>
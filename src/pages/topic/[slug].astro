---
import Layout from "../../layouts/Layout.astro";
import StartJourneyCTA from "../../components/StartJourneyCTA.astro";
import HeroSection from "../../components/HelpHeroSection.astro";
import MoreQuestions from "../../components/GotMoreQuestions.astro";
import TopicsBar from "../../components/AllHelpTopicsBar.astro";
import ArticleTile from "../../components/ArticleTile.astro";
import PopularArticles from "../../components/PopularArticles.astro";

const { slug } = Astro.params;

import {
    getTopicBySlug,
    getHelpArticlesByTopic,
    getPopularHelpArticles,
    getFullUrl,
    getReadingTime,
} from "../../lib/sanity-client.ts";
import PostTile from "../../components/PostTile.astro";

const selectedTopic = await getTopicBySlug(slug);

if (!selectedTopic) {
    return Astro.redirect("/404");
}

const helpArticles = await getHelpArticlesByTopic(selectedTopic._id);
const popularHelpArticles = await getPopularHelpArticles();

const purl = Astro.url.origin + '/previews/hub.png'
---

<Layout
    title="Flexible Property Investment and Finance | UOWN"
    classes=""
    description="UOWN is the new way to invest in and finance property, all made possible by our property crowdfunding platform. Click to invest or secure finance now."
    purl={purl}
>
    <main>
        <HeroSection isIpadHidden={true} />
        <section class="bg-gray-pages text-center">
            <p
                class="max-w-4xl lg:text-5xl md:text-4xl text-3xl tracking-normal font-extrabold py-12 md:py-24 mx-auto"
            >
                {selectedTopic.name}
            </p>
            <div class="flex md:px-20 lg:px-36 pb-24">
                <TopicsBar isSticky={false} hasBorder={false} selectedTopic={selectedTopic.name} />
                <div class="flex flex-wrap w-full lg:max-w-[78%] mb-auto lg:justify-start justify-center p-4">
                    {
                        helpArticles.map((article: any) => (
                            <ArticleTile readingTime={getReadingTime("article", article.slug.current)} postHref={getFullUrl(Astro.url.origin, article.fullSlug)} title={article.title} sectionType="topic" />
                        ))
                    }
                </div>
            </div>
        </section>
        <PopularArticles isHelpCentreMainPage={false} />
        <MoreQuestions isTopicPage={true} />
        <StartJourneyCTA colorClass="hidden lg:block" />
    </main>
</Layout>
---
import { Image } from 'astro:assets';
import Layout from "../layouts/Layout.astro";
import ContactForm from "../components/ContactForm.jsx";


import Cylinder from '../assets/images/contact/img-half-cylinder.png';
import Sphere from '../assets/images/contact/img-large-sphere.png';
import Circle from '../assets/images/contact/img-half-circle.png';

const purl = Astro.url.origin + '/previews/contact.png'
---

<Layout title="Contact UOWN | Get in Touch with Us | UOWN" classes="bg-gray" description="Get in touch with UOWN for any queries or support. We're here to help with you." purl={purl}>
  <main class="main relative py-28 lg:py-40 overflow-hidden">
        <Image src={Cylinder} alt="" class="top-[4rem] right-[15%] w-20 h-11 absolute lg:w-28 lg:h-16 z-0" />
        <Image src={Sphere} alt="" class="bottom-[5rem] left-[81%] w-24 h-20 absolute xl:w-64 xl:h-52 z-0" />
        <Image src={Circle} alt="" class="top-[24rem] right-[90%] w-28 h-24 absolute rotate-270 lg:w-40 lg:h-32 z-0" />
    <div class="mx-auto max-w-2xl text-center mb-28 w-9/12 text-black-100">
      <h2 class="text-5xl font-bold tracking-normal lg:text-6xl mb-4 z-0">
        Get in touch!
      </h2>
      <p class="mt-2 text-2xl leading-8">
        Our team is on hand to answer any questions you may have.
      </p>
    </div>
    <ContactForm client:load />
  </main>
</Layout>

<style scoped>
    .message {
        border-radius: 20px;
      grid-column: 1/-1;
    }

    .submit-form {
        grid-column: 1/-1;
    }
    </style>
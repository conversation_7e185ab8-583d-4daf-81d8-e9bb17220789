---
import Layout from "../../layouts/Layout.astro";
import CTASection from "../../components/CtaSection.jsx";

import { Author } from "../../components/Author.jsx";
import { FreeChapters } from "../../components/FreeChapters.jsx";
import { <PERSON> } from "../../components/Hero.jsx";
import { Introduction } from "../../components/Introduction.jsx";
import { NavBar } from "../../components/NavBar.jsx";
import { Screencasts } from "../../components/Screencasts.jsx";
import { TableOfContents } from "../../components/TableOfContents.jsx";
import { Testimonial } from "../../components/Testimonial.jsx";
import { Reviews } from "../../components/Reviews";
import InfiniteScroll from "../../components/InfiniteScroll.jsx";

import PostHogNode from "../../lib/posthog-node.js"; 

let pageVersion = "";
var distinctId = '';
var userAgent = '';
const projectAPIKey = "phc_Bw6vfs5tnvJF6RRRpJkZzaPdAmEnuNcxFTc7g1jVzSD";
const cookie = Astro.cookies.get(`ph_${projectAPIKey}_posthog`);
const fbcCookie = Astro.cookies.get(`_fbc`);

if (cookie && cookie.json().distinct_id) {
  try {
    distinctId = cookie.json().distinct_id;
    const enabledVariant = await PostHogNode().getFeatureFlag(
      "uk-investment-guide-landing-page-1",
      distinctId
    );
    if (enabledVariant === "test") {
      pageVersion = "test";
    } else if (enabledVariant === "control") {
      pageVersion = "control";
    }
  } catch (error) {
    pageVersion = "error";
  }
  
}

const purl = Astro.url.origin + '/previews/guides.png'
---
<Layout title="UOWN 2025 UK Property Investment Guide" classes="bg-framer" description="Discover the key trends and insights to help you make informed investments in UK property this year."  purl={purl}>
  <script is:inline>
    (function () {
      userAgent = window.navigator.userAgent;
      window.onload = function() {
        document.getElementById('userAgent').value = userAgent;
    };
  })();
  </script>

  {(pageVersion == 'test') ? (
                  <Hero client:visible />
  <Introduction client:visible />
  <InfiniteScroll client:visible />
  <NavBar client:visible />
  <TableOfContents client:load />
  <Testimonial
    id="testimonial-from-tommy-stroman"
    author={{
      name: "Francis James",
      role: "UOWN Investor",
    }}
  >
    <p>
      “Property investment to everyone is totally possible through using UOWN
      where individuals can buy a stake in property - highly recommended!”
    </p>
  </Testimonial>
  <Screencasts client:visible />
  <FreeChapters distinctId={cookie ? cookie.json().distinct_id : ''} fbc={fbcCookie ? fbcCookie : ''} variant="test" client:load />
  <Reviews client:load />
  <Author client:visible />
                ) : 
                <Hero client:visible />
  <CTASection client:visible />
  <Introduction client:visible />
  <InfiniteScroll client:visible />
  <FreeChapters distinctId={cookie ? cookie.json().distinct_id : ''} fbc={fbcCookie ? fbcCookie : ''} variant="control" client:load />
  <Reviews client:load /> }

</Layout>

<style scoped>
  .PhoneInput {
    height: 48px;
    color: #f9f7f6;
    position: relative;
}

.PhoneInputCountrySelect {
    border-radius: 9999px;
    background-color: #13b688;
    border: 0;
}

.PhoneInputCountryIcon {
    width: 30px;
    margin-left: 16px;
}

.PhoneInputInput {
    margin-right: 20px;
    width: 75%;
    background-color: #13b688 !important;
    border: 0;
    border-radius: 9999px;
}

.PhoneInputInput:focus {
    border: 0;
    --tw-ring-color: #13b688;
}
</style>

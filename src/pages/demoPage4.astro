---
import Layout from "../layouts/Layout.astro";
import HeroSection from "../components/HeroSlice.jsx";
import InfiniteScroll from "../components/InfiniteScrollSlice";
import HDIWSlice from "../components/HDIWSlice.jsx";
import BenefitsSlice from "../components/BenefitsSlice.jsx";
import OptimizeRR from "../components/OptimizeRR.jsx";
import GotQuestions from "../components/GotQuestions.astro";
import FAQs from "../components/FAQs.jsx";
import SliceRealEstateSection from "../components/SliceRealEstateSection.astro";
import Features from "../components/Features.jsx";
import PRSlice from "../components/PRSlice";
import { Reviews } from "../components/Reviews";
---

<Layout title="Property Crowdfunding & Investment Platform | UOWN" classes="bg-white" description=" from property, without owning a house" purl="">
  <HeroSection client:visible />
  <InfiniteScroll bgClass="" title="As featured in..." client:visible />
  <HDIWSlice client:visible />
  <PRSlice client:visible />
  <BenefitsSlice client:visible />
  <OptimizeRR client:visible />
  <Features client:visible />
  <FAQs client:visible />
  <Reviews client:load />
  <GotQuestions />
  <SliceRealEstateSection />
</Layout>
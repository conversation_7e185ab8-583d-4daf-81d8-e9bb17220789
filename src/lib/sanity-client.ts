import { useSanityClient } from "@sanity/astro";
import imageUrlBuilder from "@sanity/image-url";
import type { SanityImageSource } from "@sanity/image-url/lib/types/types";
import groq from "groq";

const sanityClient = useSanityClient();

export const imageBuilder = imageUrlBuilder(sanityClient);

export function urlForImage(source: SanityImageSource) {
  return imageBuilder.image(source);
}

export function getFullUrl(baseUrl: any, slug: string) {
  return baseUrl + '/' + slug;
}

export async function getHubPost(slug: any) {
  const query = groq`*[_type == "post" && slug.current =="` + slug + `"][0]`;
  const hubPost = await sanityClient.fetch(query);

  // Check if hubPost exists and has required fields
  if (!hubPost) {
    return null;
  }

  // Only fetch author if author reference exists
  if (hubPost.author && hubPost.author._ref) {
    const authorQuery = groq`*[_type == "author" && _id =="` + hubPost.author._ref + `"][0]`;
    const author = await sanityClient.fetch(authorQuery);
    hubPost.author = author;
  }

  // Only fetch category if category reference exists
  if (hubPost.category && hubPost.category._ref) {
    const categoryQuery = groq`*[_type == "category" && _id =="` + hubPost.category._ref + `"][0]`;
    const category = await sanityClient.fetch(categoryQuery);

    if (category && category.mainImage) {
      const categoryImage = urlForImage(category.mainImage);
      hubPost.category = category;
      hubPost.category.mainImage = categoryImage.url();
    } else {
      hubPost.category = category;
    }
  }

  return hubPost;
}

export async function getReadingTime(type: string, slug: any) {

  let textType = '';
  if (type == 'article') {
    textType = 'contentHtml';
  } else {
    textType = 'bodyText';
  }
  const query = groq`*[
        _type == "` + type + `" &&
        slug.current == "` + slug + `"
      ]{
        "estimatedReadingTime": round(length(pt::text(` + textType + `)) / 5 / 180 )
      }[0]`;
  const readingTime = await sanityClient.fetch(query);

  if (readingTime.estimatedReadingTime == '0') {
    return "1";
  }
  return readingTime.estimatedReadingTime;
}

export async function getCategoryPosts(categoryId: string) {
  const query = groq`*[_type == "post" && category._ref =="` + categoryId + `"]`;
  const hubPosts = await sanityClient.fetch(query);

  return hubPosts;
}

export async function getCategoryPostsWithLimit(categoryId: string) {
  const query = groq`*[_type == "post" && category._ref =="` + categoryId + `"]  | order(category._ref asc)[0...6]`;
  const hubPosts = await sanityClient.fetch(query);

  return hubPosts;
}

export async function getCategoryIDByName(name: any) {
  const query = groq`*[_type == "category" && slug.current =="` + name + `"][0]`;
  const category = await sanityClient.fetch(query);
  return category.uuid;
}

export async function getAllTopics() {
  const query = groq`*[_type == "topic"]  | order(order asc)`;
  const topics = await sanityClient.fetch(query);

  return topics;
}

export async function getHelpCentreTopic(ref: string) {
  const query = groq`*[_type == "topic" && _id =="` + ref + `"][0]`;
  const topics = await sanityClient.fetch(query);

  return topics;
}

export async function getHelpArticlesByTopic(topicId: string) {
  const query = groq`*[_type == "article"  && topic._ref =="` + topicId + `"]`;
  const articles = await sanityClient.fetch(query);

  return articles;
}

export async function getPopularHelpArticles() {
  const query = groq`*[_type == "article"][0..7]`;
  const articles = await useSanityClient().fetch(query);

  return articles;
}

export async function getHelpArticle(slug: any) {
  const query = groq`*[_type == "article" && slug.current =="` + slug + `"][0]`;
  const articles = await useSanityClient().fetch(query);

  return articles;
}

export async function getRelatedHelpArticles(topicId: string, slug: any) {
  const query = groq`*[_type == "article"  && topic._ref =="` + topicId + `" && slug.current != "` + slug + `"]`;
  const articles = await useSanityClient().fetch(query);

  return articles;
}

export async function getTopicBySlug(slug: any) {
  const query = groq`*[_type == "topic" && slug.current =="` + slug + `"][0]`;
  const topic = await useSanityClient().fetch(query);

  return topic;
}

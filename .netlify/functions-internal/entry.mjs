import * as adapter from '@astrojs/netlify/netlify-functions.js';
import { renderers } from './renderers.mjs';
import { manifest } from './manifest_d175e19b.mjs';
import 'react';
import 'react-dom/server';
import './chunks/astro_ca9e373b.mjs';
import 'clsx';
import 'html-escaper';
import 'cookie';
import 'kleur/colors';
import 'string-width';
import '@astrojs/internal-helpers/path';
import 'mime';
import 'path-to-regexp';

const _page0  = () => import('./chunks/generic_474303ff.mjs');
const _page1  = () => import('./chunks/index_ab44d3e8.mjs');
const _page2  = () => import('./chunks/terms-and-conditions_12ab9bc8.mjs');
const _page3  = () => import('./chunks/fantasy-football_75d7b781.mjs');
const _page4  = () => import('./chunks/roomzzz-glasgow_93c70dae.mjs');
const _page5  = () => import('./chunks/wicker-island-2_0704fce2.mjs');
const _page6  = () => import('./chunks/privacy-policy_6c151e17.mjs');
const _page7  = () => import('./chunks/risk-statement_71b48373.mjs');
const _page8  = () => import('./chunks/cookie-policy_410aadb0.mjs');
const _page9  = () => import('./chunks/stakeDemoPage_aec1f41f.mjs');
const _page10  = () => import('./chunks/track-record_3531bb61.mjs');
const _page11  = () => import('./chunks/_slug__88ad9d39.mjs');
const _page12  = () => import('./chunks/help-centre_94150d64.mjs');
const _page13  = () => import('./chunks/guide-sent_a1a2fb7f.mjs');
const _page14  = () => import('./chunks/robots_30d2bf48.mjs');
const _page15  = () => import('./chunks/demoPage2_23ac5e0f.mjs');
const _page16  = () => import('./chunks/demoPage3_682ef727.mjs');
const _page17  = () => import('./chunks/demoPage4_1b6b4e30.mjs');
const _page18  = () => import('./chunks/recaptcha_16f51791.mjs');
const _page19  = () => import('./chunks/thank-you_cacd88de.mjs');
const _page20  = () => import('./chunks/home-lifestyle_1cf21e22.mjs');
const _page21  = () => import('./chunks/property_2a779069.mjs');
const _page22  = () => import('./chunks/causes_22f0bf9c.mjs');
const _page23  = () => import('./chunks/money_10273d66.mjs');
const _page24  = () => import('./chunks/demoPage_8ba4a2f2.mjs');
const _page25  = () => import('./chunks/contact_a1e1f63e.mjs');
const _page26  = () => import('./chunks/_slug__1126c496.mjs');
const _page27  = () => import('./chunks/the-hub_78c814f9.mjs');
const _page28  = () => import('./chunks/uk-property-investment-guide_b7290702.mjs');
const _page29  = () => import('./chunks/property-development_efece61e.mjs');
const _page30  = () => import('./chunks/property-crowdfunding_2194a3e2.mjs');
const _page31  = () => import('./chunks/invest_1555626a.mjs');
const _page32  = () => import('./chunks/_slug__350d7ed7.mjs');
const _page33  = () => import('./chunks/404_e944f1b9.mjs');
const _page34  = () => import('./chunks/emailoctopus_594d3ae1.mjs');
const _page35  = () => import('./chunks/ArrangeCall_242131bd.mjs');
const _page36  = () => import('./chunks/feedback_5d844762.mjs');
const _page37  = () => import('./chunks/sms_d95d90b5.mjs');const pageMap = new Map([["node_modules/astro/dist/assets/endpoint/generic.js", _page0],["src/pages/index.astro", _page1],["src/pages/terms-and-conditions.mdx", _page2],["src/pages/fantasy-football.astro", _page3],["src/pages/roomzzz-glasgow.astro", _page4],["src/pages/wicker-island-2.astro", _page5],["src/pages/privacy-policy.mdx", _page6],["src/pages/risk-statement.mdx", _page7],["src/pages/cookie-policy.mdx", _page8],["src/pages/stakeDemoPage.astro", _page9],["src/pages/track-record.astro", _page10],["src/pages/help-centre/[slug].astro", _page11],["src/pages/help-centre.astro", _page12],["src/pages/guide-sent.astro", _page13],["src/pages/robots.txt.ts", _page14],["src/pages/demoPage2.astro", _page15],["src/pages/demoPage3.astro", _page16],["src/pages/demoPage4.astro", _page17],["src/pages/recaptcha.js", _page18],["src/pages/thank-you.astro", _page19],["src/pages/category/home-lifestyle.astro", _page20],["src/pages/category/property.astro", _page21],["src/pages/category/causes.astro", _page22],["src/pages/category/money.astro", _page23],["src/pages/demoPage.astro", _page24],["src/pages/contact.astro", _page25],["src/pages/the-hub/[slug].astro", _page26],["src/pages/the-hub.astro", _page27],["src/pages/guides/uk-property-investment-guide.astro", _page28],["src/pages/invest/property-crowdfunding/property-development.astro", _page29],["src/pages/invest/property-crowdfunding.astro", _page30],["src/pages/invest.astro", _page31],["src/pages/topic/[slug].astro", _page32],["src/pages/404.astro", _page33],["src/pages/api/emailoctopus.ts", _page34],["src/pages/api/ArrangeCall.ts", _page35],["src/pages/api/feedback.ts", _page36],["src/pages/api/sms.ts", _page37]]);
const _manifest = Object.assign(manifest, {
	pageMap,
	renderers,
});
const _args = {};

const _exports = adapter.createExports(_manifest, _args);
const handler = _exports['handler'];

const _start = 'start';
if(_start in adapter) {
	adapter[_start](_manifest, _args);
}

export { handler, pageMap };

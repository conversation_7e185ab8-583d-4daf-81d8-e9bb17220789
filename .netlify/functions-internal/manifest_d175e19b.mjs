import 'cookie';
import 'kleur/colors';
import 'string-width';
import '@astrojs/internal-helpers/path';
import './chunks/astro_ca9e373b.mjs';
import 'clsx';
import 'mime';
import { compile } from 'path-to-regexp';
import 'html-escaper';

if (typeof process !== "undefined") {
  let proc = process;
  if ("argv" in proc && Array.isArray(proc.argv)) {
    if (proc.argv.includes("--verbose")) ; else if (proc.argv.includes("--silent")) ; else ;
  }
}

new TextEncoder();

function getRouteGenerator(segments, addTrailingSlash) {
  const template = segments.map((segment) => {
    return "/" + segment.map((part) => {
      if (part.spread) {
        return `:${part.content.slice(3)}(.*)?`;
      } else if (part.dynamic) {
        return `:${part.content}`;
      } else {
        return part.content.normalize().replace(/\?/g, "%3F").replace(/#/g, "%23").replace(/%5B/g, "[").replace(/%5D/g, "]").replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
      }
    }).join("");
  }).join("");
  let trailing = "";
  if (addTrailingSlash === "always" && segments.length) {
    trailing = "/";
  }
  const toPath = compile(template + trailing);
  return toPath;
}

function deserializeRouteData(rawRouteData) {
  return {
    route: rawRouteData.route,
    type: rawRouteData.type,
    pattern: new RegExp(rawRouteData.pattern),
    params: rawRouteData.params,
    component: rawRouteData.component,
    generate: getRouteGenerator(rawRouteData.segments, rawRouteData._meta.trailingSlash),
    pathname: rawRouteData.pathname || void 0,
    segments: rawRouteData.segments,
    prerender: rawRouteData.prerender,
    redirect: rawRouteData.redirect,
    redirectRoute: rawRouteData.redirectRoute ? deserializeRouteData(rawRouteData.redirectRoute) : void 0
  };
}

function deserializeManifest(serializedManifest) {
  const routes = [];
  for (const serializedRoute of serializedManifest.routes) {
    routes.push({
      ...serializedRoute,
      routeData: deserializeRouteData(serializedRoute.routeData)
    });
    const route = serializedRoute;
    route.routeData = deserializeRouteData(serializedRoute.routeData);
  }
  const assets = new Set(serializedManifest.assets);
  const componentMetadata = new Map(serializedManifest.componentMetadata);
  const clientDirectives = new Map(serializedManifest.clientDirectives);
  return {
    ...serializedManifest,
    assets,
    componentMetadata,
    clientDirectives,
    routes
  };
}

const manifest = deserializeManifest({"adapterName":"@astrojs/netlify/functions","routes":[{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[],"routeData":{"type":"endpoint","route":"/_image","pattern":"^\\/_image$","segments":[[{"content":"_image","dynamic":false,"spread":false}]],"params":[],"component":"node_modules/astro/dist/assets/endpoint/generic.js","pathname":"/_image","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.3437f8e3.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".carousel_items{display:flex;wrap:nowrap;overflow:hidden}.carousel_item{position:relative;min-width:100%;height:100vh;transition:all .5s linear;background-repeat:no-repeat;background-size:cover;background-attachment:fixed}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n.shadow-light[data-astro-cid-ptkwmqzm]{box-shadow:0 4px 4px #00000040}.container[data-astro-cid-aoxzrf3e]{max-width:1508px}.card .testimonial-left-button{position:absolute;top:50%;left:32%;background-color:#3cbeec;z-index:4}.card .testimonial-right-button{position:absolute;top:50%;right:32%;box-shadow:none;background-color:#3cbeec;z-index:4}.card .card-button.right{right:0%}.react-stacked-center-carousel{padding:20px 0;overflow-y:visible!important;overflow-x:visible}.card-card{transition:all .3s ease;cursor:pointer;width:100%;border-radius:20px;height:484px;position:relative;background-color:#fff}.card-card:hover{transform:scale(1.05)}.react-stacked-center-carousel-slide-0 .card-card{cursor:default}.react-stacked-center-carousel-slide-0 .card-card:hover{transform:none}.fill{width:100%;height:100%}.card-carrier{background:transparent!important;padding-top:3%;border:none}.card-card .cover{position:absolute;transition:opacity .3s ease}.react-stacked-center-carousel-slide-0 .card-card .cover{transition:opacity .3s ease,z-index 0ms .3s}.card-card .cover.on{opacity:1;z-index:1}.card-card .cover.off{opacity:0;z-index:-1}.card-card .detail{display:flex}.card-card .video{width:40%}.card-card>p{text-align:center;padding-top:20px;font-family:Lato;font-size:16px;font-style:normal;font-weight:400;line-height:21px;letter-spacing:.02em;text-align:justified}.card-height{height:484px}@media screen and (max-width: 540px){body{overflow-x:hidden}.card-card{transition:all .3s ease;cursor:pointer;position:relative}.card .left,.card .right,.fill{display:none}.card-height{height:408px}}.light-gradient[data-astro-cid-j7pv25f6]{background-color:#eefff8}@keyframes astroFadeInOut{0%{opacity:1}to{opacity:0}}@keyframes astroFadeIn{0%{opacity:0}}@keyframes astroFadeOut{to{opacity:0}}@keyframes astroSlideFromRight{0%{transform:translate(100%)}}@keyframes astroSlideFromLeft{0%{transform:translate(-100%)}}@keyframes astroSlideToRight{to{transform:translate(100%)}}@keyframes astroSlideToLeft{to{transform:translate(-100%)}}@media (prefers-reduced-motion){::view-transition-group(*),::view-transition-old(*),::view-transition-new(*){animation:none!important}[data-astro-transition-scope]{animation:none!important}}\n"}],"routeData":{"route":"/","type":"page","pattern":"^\\/$","segments":[],"params":[],"component":"src/pages/index.astro","pathname":"/","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".content[data-astro-cid-jxsyxwfh]{border-radius:40px;box-shadow:4px 8px 14px 14px #06060603}.btn[data-astro-cid-jxsyxwfh]{max-height:58px}.sgold[data-astro-cid-jxsyxwfh]{right:80%;top:1%}.qcircle[data-astro-cid-jxsyxwfh]{left:85%;top:15%}.squircle[data-astro-cid-jxsyxwfh]{right:73%;top:23%}.hcircle[data-astro-cid-jxsyxwfh]{left:86%;top:48%}.screw[data-astro-cid-jxsyxwfh]{right:71%;top:61%}.sblack[data-astro-cid-jxsyxwfh]{left:86%;top:83%}.hcylinder[data-astro-cid-jxsyxwfh]{right:82%;top:96%}.z-1[data-astro-cid-jxsyxwfh]{z-index:1}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/terms-and-conditions","type":"page","pattern":"^\\/terms-and-conditions\\/?$","segments":[[{"content":"terms-and-conditions","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/terms-and-conditions.mdx","pathname":"/terms-and-conditions","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n@media only screen and (min-width: 1024px){.content[data-astro-cid-alovwcjm]{grid-column:span 6 / span 6;padding:192px 0 224px}.image[data-astro-cid-alovwcjm]{inset:0 0 0 50%;grid-column:span 6 / span 6;position:absolute;margin-right:0}.img-tag[data-astro-cid-alovwcjm]{aspect-ratio:auto;height:100%;inset:0;position:absolute}}\n"}],"routeData":{"route":"/fantasy-football","type":"page","pattern":"^\\/fantasy-football\\/?$","segments":[[{"content":"fantasy-football","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/fantasy-football.astro","pathname":"/fantasy-football","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n@media only screen and (min-width: 1024px){.content[data-astro-cid-42bc2dzl]{grid-column:span 6 / span 6;padding:192px 0 224px}.image[data-astro-cid-42bc2dzl]{inset:0 0 0 50%;grid-column:span 6 / span 6;position:absolute;margin-right:0}.img-tag[data-astro-cid-42bc2dzl]{aspect-ratio:auto;height:100%;inset:0;position:absolute}}\n"}],"routeData":{"route":"/roomzzz-glasgow","type":"page","pattern":"^\\/roomzzz-glasgow\\/?$","segments":[[{"content":"roomzzz-glasgow","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/roomzzz-glasgow.astro","pathname":"/roomzzz-glasgow","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":"html{font-size:16px;background:#ffffff;background-size:224px}@media (min-width: 1140px){.margint-0[data-astro-cid-hbjlhgtt]{margin-top:0!important}}\n"}],"routeData":{"route":"/wicker-island-2","type":"page","pattern":"^\\/wicker-island-2\\/?$","segments":[[{"content":"wicker-island-2","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/wicker-island-2.astro","pathname":"/wicker-island-2","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".content[data-astro-cid-jxsyxwfh]{border-radius:40px;box-shadow:4px 8px 14px 14px #06060603}.btn[data-astro-cid-jxsyxwfh]{max-height:58px}.sgold[data-astro-cid-jxsyxwfh]{right:80%;top:1%}.qcircle[data-astro-cid-jxsyxwfh]{left:85%;top:15%}.squircle[data-astro-cid-jxsyxwfh]{right:73%;top:23%}.hcircle[data-astro-cid-jxsyxwfh]{left:86%;top:48%}.screw[data-astro-cid-jxsyxwfh]{right:71%;top:61%}.sblack[data-astro-cid-jxsyxwfh]{left:86%;top:83%}.hcylinder[data-astro-cid-jxsyxwfh]{right:82%;top:96%}.z-1[data-astro-cid-jxsyxwfh]{z-index:1}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/privacy-policy","type":"page","pattern":"^\\/privacy-policy\\/?$","segments":[[{"content":"privacy-policy","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/privacy-policy.mdx","pathname":"/privacy-policy","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".content[data-astro-cid-jxsyxwfh]{border-radius:40px;box-shadow:4px 8px 14px 14px #06060603}.btn[data-astro-cid-jxsyxwfh]{max-height:58px}.sgold[data-astro-cid-jxsyxwfh]{right:80%;top:1%}.qcircle[data-astro-cid-jxsyxwfh]{left:85%;top:15%}.squircle[data-astro-cid-jxsyxwfh]{right:73%;top:23%}.hcircle[data-astro-cid-jxsyxwfh]{left:86%;top:48%}.screw[data-astro-cid-jxsyxwfh]{right:71%;top:61%}.sblack[data-astro-cid-jxsyxwfh]{left:86%;top:83%}.hcylinder[data-astro-cid-jxsyxwfh]{right:82%;top:96%}.z-1[data-astro-cid-jxsyxwfh]{z-index:1}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/risk-statement","type":"page","pattern":"^\\/risk-statement\\/?$","segments":[[{"content":"risk-statement","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/risk-statement.mdx","pathname":"/risk-statement","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".content[data-astro-cid-jxsyxwfh]{border-radius:40px;box-shadow:4px 8px 14px 14px #06060603}.btn[data-astro-cid-jxsyxwfh]{max-height:58px}.sgold[data-astro-cid-jxsyxwfh]{right:80%;top:1%}.qcircle[data-astro-cid-jxsyxwfh]{left:85%;top:15%}.squircle[data-astro-cid-jxsyxwfh]{right:73%;top:23%}.hcircle[data-astro-cid-jxsyxwfh]{left:86%;top:48%}.screw[data-astro-cid-jxsyxwfh]{right:71%;top:61%}.sblack[data-astro-cid-jxsyxwfh]{left:86%;top:83%}.hcylinder[data-astro-cid-jxsyxwfh]{right:82%;top:96%}.z-1[data-astro-cid-jxsyxwfh]{z-index:1}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/cookie-policy","type":"page","pattern":"^\\/cookie-policy\\/?$","segments":[[{"content":"cookie-policy","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/cookie-policy.mdx","pathname":"/cookie-policy","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/stakedemopage","type":"page","pattern":"^\\/stakeDemoPage\\/?$","segments":[[{"content":"stakeDemoPage","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/stakeDemoPage.astro","pathname":"/stakeDemoPage","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/track-record","type":"page","pattern":"^\\/track-record\\/?$","segments":[[{"content":"track-record","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/track-record.astro","pathname":"/track-record","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".topics-shadow[data-astro-cid-lbkq32wh]{box-shadow:0 3.9958341121673584px 5.993751049041748px -1.9979170560836792px #0000000d}\n.hero[data-astro-cid-mehffs7l]{background-image:url(/assets/images/help/help_main_image.png);height:63vh}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/help-centre/[slug]","type":"page","pattern":"^\\/help-centre\\/([^/]+?)\\/?$","segments":[[{"content":"help-centre","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/help-centre/[slug].astro","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".hero[data-astro-cid-woqydxpj]{background-image:url(/_astro/help_main_image.79a7e47c.jpg)}@media (max-width: 1024px){.hero[data-astro-cid-woqydxpj]{background-image:url(/_astro/help_main_image_ipad.f24e213f.jpg)}}@media (max-width: 640px){.hero[data-astro-cid-woqydxpj]{background-image:url(/_astro/help_main_image_mobile.3d1a8e5c.jpg)}}\n.topics-shadow[data-astro-cid-lbkq32wh]{box-shadow:0 3.9958341121673584px 5.993751049041748px -1.9979170560836792px #0000000d}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/help-centre","type":"page","pattern":"^\\/help-centre\\/?$","segments":[[{"content":"help-centre","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/help-centre.astro","pathname":"/help-centre","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/guide-sent","type":"page","pattern":"^\\/guide-sent\\/?$","segments":[[{"content":"guide-sent","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/guide-sent.astro","pathname":"/guide-sent","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[],"routeData":{"route":"/robots.txt","type":"endpoint","pattern":"^\\/robots\\.txt$","segments":[[{"content":"robots.txt","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/robots.txt.ts","pathname":"/robots.txt","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5d40a5b1.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".slideContainer[data-astro-cid-tv6iajjp]{background:linear-gradient(179.99999999999997deg,rgba(247,244,242,0) 27.325332164764404%,rgb(249,247,246) 100%)}.bg-hero-section[data-astro-cid-tv6iajjp]{background:linear-gradient(180deg,#f9f7f6 0%,rgba(247,244,242,0) 83.55469107627869%)}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n.shadow-light[data-astro-cid-e3zz4mmw]{box-shadow:0 4px 4px #00000040}.carousel_items[data-astro-cid-qajokshc]{display:flex;wrap:nowrap;overflow:hidden}.carousel_item[data-astro-cid-qajokshc]{position:relative;min-width:100%;height:100vh;transition:all .5s linear;background-repeat:no-repeat;background-size:cover;background-attachment:fixed}.shadow-light[data-astro-cid-bdtrgxjh]{box-shadow:0 4px 4px #00000040}.hero[data-astro-cid-4c66atp7]{background-image:url(/_astro/hero-img-lg.11075c19.jpg);height:100vh}.linear-grad[data-astro-cid-4c66atp7]{background-color:#fff;background-image:linear-gradient(180deg,#f5f5f5 0%,rgba(255,255,255,0) 2.58%)}\n.carousel_items{display:flex;wrap:nowrap;overflow:hidden}.carousel_item{position:relative;min-width:100%;height:100vh;transition:all .5s linear;background-repeat:no-repeat;background-size:cover;background-attachment:fixed}\n"}],"routeData":{"route":"/demopage2","type":"page","pattern":"^\\/demoPage2\\/?$","segments":[[{"content":"demoPage2","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/demoPage2.astro","pathname":"/demoPage2","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/demopage3","type":"page","pattern":"^\\/demoPage3\\/?$","segments":[[{"content":"demoPage3","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/demoPage3.astro","pathname":"/demoPage3","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/demopage4","type":"page","pattern":"^\\/demoPage4\\/?$","segments":[[{"content":"demoPage4","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/demoPage4.astro","pathname":"/demoPage4","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[],"routeData":{"route":"/recaptcha","type":"endpoint","pattern":"^\\/recaptcha$","segments":[[{"content":"recaptcha","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/recaptcha.js","pathname":"/recaptcha","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/thank-you","type":"page","pattern":"^\\/thank-you\\/?$","segments":[[{"content":"thank-you","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/thank-you.astro","pathname":"/thank-you","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".hero[data-astro-cid-jjeahtbm]{background-image:url(/_astro/hl_main_image.3912679e.png);height:63vh}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/category/home-lifestyle","type":"page","pattern":"^\\/category\\/home-lifestyle\\/?$","segments":[[{"content":"category","dynamic":false,"spread":false}],[{"content":"home-lifestyle","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/category/home-lifestyle.astro","pathname":"/category/home-lifestyle","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".hero[data-astro-cid-xfilifzt]{background-image:url(/_astro/property_main_image.c7fa6705.png);height:63vh}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/category/property","type":"page","pattern":"^\\/category\\/property\\/?$","segments":[[{"content":"category","dynamic":false,"spread":false}],[{"content":"property","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/category/property.astro","pathname":"/category/property","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".hero[data-astro-cid-f6pfmuu3]{background-image:url(/_astro/causes_main_image.d85caaa4.png);height:63vh}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/category/causes","type":"page","pattern":"^\\/category\\/causes\\/?$","segments":[[{"content":"category","dynamic":false,"spread":false}],[{"content":"causes","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/category/causes.astro","pathname":"/category/causes","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".hero[data-astro-cid-fk35ypu3]{background-image:url(/_astro/money_main_image.6af26cc1.png);height:63vh}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/category/money","type":"page","pattern":"^\\/category\\/money\\/?$","segments":[[{"content":"category","dynamic":false,"spread":false}],[{"content":"money","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/category/money.astro","pathname":"/category/money","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/demopage","type":"page","pattern":"^\\/demoPage\\/?$","segments":[[{"content":"demoPage","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/demoPage.astro","pathname":"/demoPage","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".message[data-astro-cid-uw5kdbxl]{border-radius:20px;grid-column:1/-1}.submit-form[data-astro-cid-uw5kdbxl]{grid-column:1/-1}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/contact","type":"page","pattern":"^\\/contact\\/?$","segments":[[{"content":"contact","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/contact.astro","pathname":"/contact","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/the-hub/[slug]","type":"page","pattern":"^\\/the-hub\\/([^/]+?)\\/?$","segments":[[{"content":"the-hub","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/the-hub/[slug].astro","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.8719866d.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n.hide-scrollbar[data-astro-cid-b46ygrw4]{-ms-overflow-style:none;scrollbar-width:none}.hide-scrollbar[data-astro-cid-b46ygrw4]::-webkit-scrollbar{display:none}\n"}],"routeData":{"route":"/the-hub","type":"page","pattern":"^\\/the-hub\\/?$","segments":[[{"content":"the-hub","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/the-hub.astro","pathname":"/the-hub","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n:root{--PhoneInput-color--focus: #03b2cb;--PhoneInputInternationalIconPhone-opacity: .8;--PhoneInputInternationalIconGlobe-opacity: .65;--PhoneInputCountrySelect-marginRight: .35em;--PhoneInputCountrySelectArrow-width: .3em;--PhoneInputCountrySelectArrow-marginLeft: var(--PhoneInputCountrySelect-marginRight);--PhoneInputCountrySelectArrow-borderWidth: 1px;--PhoneInputCountrySelectArrow-opacity: .45;--PhoneInputCountrySelectArrow-color: currentColor;--PhoneInputCountrySelectArrow-color--focus: var(--PhoneInput-color--focus);--PhoneInputCountrySelectArrow-transform: rotate(45deg);--PhoneInputCountryFlag-aspectRatio: 1.5;--PhoneInputCountryFlag-height: 1em;--PhoneInputCountryFlag-borderWidth: 1px;--PhoneInputCountryFlag-borderColor: rgba(0,0,0,.5);--PhoneInputCountryFlag-borderColor--focus: var(--PhoneInput-color--focus);--PhoneInputCountryFlag-backgroundColor--loading: rgba(0,0,0,.1)}.PhoneInput{display:flex;align-items:center}.PhoneInputInput{flex:1;min-width:0}.PhoneInputCountryIcon{width:calc(var(--PhoneInputCountryFlag-height) * var(--PhoneInputCountryFlag-aspectRatio));height:var(--PhoneInputCountryFlag-height)}.PhoneInputCountryIcon--square{width:var(--PhoneInputCountryFlag-height)}.PhoneInputCountryIcon--border{background-color:var(--PhoneInputCountryFlag-backgroundColor--loading);box-shadow:0 0 0 var(--PhoneInputCountryFlag-borderWidth) var(--PhoneInputCountryFlag-borderColor),inset 0 0 0 var(--PhoneInputCountryFlag-borderWidth) var(--PhoneInputCountryFlag-borderColor)}.PhoneInputCountryIconImg{display:block;width:100%;height:100%}.PhoneInputInternationalIconPhone{opacity:var(--PhoneInputInternationalIconPhone-opacity)}.PhoneInputInternationalIconGlobe{opacity:var(--PhoneInputInternationalIconGlobe-opacity)}.PhoneInputCountry{position:relative;align-self:stretch;display:flex;align-items:center;margin-right:var(--PhoneInputCountrySelect-marginRight)}.PhoneInputCountrySelect{position:absolute;top:0;left:0;height:100%;width:100%;z-index:1;border:0;opacity:0;cursor:pointer}.PhoneInputCountrySelect[disabled],.PhoneInputCountrySelect[readonly]{cursor:default}.PhoneInputCountrySelectArrow{display:block;content:\"\";width:var(--PhoneInputCountrySelectArrow-width);height:var(--PhoneInputCountrySelectArrow-width);margin-left:var(--PhoneInputCountrySelectArrow-marginLeft);border-style:solid;border-color:var(--PhoneInputCountrySelectArrow-color);border-top-width:0;border-bottom-width:var(--PhoneInputCountrySelectArrow-borderWidth);border-left-width:0;border-right-width:var(--PhoneInputCountrySelectArrow-borderWidth);transform:var(--PhoneInputCountrySelectArrow-transform);opacity:var(--PhoneInputCountrySelectArrow-opacity)}.PhoneInputCountrySelect:focus+.PhoneInputCountryIcon+.PhoneInputCountrySelectArrow{opacity:1;color:var(--PhoneInputCountrySelectArrow-color--focus)}.PhoneInputCountrySelect:focus+.PhoneInputCountryIcon--border{box-shadow:0 0 0 var(--PhoneInputCountryFlag-borderWidth) var(--PhoneInputCountryFlag-borderColor--focus),inset 0 0 0 var(--PhoneInputCountryFlag-borderWidth) var(--PhoneInputCountryFlag-borderColor--focus)}.PhoneInputCountrySelect:focus+.PhoneInputCountryIcon .PhoneInputInternationalIconGlobe{opacity:1;color:var(--PhoneInputCountrySelectArrow-color--focus)}.PhoneInput[data-astro-cid-a6c5jt4w]{height:48px;color:#f9f7f6;position:relative}.PhoneInputCountrySelect[data-astro-cid-a6c5jt4w]{border-radius:9999px;background-color:#13b688;border:0}.PhoneInputCountryIcon[data-astro-cid-a6c5jt4w]{width:30px;margin-left:16px}.PhoneInputInput[data-astro-cid-a6c5jt4w]{margin-right:20px;width:75%;background-color:#13b688!important;border:0;border-radius:9999px}.PhoneInputInput[data-astro-cid-a6c5jt4w]:focus{border:0;--tw-ring-color: #13b688}\n"}],"routeData":{"route":"/guides/uk-property-investment-guide","type":"page","pattern":"^\\/guides\\/uk-property-investment-guide\\/?$","segments":[[{"content":"guides","dynamic":false,"spread":false}],[{"content":"uk-property-investment-guide","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/guides/uk-property-investment-guide.astro","pathname":"/guides/uk-property-investment-guide","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n.feature-blocks[data-astro-cid-d564gauj]{grid-template-columns:repeat(auto-fit,minmax(215px,1fr))}\n"}],"routeData":{"route":"/invest/property-crowdfunding/property-development","type":"page","pattern":"^\\/invest\\/property-crowdfunding\\/property-development\\/?$","segments":[[{"content":"invest","dynamic":false,"spread":false}],[{"content":"property-crowdfunding","dynamic":false,"spread":false}],[{"content":"property-development","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/invest/property-crowdfunding/property-development.astro","pathname":"/invest/property-crowdfunding/property-development","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/invest/property-crowdfunding","type":"page","pattern":"^\\/invest\\/property-crowdfunding\\/?$","segments":[[{"content":"invest","dynamic":false,"spread":false}],[{"content":"property-crowdfunding","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/invest/property-crowdfunding.astro","pathname":"/invest/property-crowdfunding","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5d40a5b1.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".shadow-light[data-astro-cid-e3zz4mmw]{box-shadow:0 4px 4px #00000040}.carousel_items[data-astro-cid-qajokshc]{display:flex;wrap:nowrap;overflow:hidden}.carousel_item[data-astro-cid-qajokshc]{position:relative;min-width:100%;height:100vh;transition:all .5s linear;background-repeat:no-repeat;background-size:cover;background-attachment:fixed}.shadow-light[data-astro-cid-bdtrgxjh]{box-shadow:0 4px 4px #00000040}.hero[data-astro-cid-4c66atp7]{background-image:url(/_astro/hero-img-lg.11075c19.jpg);height:100vh}.linear-grad[data-astro-cid-4c66atp7]{background-color:#fff;background-image:linear-gradient(180deg,#f5f5f5 0%,rgba(255,255,255,0) 2.58%)}\n.carousel_items{display:flex;wrap:nowrap;overflow:hidden}.carousel_item{position:relative;min-width:100%;height:100vh;transition:all .5s linear;background-repeat:no-repeat;background-size:cover;background-attachment:fixed}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/invest","type":"page","pattern":"^\\/invest\\/?$","segments":[[{"content":"invest","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/invest.astro","pathname":"/invest","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":".hero[data-astro-cid-woqydxpj]{background-image:url(/_astro/help_main_image.79a7e47c.jpg)}@media (max-width: 1024px){.hero[data-astro-cid-woqydxpj]{background-image:url(/_astro/help_main_image_ipad.f24e213f.jpg)}}@media (max-width: 640px){.hero[data-astro-cid-woqydxpj]{background-image:url(/_astro/help_main_image_mobile.3d1a8e5c.jpg)}}\n.topics-shadow[data-astro-cid-lbkq32wh]{box-shadow:0 3.9958341121673584px 5.993751049041748px -1.9979170560836792px #0000000d}\n:root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/topic/[slug]","type":"page","pattern":"^\\/topic\\/([^/]+?)\\/?$","segments":[[{"content":"topic","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/topic/[slug].astro","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/hoisted.5157f620.js"},{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[{"type":"external","src":"/_astro/causes.5e6616d2.css"},{"type":"inline","content":":root{--accent: 136, 58, 234;--accent-light: 224, 204, 250;--accent-dark: 49, 10, 101;--accent-gradient: linear-gradient( 45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60% )}html{font-size:16px;background:#ffffff;background-size:224px}code{font-family:Menlo,Monaco,Lucida Console,Liberation Mono,DejaV cxjmc u Sans Mono,Bitstream Vera Sans Mono,Courier New,monospace}\n"}],"routeData":{"route":"/404","type":"page","pattern":"^\\/404\\/?$","segments":[[{"content":"404","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/404.astro","pathname":"/404","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[],"routeData":{"route":"/api/emailoctopus","type":"endpoint","pattern":"^\\/api\\/emailoctopus$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"emailoctopus","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/emailoctopus.ts","pathname":"/api/emailoctopus","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[],"routeData":{"route":"/api/arrangecall","type":"endpoint","pattern":"^\\/api\\/ArrangeCall$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"ArrangeCall","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/ArrangeCall.ts","pathname":"/api/ArrangeCall","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[],"routeData":{"route":"/api/feedback","type":"endpoint","pattern":"^\\/api\\/feedback$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"feedback","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/feedback.ts","pathname":"/api/feedback","prerender":false,"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"!(function(w,p,f,c){if(!window.crossOriginIsolated && !navigator.serviceWorker) return;c=w[p]=Object.assign(w[p]||{},{\"lib\":\"/~partytown/\",\"debug\":false,\"logCalls\":false,\"logGetters\":false,\"logSetters\":false,\"logImageRequests\":false,\"logScriptExecution\":false,\"logStackTraces\":false,\"resolveUrl\":\"(url) => {\\n        const siteUrl = \\\"https://your-proxy.url/\\\";\\n        const proxyUrl = new URL(siteUrl);\\n        if (\\n          url.hostname === \\\"googleads.g.doubleclick.net\\\" ||\\n          url.hostname === \\\"www.googleadservices.com\\\" ||\\n          url.hostname === \\\"googletagmanager.com\\\" ||\\n          url.hostname === \\\"www.googletagmanager.com\\\" ||\\n          url.hostname === \\\"region1.google-analytics.com\\\" ||\\n          url.hostname === \\\"google.com\\\"\\n        ) {\\n          proxyUrl.searchParams.append(\\\"apiurl\\\", url.href);\\n          return proxyUrl;\\n        }\\n        return url;\\n      }\"});c[f]=(c[f]||[]).concat([[\"dataLayer.push\"]])})(window,'partytown','forward');/* Partytown 0.11.1 - MIT QwikDev */\nconst t={preserveBehavior:!1},e=e=>{if(\"string\"==typeof e)return[e,t];const[n,r=t]=e;return[n,{...t,...r}]},n=Object.freeze((t=>{const e=new Set;let n=[];do{Object.getOwnPropertyNames(n).forEach((t=>{\"function\"==typeof n[t]&&e.add(t)}))}while((n=Object.getPrototypeOf(n))!==Object.prototype);return Array.from(e)})());!function(t,r,o,i,a,s,c,l,d,p,u=t,f){function h(){f||(f=1,\"/\"==(c=(s.lib||\"/~partytown/\")+(s.debug?\"debug/\":\"\"))[0]&&(d=r.querySelectorAll('script[type=\"text/partytown\"]'),i!=t?i.dispatchEvent(new CustomEvent(\"pt1\",{detail:t})):(l=setTimeout(v,(null==s?void 0:s.fallbackTimeout)||1e4),r.addEventListener(\"pt0\",w),a?y(1):o.serviceWorker?o.serviceWorker.register(c+(s.swPath||\"partytown-sw.js\"),{scope:c}).then((function(t){t.active?y():t.installing&&t.installing.addEventListener(\"statechange\",(function(t){\"activated\"==t.target.state&&y()}))}),console.error):v())))}function y(e){p=r.createElement(e?\"script\":\"iframe\"),t._pttab=Date.now(),e||(p.style.display=\"block\",p.style.width=\"0\",p.style.height=\"0\",p.style.border=\"0\",p.style.visibility=\"hidden\",p.setAttribute(\"aria-hidden\",!0)),p.src=c+\"partytown-\"+(e?\"atomics.js?v=0.11.1\":\"sandbox-sw.html?\"+t._pttab),r.querySelector(s.sandboxParent||\"body\").appendChild(p)}function v(n,o){for(w(),i==t&&(s.forward||[]).map((function(n){const[r]=e(n);delete t[r.split(\".\")[0]]})),n=0;n<d.length;n++)(o=r.createElement(\"script\")).innerHTML=d[n].innerHTML,o.nonce=s.nonce,r.head.appendChild(o);p&&p.parentNode.removeChild(p)}function w(){clearTimeout(l)}s=t.partytown||{},i==t&&(s.forward||[]).map((function(r){const[o,{preserveBehavior:i}]=e(r);u=t,o.split(\".\").map((function(e,r,o){var a;u=u[o[r]]=r+1<o.length?u[o[r]]||(a=o[r+1],n.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:n,thisObject:r}=((t,e)=>{let n=t;for(let t=0;t<e.length-1;t+=1)n=n[e[t]];return{thisObject:n,methodOrProperty:e.length>0?n[e[e.length-1]]:void 0}})(t,o);\"function\"==typeof n&&(e=(...t)=>n.apply(r,...t))}return function(){let n;return e&&(n=e(arguments)),(t._ptf=t._ptf||[]).push(o,arguments),n}})()}))})),\"complete\"==r.readyState?h():(t.addEventListener(\"DOMContentLoaded\",h),t.addEventListener(\"load\",h))}(window,document,navigator,top,window.crossOriginIsolated);;(e=>{e.addEventListener(\"astro:before-swap\",e=>{let r=document.body.querySelector(\"iframe[src*='/~partytown/']\");if(r)e.newDocument.body.append(r)})})(document);"}],"styles":[],"routeData":{"route":"/api/sms","type":"endpoint","pattern":"^\\/api\\/sms$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"sms","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/sms.ts","pathname":"/api/sms","prerender":false,"_meta":{"trailingSlash":"ignore"}}}],"site":"https://www.uown.co","base":"/","compressHTML":true,"componentMetadata":[["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/404.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/category/causes.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/category/home-lifestyle.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/category/money.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/category/property.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/contact.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/cookie-policy.mdx",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/demoPage.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/demoPage2.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/demoPage3.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/demoPage4.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/fantasy-football.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/guide-sent.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/guides/uk-property-investment-guide.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/help-centre.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/help-centre/[slug].astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/index.astro",{"propagation":"in-tree","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/invest.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/invest/property-crowdfunding.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/invest/property-crowdfunding/property-development.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/privacy-policy.mdx",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/risk-statement.mdx",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/roomzzz-glasgow.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/stakeDemoPage.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/terms-and-conditions.mdx",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/thank-you.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/the-hub.astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/the-hub/[slug].astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/topic/[slug].astro",{"propagation":"none","containsHead":true}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/track-record.astro",{"propagation":"none","containsHead":true}],["\u0000@astro-page:src/pages/index@_@astro",{"propagation":"in-tree","containsHead":false}],["\u0000@astrojs-ssr-virtual-entry",{"propagation":"in-tree","containsHead":false}],["/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/wicker-island-2.astro",{"propagation":"none","containsHead":true}]],"renderers":[],"clientDirectives":[["idle","(()=>{var i=t=>{let e=async()=>{await(await t())()};\"requestIdleCallback\"in window?window.requestIdleCallback(e):setTimeout(e,200)};(self.Astro||(self.Astro={})).idle=i;window.dispatchEvent(new Event(\"astro:idle\"));})();"],["load","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event(\"astro:load\"));})();"],["media","(()=>{var s=(i,t)=>{let a=async()=>{await(await i())()};if(t.value){let e=matchMedia(t.value);e.matches?a():e.addEventListener(\"change\",a,{once:!0})}};(self.Astro||(self.Astro={})).media=s;window.dispatchEvent(new Event(\"astro:media\"));})();"],["only","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event(\"astro:only\"));})();"],["visible","(()=>{var r=(i,c,s)=>{let n=async()=>{await(await i())()},t=new IntersectionObserver(e=>{for(let o of e)if(o.isIntersecting){t.disconnect(),n();break}});for(let e of s.children)t.observe(e)};(self.Astro||(self.Astro={})).visible=r;window.dispatchEvent(new Event(\"astro:visible\"));})();"]],"entryModules":{"\u0000@astrojs-ssr-virtual-entry":"entry.mjs","\u0000@astro-renderers":"renderers.mjs","\u0000empty-middleware":"_empty-middleware.mjs","/src/pages/api/ArrangeCall.ts":"chunks/pages/ArrangeCall_3769aff7.mjs","/src/pages/demoPage3.astro":"chunks/pages/demoPage3_74e06cb0.mjs","/src/pages/api/feedback.ts":"chunks/pages/feedback_eeb50de9.mjs","/node_modules/astro/dist/assets/endpoint/generic.js":"chunks/pages/generic_004635bc.mjs","/src/pages/guide-sent.astro":"chunks/pages/guide-sent_afd0310e.mjs","/src/pages/help-centre.astro":"chunks/pages/help-centre_d1a00b50.mjs","/src/pages/category/home-lifestyle.astro":"chunks/pages/home-lifestyle_662a7665.mjs","/src/pages/invest.astro":"chunks/pages/invest_01881035.mjs","/src/pages/category/money.astro":"chunks/pages/money_745f5e80.mjs","/src/pages/privacy-policy.mdx":"chunks/pages/privacy-policy_790c423a.mjs","/src/pages/invest/property-crowdfunding.astro":"chunks/pages/property-crowdfunding_1363e57d.mjs","/src/pages/invest/property-crowdfunding/property-development.astro":"chunks/pages/property-development_af1007e2.mjs","/src/pages/category/property.astro":"chunks/pages/property_510dabc7.mjs","/src/pages/recaptcha.js":"chunks/pages/recaptcha_391a40b7.mjs","/src/pages/risk-statement.mdx":"chunks/pages/risk-statement_cac82525.mjs","/src/pages/robots.txt.ts":"chunks/pages/robots_1b443223.mjs","/src/pages/api/sms.ts":"chunks/pages/sms_104e3b1b.mjs","/src/pages/stakeDemoPage.astro":"chunks/pages/stakeDemoPage_cee8aa4f.mjs","/src/pages/terms-and-conditions.mdx":"chunks/pages/terms-and-conditions_6b860f55.mjs","/src/pages/thank-you.astro":"chunks/pages/thank-you_34ddb698.mjs","/src/pages/the-hub.astro":"chunks/pages/the-hub_5924c644.mjs","/src/pages/track-record.astro":"chunks/pages/track-record_2355aa72.mjs","/src/pages/guides/uk-property-investment-guide.astro":"chunks/pages/uk-property-investment-guide_e01596b0.mjs","/src/pages/wicker-island-2.astro":"chunks/pages/wicker-island-2_d5eae641.mjs","\u0000@astrojs-manifest":"manifest_d175e19b.mjs","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/node_modules/@astrojs/react/vnode-children.js":"chunks/vnode-children_fe124244.mjs","\u0000@astro-page:node_modules/astro/dist/assets/endpoint/generic@_@js":"chunks/generic_474303ff.mjs","\u0000@astro-page:src/pages/index@_@astro":"chunks/index_ab44d3e8.mjs","\u0000@astro-page:src/pages/terms-and-conditions@_@mdx":"chunks/terms-and-conditions_12ab9bc8.mjs","\u0000@astro-page:src/pages/fantasy-football@_@astro":"chunks/fantasy-football_75d7b781.mjs","\u0000@astro-page:src/pages/roomzzz-glasgow@_@astro":"chunks/roomzzz-glasgow_93c70dae.mjs","\u0000@astro-page:src/pages/wicker-island-2@_@astro":"chunks/wicker-island-2_0704fce2.mjs","\u0000@astro-page:src/pages/privacy-policy@_@mdx":"chunks/privacy-policy_6c151e17.mjs","\u0000@astro-page:src/pages/risk-statement@_@mdx":"chunks/risk-statement_71b48373.mjs","\u0000@astro-page:src/pages/cookie-policy@_@mdx":"chunks/cookie-policy_410aadb0.mjs","\u0000@astro-page:src/pages/stakeDemoPage@_@astro":"chunks/stakeDemoPage_aec1f41f.mjs","\u0000@astro-page:src/pages/track-record@_@astro":"chunks/track-record_3531bb61.mjs","\u0000@astro-page:src/pages/help-centre/[slug]@_@astro":"chunks/_slug__88ad9d39.mjs","\u0000@astro-page:src/pages/help-centre@_@astro":"chunks/help-centre_94150d64.mjs","\u0000@astro-page:src/pages/guide-sent@_@astro":"chunks/guide-sent_a1a2fb7f.mjs","\u0000@astro-page:src/pages/robots.txt@_@ts":"chunks/robots_30d2bf48.mjs","\u0000@astro-page:src/pages/demoPage2@_@astro":"chunks/demoPage2_23ac5e0f.mjs","\u0000@astro-page:src/pages/demoPage3@_@astro":"chunks/demoPage3_682ef727.mjs","\u0000@astro-page:src/pages/demoPage4@_@astro":"chunks/demoPage4_1b6b4e30.mjs","\u0000@astro-page:src/pages/recaptcha@_@js":"chunks/recaptcha_16f51791.mjs","\u0000@astro-page:src/pages/thank-you@_@astro":"chunks/thank-you_cacd88de.mjs","\u0000@astro-page:src/pages/category/home-lifestyle@_@astro":"chunks/home-lifestyle_1cf21e22.mjs","\u0000@astro-page:src/pages/category/property@_@astro":"chunks/property_2a779069.mjs","\u0000@astro-page:src/pages/category/causes@_@astro":"chunks/causes_22f0bf9c.mjs","\u0000@astro-page:src/pages/category/money@_@astro":"chunks/money_10273d66.mjs","\u0000@astro-page:src/pages/demoPage@_@astro":"chunks/demoPage_8ba4a2f2.mjs","\u0000@astro-page:src/pages/contact@_@astro":"chunks/contact_a1e1f63e.mjs","\u0000@astro-page:src/pages/the-hub/[slug]@_@astro":"chunks/_slug__1126c496.mjs","\u0000@astro-page:src/pages/the-hub@_@astro":"chunks/the-hub_78c814f9.mjs","\u0000@astro-page:src/pages/guides/uk-property-investment-guide@_@astro":"chunks/uk-property-investment-guide_b7290702.mjs","\u0000@astro-page:src/pages/invest/property-crowdfunding/property-development@_@astro":"chunks/property-development_efece61e.mjs","\u0000@astro-page:src/pages/invest/property-crowdfunding@_@astro":"chunks/property-crowdfunding_2194a3e2.mjs","\u0000@astro-page:src/pages/invest@_@astro":"chunks/invest_1555626a.mjs","\u0000@astro-page:src/pages/topic/[slug]@_@astro":"chunks/_slug__350d7ed7.mjs","\u0000@astro-page:src/pages/404@_@astro":"chunks/404_e944f1b9.mjs","\u0000@astro-page:src/pages/api/emailoctopus@_@ts":"chunks/emailoctopus_594d3ae1.mjs","\u0000@astro-page:src/pages/api/ArrangeCall@_@ts":"chunks/ArrangeCall_242131bd.mjs","\u0000@astro-page:src/pages/api/feedback@_@ts":"chunks/feedback_5d844762.mjs","\u0000@astro-page:src/pages/api/sms@_@ts":"chunks/sms_d95d90b5.mjs","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/InsightsSection.jsx":"_astro/InsightsSection.212ea3b1.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HorizontalScroll.jsx":"_astro/HorizontalScroll.a82708d2.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/FAQs.jsx":"_astro/FAQs.00212a42.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/GlobalLeader.jsx":"_astro/GlobalLeader.3f4ff296.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/InfiniteScroll.jsx":"_astro/InfiniteScroll.1204197a.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/PRSlice":"_astro/PRSlice.46bc6bec.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/ReviewTabs.jsx":"_astro/ReviewTabs.d1160f6d.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Features.jsx":"_astro/Features.356e0720.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Screencasts.jsx":"_astro/Screencasts.4a22585b.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/ContactForm.tsx":"_astro/ContactForm.ed31951c.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/TableOfContents.jsx":"_astro/TableOfContents.370262f8.js","/astro/hoisted.js?q=1":"_astro/hoisted.3437f8e3.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/EmailForm.tsx":"_astro/EmailForm.910b0117.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/GuideSent.jsx":"_astro/GuideSent.1d857f37.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/TilesSection.jsx":"_astro/TilesSection.8c3e0ee2.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Hero.jsx":"_astro/Hero.75cea86a.js","/astro/hoisted.js?q=0":"_astro/hoisted.8719866d.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HeroSlice.jsx":"_astro/HeroSlice.39680dd8.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/PhoneForm.tsx":"_astro/PhoneForm.6690d163.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/GrowMoneyStake.jsx":"_astro/GrowMoneyStake.79af207d.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/H2Serializer.jsx":"_astro/H2Serializer.7b441f85.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Tabs.jsx":"_astro/Tabs.0503ce99.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HeroSection.jsx":"_astro/HeroSection.83621999.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Author.jsx":"_astro/Author.2f0ba0db.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/CtaSection.jsx":"_astro/CtaSection.4844d8d6.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HeroStake.jsx":"_astro/HeroStake.b4139472.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Introduction.jsx":"_astro/Introduction.643184af.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/OptimizeRR.jsx":"_astro/OptimizeRR.ad9efeed.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/SafetyStake.jsx":"_astro/SafetyStake.2b53cf3a.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/BlockSerializer.jsx":"_astro/BlockSerializer.f4d2066b.js","/astro/hoisted.js?q=2":"_astro/hoisted.5d40a5b1.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Banner.jsx":"_astro/Banner.fe886067.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Dialog.jsx":"_astro/Dialog.9d39459a.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/InfiniteScrollSlice":"_astro/InfiniteScrollSlice.3c4d81f8.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/PrimaryFeatures.tsx":"_astro/PrimaryFeatures.09f2e4a9.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Reviews.tsx":"_astro/Reviews.5b95e505.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/InfiniteScrollSlice.jsx":"_astro/InfiniteScrollSlice.82415e08.js","/astro/hoisted.js?q=3":"_astro/hoisted.5157f620.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Nav.jsx":"_astro/Nav.39c6e398.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/DownloadUownApp.jsx":"_astro/DownloadUownApp.76ef3473.js","@astrojs/react/client.js":"_astro/client.3002ca7c.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HeroSection2.jsx":"_astro/HeroSection2.c69e44a3.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Reviews":"_astro/Reviews.7d006383.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/StackedCarousel.jsx":"_astro/StackedCarousel.90ba9ef9.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/BenefitsSlice.jsx":"_astro/BenefitsSlice.7ca532c6.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HDIWSlice.jsx":"_astro/HDIWSlice.5fea332a.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Instructions.jsx":"_astro/Instructions.9e22426e.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/NavBar.jsx":"_astro/NavBar.b779e77b.js","/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/FreeChapters.jsx":"_astro/FreeChapters.e09ca6a4.js","astro:scripts/before-hydration.js":""},"assets":["/_astro/404-error.01050a38.png","/_astro/404-error-small.022f6abb.png","/_astro/PROPERTY_CROWDFUNDING_1.3133bb03.svg","/_astro/PROPERTY_CROWDFUNDING_3.7574d0cf.svg","/_astro/got_more_questions.5d5b7835.png","/_astro/thumbs-up.9a080146.png","/_astro/img-quarter-circle-black.512323bc.png","/_astro/img-sphere-green.967c1dc0.png","/_astro/help_main_image.79a7e47c.jpg","/_astro/help_main_image_mobile.3d1a8e5c.jpg","/_astro/help_main_image_ipad.f24e213f.jpg","/_astro/causes_main_image.d85caaa4.png","/_astro/img-sphere-half-gold.4984d7a9.png","/_astro/start-journey-cta.6403c8d7.png","/_astro/property_main_image.c7fa6705.png","/_astro/money_main_image.6af26cc1.png","/_astro/hl_main_image.3912679e.png","/_astro/img-large-sphere-white.13ea45ba.png","/_astro/img-large-sphere-metal.402e1655.png","/_astro/hub-causes.39663272.png","/_astro/hub-money.13f2284a.png","/_astro/img-large-cylinder-wood.6a33d348.png","/_astro/hub-property.57d20a93.png","/_astro/return-moneytowork.f1e3b8cc.mp4","/_astro/hub-hl.0fe75a49.png","/_astro/babs-safe.d60e6256.mp4","/_astro/return-invest.0a5d572c.mp4","/_astro/return-kickback.68b2fe85.mp4","/_astro/hero-img-lg.11075c19.jpg","/_astro/return-kerching.69fee828.mp4","/_astro/iwc-tried-and-tested.da50efc0.png","/_astro/iwc-transparent.0a2d42ca.png","/_astro/iwc-trustworthy.c7bfe3cb.png","/_astro/asah-solid-foundations.3b74254e.mp4","/_astro/asah-crowd-investment.b57805c2.mp4","/_astro/asah-supply-and-demand.5668d1fa.mp4","/_astro/trustpilot.56bb4973.png","/_astro/stars.cebfa5b2.png","/_astro/home-hero-img.0b5e08d2.jpg","/_astro/logo_times.f83f47aa.png","/_astro/uinuown-cta.f3d1ecad.png","/_astro/logo_inews.2a2f8440.png","/_astro/logo_business_north.9c8e3a7f.jpg","/_astro/logo_business_desk.fd4420fe.png","/_astro/logo_sun.9ca2a005.svg","/_astro/logo_telegraph.8a61ad9a.png","/_astro/logo_ngi.010e31c4.png","/_astro/logo_scotsman.e14217ab.png","/_astro/logo_traveller_choice.0f15c5f4.png","/_astro/logo_tripadvisor_best.c4ae25ca.png","/_astro/logo_booking.1a6ceada.png","/_astro/Reception-2.e6efb89b.jpg","/_astro/img-half-cylinder.d279f373.png","/_astro/img-large-sphere.64e9ba59.png","/_astro/contact-us.d6cec8de.png","/_astro/img-half-circle.1f901c7a.png","/_astro/Dezign.0a496b4b.jpg","/_astro/uown-and-investing.51e399fe.svg","/_astro/checkmark.d2b575cb.svg","/_astro/development-investment.18c136f2.svg","/_astro/less-do.2f28e8d3.svg","/_astro/less-money.b6f22588.svg","/_astro/less-know.7de7a16c.svg","/_astro/circle-icon-1.0628c9af.jpg","/_astro/circle-icon-2.5f19e949.jpg","/_astro/circle-icon-3.e242b725.jpg","/_astro/circle-icon-4.1dd19f8e.jpg","/_astro/benefits.e579b77a.webp","/_astro/checkmark.62e51395.png","/_astro/shield.1aa150d4.png","/_astro/lock.f062b023.png","/_astro/home.606c6099.png","/_astro/cat-glasses.e6a877fe.jpg","/_astro/kitchen.d6f8ce3e.jpg","/_astro/reception.b3d5721f.jpg","/_astro/tactic.7e657e45.svg","/_astro/lock.b3fb5554.svg","/_astro/bug.53b1402e.svg","/_astro/crowd.c8a1e8d9.svg","/_astro/grand_designs.cddd3df9.webp","/_astro/logo_business_north.0d2479be.png","/_astro/ideal-home.e1b01a4a.svg","/_astro/Setting-Dinner-Table.d9299642.webp","/_astro/play-download.3d0fe029.svg","/_astro/apple-download.3d1d7c86.svg","/_astro/uown-mobile.4430f4dd.webp","/_astro/prop-2.868020c9.png","/_astro/prop-1-sm.b31ae403.png","/_astro/prop-2-sm.b4160afa.png","/_astro/hero.df75b47d.png","/_astro/register.cd987e9f.png","/_astro/slice-real-estate.d3291519.png","/_astro/yellow-glow.4ee7eb8d.avif","/_astro/cardiff-main.35e2e882.webp","/_astro/prop-3-sm.3b519fa0.png","/_astro/prop-3.a69f918d.png","/_astro/withdraw.ea423909.png","/_astro/Books-on-coffee-table.f9934235.webp","/_astro/green-glow.9e919546.avif","/_astro/Sitting-Working-on-Laptop.68474fdc.webp","/_astro/together-we-achieve-cta.01b47bb1.png","/_astro/Shelving-and-Seating.3397cfed.webp","/_astro/phone-1.7d7fcfcd.png","/_astro/sin-return.8cb69cb1.mp4","/_astro/sin-uowners.7bd1e96d.mp4","/_astro/sin-value.c979a343.mp4","/_astro/home-inv-develop.2e6370b3.mp4","/_astro/hdiw.07f1d7ed.png","/_astro/home-inv-raise.56c1f810.mp4","/_astro/home-inv-acquire.4ff1f088.mp4","/_astro/home-inv-payout.7d5f4860.mp4","/_astro/unleash-the-power-cta.b402b604.png","/_astro/prop-1.40571ad2.png","/_astro/home-inv-select.0ce4608a.jpg","/_astro/three-dots.1f123b97.svg","/_astro/check-1.a6828a15.svg","/_astro/times-solid.258568e0.svg","/_astro/check-wicker.98ab4277.svg","/_astro/logo-light.24f9aac7.svg","/_astro/three-dots-wicker.d385e11e.svg","/_astro/ctaImage.90544db9.png","/_astro/times-solid-wicker.f3f63c9c.svg","/_astro/anchor-text.a182dabe.svg","/_astro/SIGNUP.551ac203.jpg","/_astro/PICK_PROJECT.0c078b3d.jpg","/_astro/SELECT.cbc9c334.jpg","/_astro/SIT_BACK.f187a334.jpg","/_astro/todson-house.e6cc1ee3.jpg","/_astro/the-bakery.5954fdfc.jpg","/_astro/wicker-island.6ec18f03.jpg","/_astro/village-street.e9845688.jpg","/_astro/db.4975dbc6.webp","/_astro/inl.ed0a1bb6.webp","/_astro/phone-frame.a0d93ab0.svg","/_astro/pyp.abacee33.webp","/_astro/coverImage.14ac2aa1.png","/_astro/author2.2e3ee7a7.jpg","/_astro/privacy-policy-1.0d5e7ee7.png","/_astro/privacy-policy-2.cbb00720.png","/_astro/img-large-quarter-circle-green.49e30dd9.png","/_astro/img-large-sphere-black.b510b10b.png","/_astro/img-large-squircle.6b234248.png","/_astro/img-large-screw.835a33e1.png","/_astro/causes.5e6616d2.css","/_redirects","/desktop.ini","/favicon.ico","/uown.png","/fonts/PlusJakartaSans-Bold.ttf","/fonts/PlusJakartaSans-ExtraBold.ttf","/fonts/PlusJakartaSans-Italic.ttf","/fonts/PlusJakartaSans-Medium.ttf","/previews/causes.png","/previews/contact.png","/previews/guides.png","/previews/help-center.png","/previews/hl.png","/previews/hub.png","/previews/main.png","/previews/money.png","/previews/properties.png","/previews/property.png","/_astro/Author.2f0ba0db.js","/_astro/Banner.fe886067.js","/_astro/BenefitsSlice.7ca532c6.js","/_astro/BlockSerializer.f4d2066b.js","/_astro/ButtonA.fd2a0e64.js","/_astro/CheckIcon.4de18d93.js","/_astro/ContactForm.ed31951c.js","/_astro/Container.977de002.js","/_astro/ContainerA.c65f3f99.js","/_astro/CtaSection.4844d8d6.js","/_astro/Dialog.9d39459a.js","/_astro/DownloadUownApp.76ef3473.js","/_astro/EmailForm.910b0117.js","/_astro/FAQs.00212a42.js","/_astro/Features.356e0720.js","/_astro/FreeChapters.e09ca6a4.js","/_astro/GlobalLeader.3f4ff296.js","/_astro/GridPattern.ff23c672.js","/_astro/GrowMoneyStake.79af207d.js","/_astro/GuideSent.1d857f37.js","/_astro/H2Serializer.7b441f85.js","/_astro/HDIWSlice.5fea332a.js","/_astro/Hero.75cea86a.js","/_astro/HeroSection.83621999.js","/_astro/HeroSection2.c69e44a3.js","/_astro/HeroSlice.39680dd8.js","/_astro/HeroStake.b4139472.js","/_astro/HorizontalScroll.a82708d2.js","/_astro/InfiniteScroll.1204197a.js","/_astro/InfiniteScrollSlice.3c4d81f8.js","/_astro/InfiniteScrollSlice.82415e08.js","/_astro/InsightsSection.212ea3b1.js","/_astro/Instructions.9e22426e.js","/_astro/Introduction.643184af.js","/_astro/Nav.39c6e398.js","/_astro/NavBar.b779e77b.js","/_astro/OptimizeRR.ad9efeed.js","/_astro/PRSlice.46bc6bec.js","/_astro/PhoneForm.6690d163.js","/_astro/PhoneFrame.d7f62544.js","/_astro/PrimaryFeatures.09f2e4a9.js","/_astro/ReviewTabs.d1160f6d.js","/_astro/Reviews.5b95e505.js","/_astro/Reviews.7d006383.js","/_astro/SVGVisualElement.f71a2c88.js","/_astro/SafetyStake.2b53cf3a.js","/_astro/Screencasts.4a22585b.js","/_astro/SectionHeading.ee95ee8f.js","/_astro/StackedCarousel.90ba9ef9.js","/_astro/TableOfContents.370262f8.js","/_astro/Tabs.0503ce99.js","/_astro/TilesSection.8c3e0ee2.js","/_astro/astro-assets-services.13b87c52.js","/_astro/bugs.fe7a60a6.js","/_astro/cardiff-main.af5de361.js","/_astro/causes.974f58c2.css","/_astro/client.3002ca7c.js","/_astro/client.75c2fd4f.js","/_astro/clsx.0839fdbe.js","/_astro/dialog.b88622b5.js","/_astro/focus-management.114d3f4b.js","/_astro/getInternationalPhoneNumberPrefix.743f5485.js","/_astro/green-glow.6dfbb112.js","/_astro/hoisted.3437f8e3.js","/_astro/hoisted.5157f620.js","/_astro/hoisted.5d40a5b1.js","/_astro/hoisted.8719866d.js","/_astro/ideal-home.78a7fa47.js","/_astro/index.09da4011.js","/_astro/index.17c849a1.js","/_astro/index.27d63c46.js","/_astro/index.3d7cdc72.css","/_astro/index.51868a37.js","/_astro/index.596ad335.js","/_astro/index.59c82a37.js","/_astro/index.b2ee4909.js","/_astro/index.de617592.css","/_astro/invest.9957ba17.css","/_astro/jsx-runtime.e0330bd5.js","/_astro/kitchen.1fd80d4b.js","/_astro/proxy.f8c92575.js","/_astro/resolve-elements.ad77d7d2.js","/_astro/times-solid-wicker.04fb6003.js","/_astro/uk-property-investment-guide.71d701a7.css","/_astro/uown-mobile.ce5fc763.js","/_astro/use-is-mounted.7aa88751.js","/_astro/use-motion-value.7ae4f8ff.js","/_astro/use-resolve-button-type.94a0d5cf.js","/_astro/use-tab-direction.0ef76b91.js","/_astro/use-transform.09c66904.js","/_astro/why-uown-hero-video-lg.a725a207.mp4","/_astro/why-uown-hero-video-md.68b4eb1e.mp4","/_astro/why-uown-hero-video-sm.cfae8d9c.mp4","/_astro/why-uown-hero-video-xl.c4e1dae8.mp4","/_astro/yellow-glow.46a16d56.js","/~partytown/partytown-atomics.js","/~partytown/partytown-media.js","/~partytown/partytown-sw.js","/~partytown/partytown.js"]});

export { manifest };

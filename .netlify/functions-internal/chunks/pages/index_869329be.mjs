import { a as $$Button, $ as $$Layout } from './404_1f20d35e.mjs';
import { f as createComponent, r as renderTemplate, e as createAstro, m as maybeRenderHead, g as addAttribute, i as renderComponent } from '../astro_ca9e373b.mjs';
import { c as $$Image } from './_slug__314c3ce5.mjs';
/* empty css                           */import 'clsx';
/* empty css                           */import { jsxs, jsx } from 'react/jsx-runtime';
import React from 'react';
import { StackedCarousel, ResponsiveContainer } from 'react-stacked-center-carousel';
import { a as Times } from './demoPage2_a9d3579e.mjs';

const $$HeroVideo = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate``;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HeroVideo.astro", void 0);

const Return = "/_astro/sin-return.8cb69cb1.mp4";

const Uowners = "/_astro/sin-uowners.7bd1e96d.mp4";

const Value = "/_astro/sin-value.c979a343.mp4";

const $$Astro$1 = createAstro("https://www.uown.co");
const $$SInOption = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$SInOption;
  const { textH5, imgUrl, text2xl, textp, btnText, btnUrl } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<div class="max-w-xs md:max-w-sm lg:max-w-md p-6 lg:px-8 text-center rounded-cxl bg-white mx-auto py-12 shadow-light mb-12" data-astro-cid-ptkwmqzm> <h5 class="text-3xl lg:text-5xl tracking-normal font-extrabold pb-4" data-astro-cid-ptkwmqzm> ${textH5} </h5> <p class="text-lg lg:text-2xl tracking-wide font-bold pb-6" data-astro-cid-ptkwmqzm> ${text2xl} </p> <video playsinline="" autoplay loop muted class="mx-auto py-6 w-[180px] lg:w-[240px]" data-astro-cid-ptkwmqzm> <source${addAttribute(imgUrl, "src")} type="video/mp4" data-astro-cid-ptkwmqzm> </video> <p class="text-base lg:text-xl tracking-wider font-regular pb-6 px-3" data-astro-cid-ptkwmqzm> ${textp} </p> <a${addAttribute(btnUrl, "href")} data-astro-cid-ptkwmqzm>${renderComponent($$result, "Button", $$Button, { "type": "button", "color": "btn-black", "text": btnText, "data-astro-cid-ptkwmqzm": true })}</a> </div> `;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/SInOption.astro", void 0);

const $$StrengthInNumbersSection = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${maybeRenderHead()}<div class="hidden container md:flex items-center justify-evenly flex-wrap mx-auto pt-24" data-astro-cid-aoxzrf3e> ${renderComponent($$result, "SInOpt", $$SInOption, { "textH5": "\xA370m+", "text2xl": "Of  Project Value", "textp": "We've handled big projects and delivered big results.", "imgUrl": Value, "btnText": "View Projects", "btnUrl": "https://app.uown.co/properties", "data-astro-cid-aoxzrf3e": true })} ${renderComponent($$result, "SInOpt", $$SInOption, { "textH5": "17.7%", "text2xl": "Average Project Return", "textp": "With our most successful project returning a huge 26%.", "imgUrl": Return, "btnText": "View Projects", "btnUrl": "https://app.uown.co/properties", "data-astro-cid-aoxzrf3e": true })} ${renderComponent($$result, "SInOpt", $$SInOption, { "textH5": "11,000+", "text2xl": "UOWNers", "textp": "The UOWN crowd is growing by the day...", "imgUrl": Uowners, "btnText": "Get Started", "btnUrl": "https://app.uown.co/register", "data-astro-cid-aoxzrf3e": true })} </div> `;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/StrengthInNumbersSection.astro", void 0);

const $$SInSectionCarousel = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${maybeRenderHead()}<div class="carousel-container md:hidden relative w-full bg-black"> <!-- Carousel wrapper --> <div class="carousel_items relative overflow-hidden h-[600px] lg:h-[700px] mt-8 bg-black"> <!-- Item 1 --> <div class="carousel_item"> ${renderComponent($$result, "SInOpt", $$SInOption, { "textH5": "\xA370m+", "text2xl": "Of  Project Value", "textp": "We've handled big projects and delivered big results.", "imgUrl": Value, "btnText": "View Projects", "btnUrl": "https://app.uown.co/properties" })} </div> <!-- Item 2 --> <div class="carousel_item"> ${renderComponent($$result, "SInOpt", $$SInOption, { "textH5": "17.7%", "text2xl": "Average Project Return", "textp": "With our most successful project returning a huge 26%.", "imgUrl": Return, "btnText": "View Projects", "btnUrl": "https://app.uown.co/properties" })} </div> <!-- Item 3 --> <div class="carousel_item"> ${renderComponent($$result, "SInOpt", $$SInOption, { "textH5": "11,000+", "text2xl": "UOWNers", "textp": "The UOWN crowd is growing by the day...", "imgUrl": Uowners, "btnText": "Get Started", "btnUrl": "https://app.uown.co/register" })} </div> </div> </div>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/SInSectionCarousel.astro", void 0);

const Stars = {"src":"/_astro/stars.cebfa5b2.png","width":118,"height":22,"format":"png"};

const Slide = React.memo(function(StackedCarouselSlideProps) {
  const {
    data,
    dataIndex,
    isCenterSlide,
    swipeTo,
    slideIndex
  } = StackedCarouselSlideProps;
  const text = data[dataIndex].text;
  const name = data[dataIndex].name;
  return /* @__PURE__ */ jsxs("div", { className: " card-card", draggable: false, children: [
    /* @__PURE__ */ jsx("div", { className: `cover fill ${isCenterSlide ? "off" : "on"}`, children: /* @__PURE__ */ jsx(
      "div",
      {
        className: "fill",
        onClick: () => {
          if (!isCenterSlide)
            swipeTo(slideIndex);
        }
      }
    ) }),
    /* @__PURE__ */ jsxs("div", { className: "detail fill flex flex-col p-3 md:p-6 w-full card-height", children: [
      /* @__PURE__ */ jsx("div", { className: "header flex items-center pb-12", children: /* @__PURE__ */ jsx("p", { className: "text-2xl tracking-normal font-extrabold", children: name }) }),
      /* @__PURE__ */ jsx("p", { className: "text-base md:text-lg tracking-wide font-regular", children: text }),
      /* @__PURE__ */ jsx(
        "img",
        {
          style: { width: 118, height: "22px" },
          alt: "stars",
          className: "mt-auto",
          src: Stars.src
        }
      )
    ] })
  ] });
});

const data = [
  {
    name: "Zak",
    text: "“ Good online service with clear information about investments and risk. great platform as you can invest from small to large amounts. The team at UOWN are incredibly useful, professional and switched on, The team at UOWN are incredibly useful.”"
  },
  {
    name: "Fraser",
    text: "“Excellent customer service, and clear that the company is aiming to be transparent and enhtusiastic.”"
  },
  {
    name: "Rebecca",
    text: "“Easy for the “little people” to  the invest. Rents always on time. Word Portfolio growing.”"
  }
];
const StackedCarouselex = () => {
  const ref = React.useRef(StackedCarousel);
  return /* @__PURE__ */ jsx("div", { className: "hidden card md:block", children: /* @__PURE__ */ jsxs("div", { style: { width: "100%", position: "relative" }, children: [
    /* @__PURE__ */ jsx(
      ResponsiveContainer,
      {
        carouselRef: ref,
        render: (width, carouselRef) => {
          return /* @__PURE__ */ jsx(
            StackedCarousel,
            {
              ref: carouselRef,
              slideComponent: Slide,
              slideWidth: 360,
              carouselWidth: width,
              data,
              maxVisibleSlide: 3,
              disableSwipe: true,
              customScales: [1, 0.85, 0.7],
              transitionTime: 450,
              height: 550
            }
          );
        }
      }
    ),
    /* @__PURE__ */ jsx(
      "div",
      {
        className: "card-button left",
        size: "small",
        onClick: () => ref.current?.goBack(),
        children: /* @__PURE__ */ jsx(
          "button",
          {
            type: "button",
            className: "absolute top-0 left-0 z-30 flex items-center justify-center h-full pl-[2%] lg:pl-[7%] cursor-pointer group focus:outline-none",
            "aria-label": "Previous",
            "data-carousel-prev": true,
            children: /* @__PURE__ */ jsxs(
              "svg",
              {
                width: "40",
                height: "40",
                viewBox: "0 0 40 40",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg",
                children: [
                  /* @__PURE__ */ jsxs("g", { clipPath: "url(#clip0_826_14695)", children: [
                    /* @__PURE__ */ jsx(
                      "circle",
                      {
                        cx: "20",
                        cy: "20",
                        r: "20",
                        transform: "rotate(-180 20 20)",
                        fill: "black"
                      }
                    ),
                    /* @__PURE__ */ jsx(
                      "path",
                      {
                        d: "M29.9453 18.4714L29.9453 22.0254L16.4645 22.0254L22.5002 28.582L17.7513 28.582L10.6432 21.1063L10.6432 19.4518L17.7513 11.9148L22.5002 11.9148L16.4951 18.4714L29.9453 18.4714Z",
                        fill: "white"
                      }
                    )
                  ] }),
                  /* @__PURE__ */ jsx("defs", { children: /* @__PURE__ */ jsx("clipPath", { id: "clip0_826_14695", children: /* @__PURE__ */ jsx(
                    "rect",
                    {
                      width: "1920",
                      height: "9511",
                      fill: "white",
                      transform: "translate(-528 -5776)"
                    }
                  ) }) })
                ]
              }
            )
          }
        )
      }
    ),
    /* @__PURE__ */ jsx(
      "div",
      {
        className: "card-button right",
        size: "small",
        onClick: () => ref.current?.goNext(),
        children: /* @__PURE__ */ jsx(
          "button",
          {
            type: "button",
            className: "absolute top-0 right-0 z-30 flex items-center justify-center h-full pr-[2%] lg:pr-[7%] cursor-pointer group focus:outline-none",
            "aria-label": "Next",
            "data-carousel-next": true,
            children: /* @__PURE__ */ jsxs(
              "svg",
              {
                width: "40",
                height: "40",
                viewBox: "0 0 40 40",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg",
                children: [
                  /* @__PURE__ */ jsx("circle", { cx: "20", cy: "20", r: "20", fill: "black" }),
                  /* @__PURE__ */ jsx(
                    "path",
                    {
                      d: "M10.0547 21.5286L10.0547 17.9746L23.5355 17.9746L17.4998 11.418L22.2487 11.418L29.3568 18.8937L29.3568 20.5482L22.2487 28.0852L17.4998 28.0852L23.5049 21.5286L10.0547 21.5286Z",
                      fill: "white"
                    }
                  )
                ]
              }
            )
          }
        )
      }
    )
  ] }) });
};

const $$Carousel = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate``;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Carousel.astro", void 0);

const Trustpilot = {"src":"/_astro/trustpilot.56bb4973.png","width":163,"height":43,"format":"png"};

const HeroPlaceholder = {"src":"/_astro/home-hero-img.0b5e08d2.jpg","width":1920,"height":1080,"format":"jpg"};

const SolidFoundations = "/_astro/asah-solid-foundations.3b74254e.mp4";

const CrowdInvestment = "/_astro/asah-crowd-investment.b57805c2.mp4";

const SupplyDemand = "/_astro/asah-supply-and-demand.5668d1fa.mp4";

const UInUOWN = {"src":"/_astro/uinuown-cta.f3d1ecad.png","width":389,"height":480,"format":"png"};

const IN = {"src":"/_astro/logo_inews.2a2f8440.png","width":900,"height":652,"format":"png"};

const BusinessUpNorth = {"src":"/_astro/logo_business_north.9c8e3a7f.jpg","width":391,"height":129,"format":"jpg"};

const BusinessDesk = {"src":"/_astro/logo_business_desk.fd4420fe.png","width":225,"height":225,"format":"png"};

const $$Astro = createAstro("https://www.uown.co");
const $$Index = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Index;
  const purl = Astro2.url.origin + "/previews/main.png";
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Property Crowdfunding & Investment Platform | UOWN", "classes": "", "description": "Join UOWN, the leading property crowdfunding and investment platform. Explore profitable investment opportunities today.", "purl": purl, "data-astro-cid-j7pv25f6": true }, { "addJs": ($$result2) => renderTemplate`${renderComponent($$result2, "HeroVideo", $$HeroVideo, { "slot": "addJs", "data-astro-cid-j7pv25f6": true })}`, "addJs2": ($$result2) => renderTemplate`${renderComponent($$result2, "Carousel", $$Carousel, { "slot": "addJs2", "data-astro-cid-j7pv25f6": true })}`, "default": ($$result2) => renderTemplate`   ${maybeRenderHead()}<main class="main md:pb-64 pb-40" data-astro-cid-j7pv25f6> <section class="" data-astro-cid-j7pv25f6> <video id="hero"${addAttribute(HeroPlaceholder.src, "poster")} playsinline="" width="100vw" height="100vh" autoplay loop muted data-astro-transition-persist="media-player" class="w-screen object-fill" data-astro-cid-j7pv25f6> <source src="" type="video/mp4" data-astro-cid-j7pv25f6> </video> </section> <section class="bg-black-300 md:pb-24 lxl:pb-40 pt-24" data-astro-cid-j7pv25f6> <div class="heading text-white mx-auto text-center max-w-2xl lg:px-0 px-11" data-astro-cid-j7pv25f6> <p class="text-3xl md:text-4xl lg:text-5xl tracking-normal font-extrabold pb-4" data-astro-cid-j7pv25f6>
Strength in numbers
</p> <p class="text-base lg:text-2xl md:text-xl tracking-normal font-regular" data-astro-cid-j7pv25f6>
The numbers say it all. That’s the power of our crowd.
</p> </div> ${renderComponent($$result2, "StrengthInNumbersSection", $$StrengthInNumbersSection, { "data-astro-cid-j7pv25f6": true })} ${renderComponent($$result2, "SInCarousel", $$SInSectionCarousel, { "data-astro-cid-j7pv25f6": true })} </section> <!-- UOWN PRESS --> <div class="bg-white py-24 sm:py-32" data-astro-cid-j7pv25f6> <div class="mx-auto max-w-7xl px-6 lg:px-8" data-astro-cid-j7pv25f6> <h2 class="text-center text-5xl font-semibold leading-8 text-gray-900" data-astro-cid-j7pv25f6>
As seen in
</h2> <div class="mx-auto mt-16 grid max-w-lg grid-cols-4 items-center justify-center gap-x-8 gap-y-8 sm:max-w-xs sm:grid-cols-1 md:grid-cols-2 sm:gap-x-10 lg:mx-0 lg:max-w-none lg:grid-cols-4" data-astro-cid-j7pv25f6> <a href="https://www.thetimes.co.uk/article/the-new-way-for-millennials-to-own-a-home-xvb5pg387" data-astro-cid-j7pv25f6>${renderComponent($$result2, "Image", $$Image, { "class": "col-span-2 max-h-16 w-full object-contain lg:col-span-1", "src": Times, "alt": "Times", "data-astro-cid-j7pv25f6": true })}</a> <a href="https://inews.co.uk/inews-lifestyle/money/as-a-student-aged-19-can-i-raise-5000-for-a-new-saxophone-through-property-117786" data-astro-cid-j7pv25f6>${renderComponent($$result2, "Image", $$Image, { "class": "col-span-2 max-h-16 w-full object-contain lg:col-span-1", "src": IN, "alt": "I News", "data-astro-cid-j7pv25f6": true })}</a> <a href="https://www.thebusinessdesk.com/yorkshire/news/2009845-2009845" data-astro-cid-j7pv25f6>${renderComponent($$result2, "Image", $$Image, { "class": "col-span-2 max-h-16 w-full object-contain sm:col-start-2 lg:col-span-1", "src": BusinessDesk, "alt": "Business Desk", "data-astro-cid-j7pv25f6": true })}</a> <a href="https://www.businessupnorth.co.uk/leeds-property-crowdfunder-uown-hosts-first-monopoly-championship/" data-astro-cid-j7pv25f6>${renderComponent($$result2, "Image", $$Image, { "class": "col-span-2 max-h-16 w-full object-contain lg:col-span-1", "src": BusinessUpNorth, "alt": "Business Up North", "data-astro-cid-j7pv25f6": true })}</a> </div> </div> </div> <section class="lg:pt-40 py-24" data-astro-cid-j7pv25f6> <div class="heading mx-auto text-center max-w-2xl px-11 pb-24" data-astro-cid-j7pv25f6> <p class="lg:text-5xl md:text-4xl text-3xl tracking-normal font-extrabold pb-4" data-astro-cid-j7pv25f6>
As safe as houses
</p> <p class="lg:text-2xl md:text-xl text-base tracking-normal font-regular" data-astro-cid-j7pv25f6>
The UOWN investment strategy is a lower risk option for long
					term investment for a number of reasons...
</p> </div> <div class="max-w-6xl flex flex-wrap items-center mx-auto px-11 lg:px-0 lg:py-44 pb-24" data-astro-cid-j7pv25f6> <div class="max-w-xl mx-auto text-center order-2 lg:order-1" data-astro-cid-j7pv25f6> <h3 class="text-3xl md:text-4xl lg:text-5xl tracking-normal font-extrabold pb-4" data-astro-cid-j7pv25f6>
Supply & demand
</h3> <p class="text-base md:text-xl tracking-normal font-regular pb-6" data-astro-cid-j7pv25f6>
The UK property market is the perfect choice for
						investors. With limited supply and a growing population,
						it's a smart investment. Since 1952 property prices have
						doubled on average every 8.5 years.
</p> </div> <video playsinline="" autoplay loop muted class="mx-auto order-1 lg:order-2" data-astro-cid-j7pv25f6> <source${addAttribute(SupplyDemand, "src")} type="video/mp4" data-astro-cid-j7pv25f6> </video> </div> <div class="max-w-6xl flex flex-wrap items-center mx-auto px-11 lg:px-0 lg:pb-44 pb-24" data-astro-cid-j7pv25f6> <div class="max-w-xl mx-auto text-center order-2" data-astro-cid-j7pv25f6> <h3 class="text-3xl md:text-4xl lg:text-5xl tracking-normal font-extrabold pb-4" data-astro-cid-j7pv25f6>
Solid foundations
</h3> <p class="text-base md:text-xl tracking-normal font-regular pb-6" data-astro-cid-j7pv25f6>
UOWN is backed by The Parklane Group, a trusted name in
						the UK property market with over 45 years of group
						experience. Operating nationally, with a focus on
						high-quality properties, The Parklane Group retains a
						competitive edge in the North of England.
</p> </div> <video playsinline="" autoplay loop muted class="mx-auto order-1" data-astro-cid-j7pv25f6> <source${addAttribute(SolidFoundations, "src")} type="video/mp4" data-astro-cid-j7pv25f6> </video> </div> <div class="max-w-6xl flex flex-wrap items-center mx-auto px-11 lg:px-0 lg:pb-44 pb-40" data-astro-cid-j7pv25f6> <div class="max-w-xl mx-auto text-center order-2 lg:order-1" data-astro-cid-j7pv25f6> <h3 class="text-3xl md:text-4xl lg:text-5xl tracking-normal font-extrabold pb-4" data-astro-cid-j7pv25f6>
Crowd investment
</h3> <p class="text-base md:text-xl tracking-normal font-regular pb-6" data-astro-cid-j7pv25f6>
Joining forces with your fellow UOWNers opens the door
						to large-scale projects and large-scale returns that are
						usually off limits. By investing as part of a crowd, you
						can diversify across multiple projects and invest
						smaller, more manageable amounts. That’s the power of
						us.
</p> </div> <video playsinline="" autoplay loop muted class="mx-auto order-1 lg:order-2" data-astro-cid-j7pv25f6> <source${addAttribute(CrowdInvestment, "src")} type="video/mp4" data-astro-cid-j7pv25f6> </video> </div> </section> <section class="pb-40 pt-24 bg-gray-pages px-6" data-astro-cid-j7pv25f6> <div class="heading mx-auto text-center lg:px-0 px-11" data-astro-cid-j7pv25f6> <p class="text-3xl lg:text-5xl md:text-4xl tracking-normal font-extrabold pb-4" data-astro-cid-j7pv25f6>
Don’t just take our word for it
</p> <p class="text-base lg:text-2xl md:text-xl tracking-normal font-regular pb-12" data-astro-cid-j7pv25f6>
See what existing UOWNers had to say about us. They let us
					know that we’re on the right track.
</p> <div class="flex justify-center items-center" data-astro-cid-j7pv25f6> <span class="text-2xl tracking-normal font-bold" data-astro-cid-j7pv25f6>4.9/5</span><a aria-label="Trustpilot rating" href="https://uk.trustpilot.com/review/uown.co" target="_blank" data-astro-cid-j7pv25f6>${renderComponent($$result2, "Image", $$Image, { "src": Trustpilot, "alt": "Trustpilot", "data-astro-cid-j7pv25f6": true })}</a> </div> </div> ${renderComponent($$result2, "StackedCarouselex", StackedCarouselex, { "client:load": true, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/StackedCarousel.jsx", "client:component-export": "default", "data-astro-cid-j7pv25f6": true })} <div class="max-w-xs md:hidden flex flex-col p-6 mx-auto rounded-cxl bg-white mt-12" data-astro-cid-j7pv25f6> <div class="header flex items-center pb-12" data-astro-cid-j7pv25f6> <p class="text-2xl tracking-normal font-extrabold" data-astro-cid-j7pv25f6>Zak</p> </div> <p class="text-base md:text-lg tracking-wide font-regular pb-12" data-astro-cid-j7pv25f6>
“ Good online service with clear information about
					investments and risk. great platform as you can invest from
					small to large amounts. The team at UOWN are incredibly
					useful, professional and switched on, The team at UOWN are
					incredibly useful.”
</p> ${renderComponent($$result2, "Image", $$Image, { "style": { width: "118px", height: "22px" }, "alt": "stars", "class": "mt-auto", "src": Stars, "data-astro-cid-j7pv25f6": true })} </div> </section> <section class="mt-24 py-12 md:py-24 max-w-6xl lg:mx-auto light-gradient rounded-cxl lg:px-0 mx-7 md:mx-32" data-astro-cid-j7pv25f6> <div class="flex flex-wrap flex-col lg:flex-row items-center justify-center mx-auto px-11 lg:px-0" data-astro-cid-j7pv25f6> ${renderComponent($$result2, "Image", $$Image, { "class": "w-52 md:w-full md:max-w-xs lg:max-w-sm pb-12", "src": UInUOWN, "alt": "", "data-astro-cid-j7pv25f6": true })} <div class="max-w-sm lg:max-w-xs lg:ml-28 lg:pl-10 text-center lg:text-left" data-astro-cid-j7pv25f6> <h3 class="text-3xl md:text-4xl lg:text-5xl tracking-normal font-extrabold pb-4" data-astro-cid-j7pv25f6>
Put the U into UOWN
</h3> <p class="text-base md:text-xl lg:text-2xl tracking-normal font-regular pb-6 lg:pb-12" data-astro-cid-j7pv25f6>
Join thousands of others by becoming part of our crowd.
</p> <a href="https://app.uown.co/register" data-astro-cid-j7pv25f6>${renderComponent($$result2, "Button", $$Button, { "type": "button", "color": "btn-black btn-get-started", "text": "Get Started", "data-astro-cid-j7pv25f6": true })}</a> </div> </div> </section> </main> ` })} `;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/index.astro", "self");

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/index.astro";
const $$url = "";

const index = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
    __proto__: null,
    default: $$Index,
    file: $$file,
    url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

export { $$Carousel as $, BusinessDesk as B, IN as I, Trustpilot as T, BusinessUpNorth as a, index as i };

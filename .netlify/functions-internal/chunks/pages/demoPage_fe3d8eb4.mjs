import { $ as $$Layout } from './404_1f20d35e.mjs';
import { f as createComponent, r as renderTemplate, i as renderComponent } from '../astro_ca9e373b.mjs';
import clsx from 'clsx';
import { jsxs, jsx, Fragment } from 'react/jsx-runtime';
import { useRef, useState, useEffect, useMemo, forwardRef, useId, Fragment as Fragment$1 } from 'react';
import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/react';
import { useInView, motion, AnimatePresence } from 'motion/react';
import { useDebouncedCallback } from 'use-debounce';
import Link from 'next/link.js';

const frame = {"src":"/_astro/phone-frame.a0d93ab0.svg","width":366,"height":729,"format":"svg"};

function PlaceholderFrame(props) {
  return /* @__PURE__ */ jsxs("svg", { viewBox: "0 0 366 729", "aria-hidden": "true", ...props, children: [
    /* @__PURE__ */ jsx(
      "path",
      {
        fill: "#F2F2F2",
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M300.092 1c41.22 0 63.223 21.99 63.223 63.213V184.94c-.173.184-.329.476-.458.851.188-.282.404-.547.647-.791.844-.073 2.496.257 2.496 2.157V268.719c-.406 2.023-2.605 2.023-2.605 2.023a7.119 7.119 0 0 1-.08-.102v394.462c0 41.213-22.001 63.212-63.223 63.212h-95.074c-.881-.468-2.474-.795-4.323-.838l-33.704-.005-.049.001h-.231l-.141-.001c-2.028 0-3.798.339-4.745.843H66.751c-41.223 0-63.223-21.995-63.223-63.208V287.739c-.402-.024-2.165-.23-2.524-2.02v-.973A2.039 2.039 0 0 1 1 284.62v-47.611c0-.042.001-.084.004-.126v-.726c0-1.9 1.652-2.23 2.496-2.157l.028.028v-16.289c-.402-.024-2.165-.23-2.524-2.02v-.973A2.039 2.039 0 0 1 1 214.62v-47.611c0-.042.001-.084.004-.126v-.726c0-1.9 1.652-2.23 2.496-2.157l.028.028v-26.041a2.26 2.26 0 0 0 .093-.236l-.064-.01a3.337 3.337 0 0 1-.72-.12l-.166-.028A2 2 0 0 1 1 135.62v-24.611a2 2 0 0 1 1.671-1.973l.857-.143v-44.68C3.528 22.99 25.53 1 66.75 1h233.341ZM3.952 234.516a5.481 5.481 0 0 0-.229-.278c.***************.228.278Zm89.99-206.304A4.213 4.213 0 0 0 89.727 24H56.864C38.714 24 24 38.708 24 56.852v618.296C24 693.292 38.714 708 56.864 708h250.272c18.15 0 32.864-14.708 32.864-32.852V56.852C340 38.708 325.286 24 307.136 24h-32.864a4.212 4.212 0 0 0-4.213 4.212v2.527c0 10.235-8.3 18.532-18.539 18.532H112.48c-10.239 0-18.539-8.297-18.539-18.532v-2.527Z"
      }
    ),
    /* @__PURE__ */ jsx("rect", { x: "154", y: "29", width: "56", height: "5", rx: "2.5", fill: "#D4D4D4" })
  ] });
}
function PhoneFrame({
  className,
  children,
  priority = false,
  ...props
}) {
  return /* @__PURE__ */ jsxs("div", { className: clsx("relative aspect-[366/729]", className), ...props, children: [
    /* @__PURE__ */ jsx("div", { className: "absolute inset-y-[calc(1/729*100%)] left-[calc(7/729*100%)] right-[calc(5/729*100%)] rounded-[calc(58/366*100%)/calc(58/729*100%)] shadow-2xl" }),
    /* @__PURE__ */ jsx("div", { className: "absolute left-[calc(23/366*100%)] top-[calc(23/729*100%)] grid h-[calc(686/729*100%)] w-[calc(318/366*100%)] transform grid-cols-1 overflow-hidden bg-white pt-[calc(23/318*100%)]", children }),
    /* @__PURE__ */ jsx(PlaceholderFrame, { className: "pointer-events-none absolute inset-0 h-full w-full fill-gray-100" }),
    /* @__PURE__ */ jsx(
      "img",
      {
        src: frame.src,
        alt: "",
        className: "pointer-events-none absolute inset-0 h-full w-full"
      }
    )
  ] });
}

function Container({
  className,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    "div",
    {
      className: clsx("mx-auto max-w-7xl px-4 sm:px-6 lg:px-8", className),
      ...props
    }
  );
}

const reviews = [
  {
    title: "This is the best place for returns & top customer service",
    body: "I have been thoroughly impressed with my experience using UOWN.co. ",
    author: "Aliza Ayaz",
    rating: 5
  },
  {
    title: "I have been investing with UOWN for several years now and they have never let me down.",
    body: "UOWN allows me to invest in property through an expert team, without the time commitment of doing it all myself.",
    author: "Paul",
    rating: 5
  },
  {
    title: "Great service",
    body: "This company is an active business and this has given me confidence in using the platform for the future.",
    author: "Kyaw L Saw",
    rating: 5
  },
  {
    title: "Used Uown for years, great experience",
    body: "I used uown for 3-4 years, and earned a very solid return. When it came time to withdraw basically all my portfolio, I was able to do so very quickly, with no hassle.",
    author: "Jasim",
    rating: 5
  },
  {
    title: "What a great company",
    body: "What a great company. Had an issue with one of my bank transfers, it was sorted out by one of the owners of the company.",
    author: "Craig T",
    rating: 5
  },
  {
    title: "Great place to invest on properties.",
    body: "Great place to invest on properties.I have joined them long time and had great returns.",
    author: "Shamoon Mushtaq",
    rating: 5
  },
  {
    title: "Fantastic opportunities",
    body: "Fantastic opportunities and these guys have always delivered.",
    author: "Naveen Ahmed",
    rating: 5
  },
  {
    title: "Great returns so far.",
    body: "UOWN is a fantastic place to invest. They offer several properties which allows you to diversify, but also new properties enter their portfolio every couple of months. Well worth it!",
    author: "Swissmat",
    rating: 5
  },
  {
    title: "Can't recommend highly enough!",
    body: "Best kept secret in the investment sector. Nailed on for returns after returns.",
    author: "Adam Smith",
    rating: 5
  },
  {
    title: "Very happy I found this little gem",
    body: "I've made multiple investments through the UOWN platform, and every single time they've delivered what they said they would.",
    author: "Matt Lord",
    rating: 5
  },
  {
    title: "Great investment platform",
    body: "Great investment platform that delivers excellent returns. Have invested in multiple projects.",
    author: "S.",
    rating: 5
  },
  {
    title: "Very pleased and will continue to invest with UOWN!",
    body: "I’ve invested with UOWN for some years now, and have always been very pleased with both the investments and the service from UOWN.",
    author: "Joe Dooley",
    rating: 5
  },
  {
    title: "Excellent service",
    body: "The right platform to invest your savings and get value back.",
    author: "Hugo Shepherd",
    rating: 5
  },
  {
    title: "Great company",
    body: "Great company. Never had any problems with them and got steady returns on my investments. Encouraged my parents and brother to use them, and would recommend them to anyone.",
    author: "Thomas",
    rating: 5
  }
];
function StarIcon(props) {
  return /* @__PURE__ */ jsx("svg", { viewBox: "0 0 20 20", "aria-hidden": "true", ...props, children: /* @__PURE__ */ jsx("path", { d: "M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" }) });
}
function StarRating({ rating }) {
  return /* @__PURE__ */ jsx("div", { className: "flex", children: [...Array(5).keys()].map((index) => /* @__PURE__ */ jsx(
    StarIcon,
    {
      className: clsx(
        "h-5 w-5",
        rating > index ? "fill-mint-500" : "fill-gray-300"
      )
    },
    index
  )) });
}
function Review({
  title,
  body,
  author,
  rating,
  className,
  ...props
}) {
  let animationDelay = useMemo(() => {
    let possibleAnimationDelays = ["0s", "0.1s", "0.2s", "0.3s", "0.4s", "0.5s"];
    return possibleAnimationDelays[Math.floor(Math.random() * possibleAnimationDelays.length)];
  }, []);
  return /* @__PURE__ */ jsxs(
    "figure",
    {
      className: clsx(
        "animate-fade-in rounded-3xl bg-white p-6 opacity-0 shadow-md rounded-cmd shadow-gray-900/5",
        className
      ),
      style: { animationDelay },
      ...props,
      children: [
        /* @__PURE__ */ jsxs("blockquote", { className: "text-gray-900", children: [
          /* @__PURE__ */ jsx(StarRating, { rating }),
          /* @__PURE__ */ jsx("p", { className: "mt-4 text-lg font-semibold leading-6 before:content-['“'] after:content-['”']", children: title }),
          /* @__PURE__ */ jsx("p", { className: "mt-3 text-base leading-7", children: body })
        ] }),
        /* @__PURE__ */ jsx("figcaption", { className: "mt-3 text-sm text-gray-600 before:content-['–_']", children: author })
      ]
    }
  );
}
function splitArray(array, numParts) {
  let result = [];
  for (let i = 0; i < array.length; i++) {
    let index = i % numParts;
    if (!result[index]) {
      result[index] = [];
    }
    result[index].push(array[i]);
  }
  return result;
}
function ReviewColumn({
  reviews: reviews2,
  className,
  reviewClassName,
  msPerPixel = 0
}) {
  let columnRef = useRef(null);
  let [columnHeight, setColumnHeight] = useState(0);
  let duration = `${columnHeight * msPerPixel}ms`;
  useEffect(() => {
    if (!columnRef.current) {
      return;
    }
    let resizeObserver = new window.ResizeObserver(() => {
      setColumnHeight(columnRef.current?.offsetHeight ?? 0);
    });
    resizeObserver.observe(columnRef.current);
    return () => {
      resizeObserver.disconnect();
    };
  }, []);
  return /* @__PURE__ */ jsx(
    "div",
    {
      ref: columnRef,
      className: clsx("animate-marquee space-y-8 py-4", className),
      style: { "--marquee-duration": duration },
      children: reviews2.concat(reviews2).map((review, reviewIndex) => /* @__PURE__ */ jsx(
        Review,
        {
          "aria-hidden": reviewIndex >= reviews2.length,
          className: reviewClassName?.(reviewIndex % reviews2.length),
          ...review
        },
        reviewIndex
      ))
    }
  );
}
function ReviewGrid() {
  let containerRef = useRef(null);
  let isInView = useInView(containerRef, { once: true, amount: 0.4 });
  let columns = splitArray(reviews, 3);
  let column1 = columns[0];
  let column2 = columns[1];
  let column3 = splitArray(columns[2], 2);
  return /* @__PURE__ */ jsxs(
    "div",
    {
      ref: containerRef,
      className: "relative -mx-4 mt-16 grid h-[49rem] max-h-[150vh] grid-cols-1 items-start gap-8 overflow-hidden px-4 sm:mt-20 md:grid-cols-2 lg:grid-cols-3",
      children: [
        isInView && /* @__PURE__ */ jsxs(Fragment, { children: [
          /* @__PURE__ */ jsx(
            ReviewColumn,
            {
              reviews: [...column1, ...column3.flat(), ...column2],
              reviewClassName: (reviewIndex) => clsx(
                reviewIndex >= column1.length + column3[0].length && "md:hidden",
                reviewIndex >= column1.length && "lg:hidden"
              ),
              msPerPixel: 10
            }
          ),
          /* @__PURE__ */ jsx(
            ReviewColumn,
            {
              reviews: [...column2, ...column3[1]],
              className: "hidden md:block",
              reviewClassName: (reviewIndex) => reviewIndex >= column2.length ? "lg:hidden" : "",
              msPerPixel: 15
            }
          ),
          /* @__PURE__ */ jsx(
            ReviewColumn,
            {
              reviews: column3.flat(),
              className: "hidden lg:block",
              msPerPixel: 10
            }
          )
        ] }),
        /* @__PURE__ */ jsx("div", { className: "pointer-events-none absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-gray-10" }),
        /* @__PURE__ */ jsx("div", { className: "pointer-events-none absolute inset-x-0 bottom-0 h-32 bg-gradient-to-t from-gray-10" })
      ]
    }
  );
}
function Reviews() {
  return /* @__PURE__ */ jsx(
    "section",
    {
      id: "reviews",
      "aria-labelledby": "reviews-title",
      className: "bg-gray-10 pb-16 pt-20 sm:pb-24 sm:pt-32",
      children: /* @__PURE__ */ jsxs(Container, { children: [
        /* @__PURE__ */ jsxs("div", { className: "max-w-3xl mx-auto text-center px-6", children: [
          /* @__PURE__ */ jsx("h2", { className: "font-display text-4xl font-bold tracking-tight text-slate-900", children: "See what our customers have to say..." }),
          /* @__PURE__ */ jsx("p", { className: "mt-4 text-lg tracking-tight text-slate-600", children: "We have been delivering fantastic returns for our investors since 2016. With a trustpilot score of 4.9 we are proud of what we have achieved for our tens of thousands of investors." })
        ] }),
        /* @__PURE__ */ jsx(ReviewGrid, {})
      ] })
    }
  );
}

function Logo(props) {
  return /* @__PURE__ */ jsxs("svg", { width: "48", height: "24", viewBox: "0 0 48 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", ...props, children: [
    /* @__PURE__ */ jsx("path", { d: "M20.3391 14.2694C20.3391 16.8492 19.3167 19.3234 17.4967 21.1476C15.6768 22.9718 13.2084 23.9966 10.6346 23.9966C8.06085 23.9966 5.59249 22.9718 3.77255 21.1476C1.95261 19.3234 0.930176 16.8492 0.930176 14.2694L0.930176 0.845967C0.945786 0.626796 1.03971 0.420614 1.19472 0.265242C1.34973 0.10987 1.55543 0.0157264 1.77409 7.91782e-05H5.57114C5.68239 -0.00145697 5.79282 0.0193733 5.8959 0.0613392C5.99898 0.103305 6.09262 0.165555 6.1713 0.244411C6.24997 0.323267 6.31207 0.417129 6.35394 0.520451C6.39581 0.623773 6.41659 0.734458 6.41506 0.845967V14.2694C6.43023 15.3875 6.88541 16.4542 7.6815 17.2375C8.07335 17.6163 8.53586 17.9141 9.04258 18.1137C9.54929 18.3133 10.0903 18.4108 10.6346 18.4007C11.179 18.4108 11.72 18.3133 12.2267 18.1137C12.7334 17.9141 13.1959 17.6163 13.5878 17.2375C14.3839 16.4542 14.839 15.3875 14.8542 14.2694V0.845967C14.8527 0.734556 14.8734 0.623965 14.9152 0.52072C14.957 0.417475 15.019 0.323665 15.0976 0.244825C15.1761 0.165984 15.2696 0.10371 15.3726 0.0616755C15.4755 0.0196407 15.5858 -0.00130318 15.697 7.91782e-05H19.494C19.6053 -0.00145697 19.7157 0.0193733 19.8188 0.0613392C19.9219 0.103305 20.0155 0.165555 20.0942 0.244411C20.1729 0.323267 20.235 0.417129 20.2768 0.520451C20.3187 0.623773 20.3395 0.734458 20.338 0.845967L20.3391 14.2694Z", fill: "#71E5BD" }),
    /* @__PURE__ */ jsx("path", { d: "M38.3759 17.9737C37.3749 18.5285 36.2499 18.8195 35.1061 18.8195C33.9624 18.8195 32.8373 18.5285 31.8364 17.9737C30.7783 17.392 29.8965 16.5349 29.2839 15.4928C28.6713 14.4506 28.3507 13.262 28.3559 12.0525C28.3559 10.2299 29.0783 8.48193 30.364 7.19315C31.6498 5.90438 33.3937 5.18035 35.212 5.18035C37.0304 5.18035 38.7743 5.90438 40.06 7.19315C41.3458 8.48193 42.0681 10.2299 42.0681 12.0525C42.0103 13.2738 41.6403 14.4598 40.9938 15.4966C40.3473 16.5334 39.4458 17.3864 38.3759 17.9737ZM35.1061 6.61337e-06C32.7487 6.61337e-06 30.4441 0.700712 28.484 2.01351C26.5238 3.32632 24.996 5.19225 24.0939 7.37536C23.1917 9.55847 22.9556 11.9607 23.4156 14.2783C23.8755 16.5959 25.0107 18.7247 26.6777 20.3956C28.3447 22.0665 30.4686 23.2043 32.7807 23.6653C35.0929 24.1263 37.4895 23.8897 39.6676 22.9855C41.8456 22.0812 43.7072 20.5498 45.0169 18.5851C46.3267 16.6204 47.0257 14.3104 47.0257 11.9475C47.0274 10.378 46.7202 8.82369 46.1218 7.37341C45.5233 5.92313 44.6454 4.6054 43.5382 3.49565C42.4311 2.3859 41.1164 1.50592 39.6695 0.906087C38.2226 0.306257 36.6719 -0.00164549 35.1061 6.61337e-06Z", fill: "#101010" })
  ] });
}
function MenuIcon(props) {
  return /* @__PURE__ */ jsx("svg", { viewBox: "0 0 24 24", fill: "none", "aria-hidden": "true", ...props, children: /* @__PURE__ */ jsx(
    "path",
    {
      d: "M5 6h14M5 18h14M5 12h14",
      stroke: "#000",
      strokeWidth: "2",
      strokeLinecap: "round",
      strokeLinejoin: "round"
    }
  ) });
}
function AppScreen({
  children,
  className,
  ...props
}) {
  return /* @__PURE__ */ jsxs("div", { className: clsx("flex flex-col bg-white", className), ...props, children: [
    /* @__PURE__ */ jsxs("div", { className: "flex justify-between px-4 pt-4", children: [
      /* @__PURE__ */ jsx(Logo, { className: "h-6 flex-none" }),
      /* @__PURE__ */ jsx(MenuIcon, { className: "h-6 w-6 flex-none" })
    ] }),
    children
  ] });
}
AppScreen.Header = forwardRef(function AppScreenHeader({ children }, ref) {
  return /* @__PURE__ */ jsx("div", { ref, className: "mt-6 px-4 text-white bg-white", children });
});
AppScreen.Title = forwardRef(function AppScreenTitle({ children }, ref) {
  return /* @__PURE__ */ jsx("div", { ref, className: "text-2xl text-black-50 font-bold", children });
});
AppScreen.Subtitle = forwardRef(function AppScreenSubtitle({ children }, ref) {
  return /* @__PURE__ */ jsx("div", { ref, className: "text-sm text-gray-500", children });
});
AppScreen.Body = forwardRef(function AppScreenBody({ children, className }, ref) {
  return /* @__PURE__ */ jsx(
    "div",
    {
      ref,
      className: clsx("mt-6 flex-auto rounded-t-2xl bg-white", className),
      children
    }
  );
});

function CircleBackground({
  color,
  ...props
}) {
  let id = useId();
  return /* @__PURE__ */ jsxs(
    "svg",
    {
      viewBox: "0 0 558 558",
      width: "558",
      height: "558",
      fill: "none",
      "aria-hidden": "true",
      ...props,
      children: [
        /* @__PURE__ */ jsx("defs", { children: /* @__PURE__ */ jsxs(
          "linearGradient",
          {
            id,
            x1: "79",
            y1: "16",
            x2: "105",
            y2: "237",
            gradientUnits: "userSpaceOnUse",
            children: [
              /* @__PURE__ */ jsx("stop", { stopColor: color }),
              /* @__PURE__ */ jsx("stop", { offset: "1", stopColor: color, stopOpacity: "0" })
            ]
          }
        ) }),
        /* @__PURE__ */ jsx(
          "path",
          {
            opacity: ".2",
            d: "M1 279C1 125.465 125.465 1 279 1s278 124.465 278 278-124.465 278-278 278S1 432.535 1 279Z",
            stroke: color
          }
        ),
        /* @__PURE__ */ jsx(
          "path",
          {
            d: "M1 279C1 125.465 125.465 1 279 1",
            stroke: `url(#${id})`,
            strokeLinecap: "round"
          }
        )
      ]
    }
  );
}

function LaravelLogo(props) {
  return /* @__PURE__ */ jsx("svg", { viewBox: "0 0 40 40", fill: "#fff", "aria-hidden": "true", ...props, children: /* @__PURE__ */ jsx(
    "path",
    {
      fillRule: "evenodd",
      clipRule: "evenodd",
      d: "M29.982 14.509c.**************.007.015a.316.316 0 0 1 .011.082v4.293a.304.304 0 0 1-.************* 0 0 1-.119.115l-3.709 2.075v4.112a.305.305 0 0 1-.************* 0 0 1-.119.114l-7.742 4.33a.286.286 0 0 1-.056.023l-.022.008a.33.33 0 0 1-.18-.005l-.01-.005c-.018-.006-.036-.011-.053-.021l-7.742-4.33a.32.32 0 0 1-.119-.115.304.304 0 0 1-.043-.156v-12.88a.33.33 0 0 1 .01-.08c.004-.01.01-.018.012-.027l.01-.027a.158.158 0 0 1 .011-.022c.006-.01.015-.018.023-.028.009-.012.017-.025.028-.036.01-.01.02-.016.031-.024.012-.009.023-.019.036-.026l3.871-2.165a.33.33 0 0 1 .322 0l3.872 2.165c.***************.036.026l.01.008c.**************.021.016a.175.175 0 0 1 .021.025l.008.011.022.028c.***************.02.049l.006.01.006.016a.307.307 0 0 1 .01.082v8.044l3.227-1.804v-4.112c0-.028.004-.055.011-.082.003-.01.008-.017.011-.026l.004-.01a.228.228 0 0 1 .017-.039.132.132 0 0 1 .013-.018.203.203 0 0 0 .01-.01c.009-.012.017-.025.028-.036l.015-.013.016-.01.019-.016a.126.126 0 0 1 .017-.011l3.871-2.165a.33.33 0 0 1 .322 0l3.871 2.165c.014.007.024.018.036.026l.012.008.02.016a.162.162 0 0 1 .02.026l.009.01.008.01c.005.006.01.012.013.018a.254.254 0 0 1 .018.04l.003.009.005.01Zm-15.138 8.717 3.22 1.77 7.094-3.933-3.223-1.803-7.091 3.966Zm10.64-2.704v-3.57l-3.226-1.804v3.57l3.225 1.804Zm3.547-5.916-3.225-1.803-3.224 1.803 3.224 1.803 3.225-1.803Zm-14.515.218v7.863l3.226-1.805V13.02l-3.226 1.804Zm2.902-2.346-3.225-1.803-3.224 1.803 3.224 1.803 3.225-1.803Zm-3.547 2.347-3.226-1.805v12.155l7.098 3.97V25.54l-3.708-2.038h-.001l-.002-.002c-.013-.008-.024-.018-.035-.027a.28.28 0 0 0-.011-.007.133.133 0 0 1-.02-.015v-.001l-.019-.022a.452.452 0 0 0-.008-.011l-.016-.02a.086.086 0 0 1-.008-.01v-.002a.123.123 0 0 1-.013-.027l-.005-.012-.008-.016a.115.115 0 0 1-.007-.02.18.18 0 0 1-.005-.033l-.002-.013a.293.293 0 0 0-.002-.013l-.002-.022v-8.405Zm4.516 10.715v3.605l7.096-3.969v-3.572l-7.096 3.935Zm7.742-5.019 3.226-1.804v-3.57l-3.226 1.805v3.57Z"
    }
  ) });
}
function TupleLogo(props) {
  return /* @__PURE__ */ jsx("svg", { viewBox: "0 0 40 40", fill: "#fff", "aria-hidden": "true", ...props, children: /* @__PURE__ */ jsx(
    "path",
    {
      fillRule: "evenodd",
      clipRule: "evenodd",
      d: "M22.5 8 12 11.692v12l3.5 1.231v3.385L26 32V12.615l-3.5 1.231V8Zm-5.833 17.334 5.833 2.05v-12.24l2.333-.82v15.968l-8.166-2.87v-2.088Z"
    }
  ) });
}
function TransistorLogo(props) {
  return /* @__PURE__ */ jsxs("svg", { viewBox: "0 0 40 40", fill: "#fff", "aria-hidden": "true", ...props, children: [
    /* @__PURE__ */ jsx("path", { d: "M20 32c-6.617 0-12-5.383-12-12S13.383 8 20 8s12 5.383 12 12-5.383 12-12 12Zm0-22.4C14.267 9.6 9.6 14.266 9.6 20S14.267 30.4 20 30.4c5.734 0 10.4-4.666 10.4-10.4S25.734 9.6 20 9.6Z" }),
    /* @__PURE__ */ jsx("path", { d: "M19.434 27.749c.15.15.354.234.566.235.433 0 .8-.368.8-.8V12.815a.8.8 0 0 0-1.6 0v14.368c0 .212.084.415.234.565ZM12.833 20.8h3.833a.802.802 0 0 0 .802-.8.8.8 0 0 0-.801-.8h-3.834c-.45 0-.8.35-.8.8a.8.8 0 0 0 .8.8ZM23.333 20.8h3.85c.433 0 .783-.35.783-.8a.799.799 0 0 0-.8-.8h-3.833c-.45 0-.8.35-.8.8a.8.8 0 0 0 .8.8Z" })
  ] });
}
function DiageoLogo(props) {
  return /* @__PURE__ */ jsxs(
    "svg",
    {
      viewBox: "0 0 40 40",
      fill: "#fff",
      stroke: "#fff",
      strokeWidth: "2",
      strokeLinecap: "round",
      strokeLinejoin: "round",
      "aria-hidden": "true",
      ...props,
      children: [
        /* @__PURE__ */ jsx("path", { d: "M22.16 19 26 13H14l3.84 6", fill: "none" }),
        /* @__PURE__ */ jsx("path", { d: "M25 24a5 5 0 1 1-10 0 5 5 0 0 1 10 0Z" })
      ]
    }
  );
}
function StaticKitLogo(props) {
  return /* @__PURE__ */ jsx("svg", { viewBox: "0 0 40 40", fill: "#fff", "aria-hidden": "true", ...props, children: /* @__PURE__ */ jsx("path", { d: "m26.068 10.555-11.49 13.089L12 21.089 23.489 8l2.58 2.555ZM28 18.91 16.512 32l-2.579-2.555 11.489-13.089L28 18.911Z" }) });
}
function StatamicLogo(props) {
  return /* @__PURE__ */ jsx("svg", { viewBox: "0 0 40 40", fill: "#fff", "aria-hidden": "true", ...props, children: /* @__PURE__ */ jsx(
    "path",
    {
      fillRule: "evenodd",
      clipRule: "evenodd",
      d: "M30.177 27.293c0 1.921-.644 2.707-2.398 2.707H12.22c-1.754 0-2.397-.786-2.397-2.707v-3.741c0-1.805-.837-2.824-1.642-3.291a.385.385 0 0 1-.133-.143.403.403 0 0 1 .133-.526c.837-.551 1.642-1.704 1.642-3.241v-3.677c0-2.072.547-2.674 2.3-2.674h15.754c1.754 0 2.3.602 2.3 2.674v3.675c0 1.537.805 2.69 1.641 ************.243.52 0 .67-.804.484-1.64 1.503-1.64 3.29v3.743h-.001Zm-14.739-2.455c1.271 1.152 2.64 1.737 4.522 1.737 2.96 0 4.891-1.537 4.891-4.026 0-2.637-2.3-3.31-4.17-3.856-1.282-.375-2.363-.691-2.363-1.54 0-.551.564-1.086 1.513-1.086.917 0 1.674.2 2.397.584.242.117.467.2.676.2.306 0 .547-.15.756-.45l.29-.451a.955.955 0 0 0 .161-.55c0-.336-.161-.67-.402-.837-.966-.635-2.27-1.17-4.039-1.17-2.51 0-4.44 1.37-4.44 3.826 0 2.746 2.349 3.443 4.23 4h.001c1.255.372 2.3.681 2.3 1.497 0 .785-.707 1.17-1.592 1.17a5.19 5.19 0 0 1-2.992-.92c-.274-.183-.532-.3-.805-.3-.242 0-.451.117-.644.368l-.387.517a.888.888 0 0 0-.192.585c0 .25.08.501.29.702Z"
    }
  ) });
}
function MirageLogo(props) {
  return /* @__PURE__ */ jsxs("svg", { viewBox: "0 0 40 40", fill: "#fff", "aria-hidden": "true", ...props, children: [
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M24.05 9c2.307 0 4.177 1.885 4.177 4.21a4.21 4.21 0 0 1-2.762 3.964l3.366 6.057h2.304c.355 0 .642.29.642.647a.645.645 0 0 1-.642.647H7.142a.645.645 0 0 1-.642-.647c0-.358.288-.647.643-.647h2.304l5.994-10.747a.641.641 0 0 1 1.097-.036l3.444 5.32 1.071-1.627a4.214 4.214 0 0 1-1.178-2.93c0-2.326 1.87-4.211 4.176-4.211Zm-3.304 9.948 2.772 4.283h3.84l-4.317-7.769-2.295 3.486Zm1.239 4.283-5.944-9.183-5.121 9.183h11.065Zm5.038-10.02a2.995 2.995 0 0 1-2.159 2.883l-1.216-2.19a.64.64 0 0 0-1.096-.04l-.811 1.232a3 3 0 0 1-.663-1.885c0-1.655 1.332-2.997 2.973-2.997 1.641 0 2.972 1.341 2.972 2.997Z"
      }
    ),
    /* @__PURE__ */ jsx("path", { d: "M12.069 26.469c-.354 0-.641.289-.641.646 0 .358.287.646.64.646h14.139c.354 0 .641-.29.641-.646a.644.644 0 0 0-.64-.646h-14.14Zm4.928 3.236a.645.645 0 0 0-.642.648c0 .357.288.647.642.647h4.282c.355 0 .643-.29.643-.647a.645.645 0 0 0-.643-.648h-4.282Z" })
  ] });
}
function ReversableLogo(props) {
  return /* @__PURE__ */ jsx(
    "svg",
    {
      viewBox: "0 0 40 40",
      fill: "none",
      stroke: "#fff",
      strokeWidth: "2",
      strokeLinecap: "square",
      strokeLinejoin: "round",
      "aria-hidden": "true",
      ...props,
      children: /* @__PURE__ */ jsx("path", { d: "M15 26v-5.25m0 0V16a2 2 0 0 1 2-2h4.21c.968 0 1.37 1.24.587 1.809L15 20.75Zm0 0L25 26" })
    }
  );
}

const MotionAppScreenHeader = motion.create(AppScreen.Header);
const MotionAppScreenBody = motion.create(AppScreen.Body);
const features$1 = [
  {
    name: "Invite friends for better returns",
    description: "For every friend you invite to Pocket, you get insider notifications 5 seconds sooner. And it’s 10 seconds if you invite an insider.",
    icon: DeviceUserIcon,
    screen: InviteScreen
  },
  {
    name: "Notifications on stock dips",
    description: "Get a push notification every time we find out something that’s going to lower the share price on your holdings so you can sell before the information hits the public markets.",
    icon: DeviceNotificationIcon,
    screen: StocksScreen
  },
  {
    name: "Invest what you want",
    description: "We hide your stock purchases behind thousands of anonymous trading accounts, so suspicious activity can never be traced back to you.",
    icon: DeviceTouchIcon,
    screen: InvestScreen
  }
];
function DeviceUserIcon(props) {
  return /* @__PURE__ */ jsxs("svg", { viewBox: "0 0 32 32", "aria-hidden": "true", ...props, children: [
    /* @__PURE__ */ jsx("circle", { cx: 16, cy: 16, r: 16, fill: "#A3A3A3", fillOpacity: 0.2 }),
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M16 23a3 3 0 100-6 3 3 0 000 6zm-1 2a4 4 0 00-4 4v1a2 2 0 002 2h6a2 2 0 002-2v-1a4 4 0 00-4-4h-2z",
        fill: "#737373"
      }
    ),
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M5 4a4 4 0 014-4h14a4 4 0 014 4v24a4.002 4.002 0 01-3.01 3.877c-.535.136-.99-.325-.99-.877s.474-.98.959-1.244A2 2 0 0025 28V4a2 2 0 00-2-2h-1.382a1 1 0 00-.894.553l-.448.894a1 1 0 01-.894.553h-6.764a1 1 0 01-.894-.553l-.448-.894A1 1 0 0010.382 2H9a2 2 0 00-2 2v24a2 2 0 001.041 1.756C8.525 30.02 9 30.448 9 31s-.455 1.013-.99.877A4.002 4.002 0 015 28V4z",
        fill: "#A3A3A3"
      }
    )
  ] });
}
function DeviceNotificationIcon(props) {
  return /* @__PURE__ */ jsxs("svg", { viewBox: "0 0 32 32", "aria-hidden": "true", ...props, children: [
    /* @__PURE__ */ jsx("circle", { cx: 16, cy: 16, r: 16, fill: "#A3A3A3", fillOpacity: 0.2 }),
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M9 0a4 4 0 00-4 4v24a4 4 0 004 4h14a4 4 0 004-4V4a4 4 0 00-4-4H9zm0 2a2 2 0 00-2 2v24a2 2 0 002 2h14a2 2 0 002-2V4a2 2 0 00-2-2h-1.382a1 1 0 00-.894.553l-.448.894a1 1 0 01-.894.553h-6.764a1 1 0 01-.894-.553l-.448-.894A1 1 0 0010.382 2H9z",
        fill: "#A3A3A3"
      }
    ),
    /* @__PURE__ */ jsx(
      "path",
      {
        d: "M9 8a2 2 0 012-2h10a2 2 0 012 2v2a2 2 0 01-2 2H11a2 2 0 01-2-2V8z",
        fill: "#737373"
      }
    )
  ] });
}
function DeviceTouchIcon(props) {
  let id = useId();
  return /* @__PURE__ */ jsxs("svg", { viewBox: "0 0 32 32", fill: "none", "aria-hidden": "true", ...props, children: [
    /* @__PURE__ */ jsx("defs", { children: /* @__PURE__ */ jsxs(
      "linearGradient",
      {
        id: `${id}-gradient`,
        x1: 14,
        y1: 14.5,
        x2: 7,
        y2: 17,
        gradientUnits: "userSpaceOnUse",
        children: [
          /* @__PURE__ */ jsx("stop", { stopColor: "#737373" }),
          /* @__PURE__ */ jsx("stop", { offset: 1, stopColor: "#D4D4D4", stopOpacity: 0 })
        ]
      }
    ) }),
    /* @__PURE__ */ jsx("circle", { cx: 16, cy: 16, r: 16, fill: "#A3A3A3", fillOpacity: 0.2 }),
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M5 4a4 4 0 014-4h14a4 4 0 014 4v13h-2V4a2 2 0 00-2-2h-1.382a1 1 0 00-.894.553l-.448.894a1 1 0 01-.894.553h-6.764a1 1 0 01-.894-.553l-.448-.894A1 1 0 0010.382 2H9a2 2 0 00-2 2v24a2 2 0 002 2h4v2H9a4 4 0 01-4-4V4z",
        fill: "#A3A3A3"
      }
    ),
    /* @__PURE__ */ jsx(
      "path",
      {
        d: "M7 22c0-4.694 3.5-8 8-8",
        stroke: `url(#${id}-gradient)`,
        strokeWidth: 2,
        strokeLinecap: "round",
        strokeLinejoin: "round"
      }
    ),
    /* @__PURE__ */ jsx(
      "path",
      {
        d: "M21 20l.217-5.513a1.431 1.431 0 00-2.85-.226L17.5 21.5l-1.51-1.51a2.107 2.107 0 00-2.98 0 .024.024 0 00-.005.024l3.083 9.25A4 4 0 0019.883 32H25a4 4 0 004-4v-5a3 3 0 00-3-3h-5z",
        fill: "#A3A3A3"
      }
    )
  ] });
}
const headerAnimation = {
  initial: { opacity: 0, transition: { duration: 0.3 } },
  animate: { opacity: 1, transition: { duration: 0.3, delay: 0.3 } },
  exit: { opacity: 0, transition: { duration: 0.3 } }
};
const maxZIndex = 2147483647;
const bodyVariantBackwards = {
  opacity: 0.4,
  scale: 0.8,
  zIndex: 0,
  filter: "blur(4px)",
  transition: { duration: 0.4 }
};
const bodyVariantForwards = (custom) => ({
  y: "100%",
  zIndex: maxZIndex - custom.changeCount,
  transition: { duration: 0.4 }
});
const bodyAnimation = {
  initial: "initial",
  animate: "animate",
  exit: "exit",
  variants: {
    initial: (custom, ...props) => custom.isForwards ? bodyVariantForwards(custom, ...props) : bodyVariantBackwards,
    animate: (custom) => ({
      y: "0%",
      opacity: 1,
      scale: 1,
      zIndex: maxZIndex / 2 - custom.changeCount,
      filter: "blur(0px)",
      transition: { duration: 0.4 }
    }),
    exit: (custom, ...props) => custom.isForwards ? bodyVariantBackwards : bodyVariantForwards(custom, ...props)
  }
};
function InviteScreen(props) {
  return /* @__PURE__ */ jsxs(AppScreen, { className: "w-full", children: [
    /* @__PURE__ */ jsxs(MotionAppScreenHeader, { ...props.animated ? headerAnimation : {}, children: [
      /* @__PURE__ */ jsx(AppScreen.Title, { children: "Sign Up" }),
      /* @__PURE__ */ jsx(AppScreen.Subtitle, { children: "Sign up to join the UOWN team." })
    ] }),
    /* @__PURE__ */ jsx(
      MotionAppScreenBody,
      {
        ...props.animated ? { ...bodyAnimation, custom: props.custom } : {},
        children: /* @__PURE__ */ jsxs("div", { className: "px-4 py-6", children: [
          /* @__PURE__ */ jsx("div", { className: "space-y-6", children: [
            { label: "Full name", value: "Albert H. Wiggin" },
            { label: "Email address", value: "<EMAIL>" }
          ].map((field) => /* @__PURE__ */ jsxs("div", { children: [
            /* @__PURE__ */ jsx("div", { className: "text-sm text-gray-500", children: field.label }),
            /* @__PURE__ */ jsx("div", { className: "mt-2 border-b border-gray-200 pb-2 text-sm text-gray-900", children: field.value })
          ] }, field.label)) }),
          /* @__PURE__ */ jsx("div", { className: "mt-6 rounded-full bg-mint-300 px-3 py-2 text-center text-sm font-semibold text-black-50", children: "Sign Up" })
        ] })
      }
    )
  ] });
}
function StocksScreen(props) {
  return /* @__PURE__ */ jsxs(AppScreen, { className: "w-full", children: [
    /* @__PURE__ */ jsxs(MotionAppScreenHeader, { ...props.animated ? headerAnimation : {}, children: [
      /* @__PURE__ */ jsx(AppScreen.Title, { children: "Stocks" }),
      /* @__PURE__ */ jsx(AppScreen.Subtitle, { children: "March 9, 2022" })
    ] }),
    /* @__PURE__ */ jsx(
      MotionAppScreenBody,
      {
        ...props.animated ? { ...bodyAnimation, custom: props.custom } : {},
        children: /* @__PURE__ */ jsx("div", { className: "divide-y divide-gray-100", children: [
          {
            name: "Laravel",
            price: "4,098.01",
            change: "+4.98%",
            color: "#F9322C",
            logo: LaravelLogo
          },
          {
            name: "Tuple",
            price: "5,451.10",
            change: "-3.38%",
            color: "#5A67D8",
            logo: TupleLogo
          },
          {
            name: "Transistor",
            price: "4,098.41",
            change: "+6.25%",
            color: "#2A5B94",
            logo: TransistorLogo
          },
          {
            name: "Diageo",
            price: "250.65",
            change: "+1.25%",
            color: "#3320A7",
            logo: DiageoLogo
          },
          {
            name: "StaticKit",
            price: "250.65",
            change: "-3.38%",
            color: "#2A3034",
            logo: StaticKitLogo
          },
          {
            name: "Statamic",
            price: "5,040.85",
            change: "-3.11%",
            color: "#0EA5E9",
            logo: StatamicLogo
          },
          {
            name: "Mirage",
            price: "140.44",
            change: "+9.09%",
            color: "#16A34A",
            logo: MirageLogo
          },
          {
            name: "Reversable",
            price: "550.60",
            change: "-1.25%",
            color: "#8D8D8D",
            logo: ReversableLogo
          }
        ].map((stock) => /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-4 px-4 py-3", children: [
          /* @__PURE__ */ jsx(
            "div",
            {
              className: "flex-none rounded-full",
              style: { backgroundColor: stock.color },
              children: /* @__PURE__ */ jsx(stock.logo, { className: "h-10 w-10" })
            }
          ),
          /* @__PURE__ */ jsx("div", { className: "flex-auto text-sm text-gray-900", children: stock.name }),
          /* @__PURE__ */ jsxs("div", { className: "flex-none text-right", children: [
            /* @__PURE__ */ jsx("div", { className: "text-sm font-medium text-gray-900", children: stock.price }),
            /* @__PURE__ */ jsx(
              "div",
              {
                className: clsx(
                  "text-xs leading-5",
                  stock.change.startsWith("+") ? "text-cyan-500" : "text-gray-500"
                ),
                children: stock.change
              }
            )
          ] })
        ] }, stock.name)) })
      }
    )
  ] });
}
function InvestScreen(props) {
  return /* @__PURE__ */ jsxs(AppScreen, { className: "w-full", children: [
    /* @__PURE__ */ jsxs(MotionAppScreenHeader, { ...props.animated ? headerAnimation : {}, children: [
      /* @__PURE__ */ jsx(AppScreen.Title, { children: "Buy $LA" }),
      /* @__PURE__ */ jsxs(AppScreen.Subtitle, { children: [
        /* @__PURE__ */ jsx("span", { className: "text-white", children: "$34.28" }),
        " per share"
      ] })
    ] }),
    /* @__PURE__ */ jsx(
      MotionAppScreenBody,
      {
        ...props.animated ? { ...bodyAnimation, custom: props.custom } : {},
        children: /* @__PURE__ */ jsx("div", { className: "px-4 py-6", children: /* @__PURE__ */ jsxs("div", { className: "space-y-4", children: [
          [
            { label: "Number of shares", value: "100" },
            {
              label: "Current market price",
              value: /* @__PURE__ */ jsxs("div", { className: "flex", children: [
                "$34.28",
                /* @__PURE__ */ jsx("svg", { viewBox: "0 0 24 24", fill: "none", className: "h-6 w-6", children: /* @__PURE__ */ jsx(
                  "path",
                  {
                    d: "M17 15V7H9M17 7 7 17",
                    stroke: "#06B6D4",
                    strokeWidth: "2",
                    strokeLinecap: "round",
                    strokeLinejoin: "round"
                  }
                ) })
              ] })
            },
            { label: "Estimated cost", value: "$3,428.00" }
          ].map((item) => /* @__PURE__ */ jsxs(
            "div",
            {
              className: "flex justify-between border-b border-gray-100 pb-4",
              children: [
                /* @__PURE__ */ jsx("div", { className: "text-sm text-gray-500", children: item.label }),
                /* @__PURE__ */ jsx("div", { className: "text-sm font-semibold text-gray-900", children: item.value })
              ]
            },
            item.label
          )),
          /* @__PURE__ */ jsx("div", { className: "rounded-lg bg-cyan-500 px-3 py-2 text-center text-sm font-semibold text-white", children: "Buy shares" })
        ] }) })
      }
    )
  ] });
}
function usePrevious(value) {
  let ref = useRef();
  useEffect(() => {
    ref.current = value;
  }, [value]);
  return ref.current;
}
function FeaturesDesktop() {
  let [changeCount, setChangeCount] = useState(0);
  let [selectedIndex, setSelectedIndex] = useState(0);
  let prevIndex = usePrevious(selectedIndex);
  let isForwards = prevIndex === void 0 ? true : selectedIndex > prevIndex;
  let onChange = useDebouncedCallback(
    (selectedIndex2) => {
      setSelectedIndex(selectedIndex2);
      setChangeCount((changeCount2) => changeCount2 + 1);
    },
    100,
    { leading: true }
  );
  return /* @__PURE__ */ jsxs(
    TabGroup,
    {
      className: "grid grid-cols-12 items-center gap-8 lg:gap-16 xl:gap-24",
      selectedIndex,
      onChange,
      vertical: true,
      children: [
        /* @__PURE__ */ jsx(TabList, { className: "relative z-10 order-last col-span-6 space-y-6", children: features$1.map((feature, featureIndex) => /* @__PURE__ */ jsxs(
          "div",
          {
            className: "relative rounded-clg transition-colors hover:bg-gray-700/30",
            children: [
              featureIndex === selectedIndex && /* @__PURE__ */ jsx(
                motion.div,
                {
                  layoutId: "activeBackground",
                  className: "absolute inset-0 bg-gray-900",
                  initial: { borderRadius: 16 }
                }
              ),
              /* @__PURE__ */ jsxs("div", { className: "relative z-10 p-8", children: [
                /* @__PURE__ */ jsx(feature.icon, { className: "h-8 w-8" }),
                /* @__PURE__ */ jsx("h3", { className: "mt-6 text-lg font-semibold text-white", children: /* @__PURE__ */ jsxs(Tab, { className: "text-left ui-not-focus-visible:outline-none", children: [
                  /* @__PURE__ */ jsx("span", { className: "absolute inset-0 rounded-clg" }),
                  feature.name
                ] }) }),
                /* @__PURE__ */ jsx("p", { className: "mt-2 text-sm text-gray-400", children: feature.description })
              ] })
            ]
          },
          feature.name
        )) }),
        /* @__PURE__ */ jsxs("div", { className: "relative col-span-6", children: [
          /* @__PURE__ */ jsx("div", { className: "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2", children: /* @__PURE__ */ jsx(CircleBackground, { color: "#13B5C8", className: "animate-spin-slower" }) }),
          /* @__PURE__ */ jsx(PhoneFrame, { className: "z-10 mx-auto w-full max-w-[366px]", children: /* @__PURE__ */ jsx(TabPanels, { as: Fragment$1, children: /* @__PURE__ */ jsx(
            AnimatePresence,
            {
              initial: false,
              custom: { isForwards, changeCount },
              children: features$1.map(
                (feature, featureIndex) => selectedIndex === featureIndex ? /* @__PURE__ */ jsx(
                  TabPanel,
                  {
                    static: true,
                    className: "col-start-1 row-start-1 flex focus:outline-offset-[32px] ui-not-focus-visible:outline-none",
                    children: /* @__PURE__ */ jsx(
                      feature.screen,
                      {
                        animated: true,
                        custom: { isForwards, changeCount }
                      }
                    )
                  },
                  feature.name + changeCount
                ) : null
              )
            }
          ) }) })
        ] })
      ]
    }
  );
}
function FeaturesMobile() {
  let [activeIndex, setActiveIndex] = useState(0);
  let slideContainerRef = useRef(null);
  let slideRefs = useRef([]);
  useEffect(() => {
    let observer = new window.IntersectionObserver(
      (entries) => {
        for (let entry of entries) {
          if (entry.isIntersecting && entry.target instanceof HTMLDivElement) {
            setActiveIndex(slideRefs.current.indexOf(entry.target));
            break;
          }
        }
      },
      {
        root: slideContainerRef.current,
        threshold: 0.6
      }
    );
    for (let slide of slideRefs.current) {
      if (slide) {
        observer.observe(slide);
      }
    }
    return () => {
      observer.disconnect();
    };
  }, [slideContainerRef, slideRefs]);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(
      "div",
      {
        ref: slideContainerRef,
        className: "-mb-4 flex snap-x snap-mandatory -space-x-4 overflow-x-auto overscroll-x-contain scroll-smooth pb-4 [scrollbar-width:none] sm:-space-x-6 [&::-webkit-scrollbar]:hidden",
        children: features$1.map((feature, featureIndex) => /* @__PURE__ */ jsx(
          "div",
          {
            ref: (ref) => ref && (slideRefs.current[featureIndex] = ref),
            className: "w-full flex-none snap-center px-4 sm:px-6",
            children: /* @__PURE__ */ jsxs("div", { className: "relative transform overflow-hidden rounded-clg bg-gray-800 px-5 py-6", children: [
              /* @__PURE__ */ jsx("div", { className: "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2", children: /* @__PURE__ */ jsx(
                CircleBackground,
                {
                  color: "#13B5C8",
                  className: featureIndex % 2 === 1 ? "rotate-180" : void 0
                }
              ) }),
              /* @__PURE__ */ jsx(PhoneFrame, { className: "relative mx-auto w-full max-w-[366px]", children: /* @__PURE__ */ jsx(feature.screen, {}) }),
              /* @__PURE__ */ jsxs("div", { className: "absolute inset-x-0 bottom-0 bg-gray-800/95 p-6 backdrop-blur sm:p-10", children: [
                /* @__PURE__ */ jsx(feature.icon, { className: "h-8 w-8" }),
                /* @__PURE__ */ jsx("h3", { className: "mt-6 text-sm font-semibold text-white sm:text-lg", children: feature.name }),
                /* @__PURE__ */ jsx("p", { className: "mt-2 text-sm text-gray-400", children: feature.description })
              ] })
            ] })
          },
          featureIndex
        ))
      }
    ),
    /* @__PURE__ */ jsx("div", { className: "mt-6 flex justify-center gap-3", children: features$1.map((_, featureIndex) => /* @__PURE__ */ jsx(
      "button",
      {
        type: "button",
        className: clsx(
          "relative h-0.5 w-4 rounded-full",
          featureIndex === activeIndex ? "bg-gray-300" : "bg-gray-500"
        ),
        "aria-label": `Go to slide ${featureIndex + 1}`,
        onClick: () => {
          slideRefs.current[featureIndex].scrollIntoView({
            block: "nearest",
            inline: "nearest"
          });
        },
        children: /* @__PURE__ */ jsx("span", { className: "absolute -inset-x-1.5 -inset-y-3" })
      },
      featureIndex
    )) })
  ] });
}
function PrimaryFeatures() {
  return /* @__PURE__ */ jsxs(
    "section",
    {
      id: "features",
      "aria-label": "Features for investing all your money",
      className: "bg-black-200 py-20 sm:py-32",
      children: [
        /* @__PURE__ */ jsx(Container, { children: /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-2xl lg:mx-0 lg:max-w-3xl", children: [
          /* @__PURE__ */ jsx("h2", { className: "text-3xl font-medium tracking-tight text-white", children: "Every feature you need to win. Try it for yourself." }),
          /* @__PURE__ */ jsx("p", { className: "mt-2 text-lg text-gray-400", children: "Pocket was built for investors like you who play by their own rules and aren’t going to let SEC regulations get in the way of their dreams. If other investing tools are afraid to build it, Pocket has it." })
        ] }) }),
        /* @__PURE__ */ jsx("div", { className: "mt-16 md:hidden", children: /* @__PURE__ */ jsx(FeaturesMobile, {}) }),
        /* @__PURE__ */ jsx(Container, { className: "hidden md:mt-20 md:block", children: /* @__PURE__ */ jsx(FeaturesDesktop, {}) })
      ]
    }
  );
}

const features = [
  {
    name: "Invest any amount",
    description: "Whether it’s $1 or $1,000,000, we can put your money to work for you.",
    icon: DeviceArrowIcon
  },
  {
    name: "Build a balanced portfolio",
    description: "Invest in different industries to find the most opportunities to win huge.",
    icon: DeviceCardsIcon
  },
  {
    name: "Trade in real-time",
    description: "Get insider tips on big stock moves and act on them within seconds.",
    icon: DeviceClockIcon
  },
  {
    name: "Profit from your network",
    description: "Invite new insiders to get tips faster and beat even other Pocket users.",
    icon: DeviceListIcon
  },
  {
    name: "Encrypted and anonymized",
    description: "Cutting-edge security technology that even the NSA doesn’t know about keeps you hidden.",
    icon: DeviceLockIcon
  },
  {
    name: "Portfolio tracking",
    description: "Watch your investments grow exponentially, leaving other investors in the dust.",
    icon: DeviceChartIcon
  }
];
function DeviceArrowIcon(props) {
  return /* @__PURE__ */ jsxs("svg", { viewBox: "0 0 32 32", "aria-hidden": "true", ...props, children: [
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M9 0a4 4 0 00-4 4v24a4 4 0 004 4h14a4 4 0 004-4V4a4 4 0 00-4-4H9zm0 2a2 2 0 00-2 2v24a2 2 0 002 2h14a2 2 0 002-2V4a2 2 0 00-2-2h-1.382a1 1 0 00-.894.553l-.448.894a1 1 0 01-.894.553h-6.764a1 1 0 01-.894-.553l-.448-.894A1 1 0 0010.382 2H9z",
        fill: "#737373"
      }
    ),
    /* @__PURE__ */ jsx(
      "path",
      {
        d: "M12 25l8-8m0 0h-6m6 0v6",
        stroke: "#171717",
        strokeWidth: 2,
        strokeLinecap: "round"
      }
    ),
    /* @__PURE__ */ jsx("circle", { cx: 16, cy: 16, r: 16, fill: "#A3A3A3", fillOpacity: 0.2 })
  ] });
}
function DeviceCardsIcon(props) {
  let id = useId();
  return /* @__PURE__ */ jsxs("svg", { viewBox: "0 0 32 32", "aria-hidden": "true", ...props, children: [
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M9 0a4 4 0 00-4 4v24a4 4 0 004 4h14a4 4 0 004-4V4a4 4 0 00-4-4H9zm0 2a2 2 0 00-2 2v24a2 2 0 002 2h14a2 2 0 002-2V4a2 2 0 00-2-2h-1.382a1 1 0 00-.894.553l-.448.894a1 1 0 01-.894.553h-6.764a1 1 0 01-.894-.553l-.448-.894A1 1 0 0010.382 2H9z",
        fill: "#737373"
      }
    ),
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M9 13a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H10a1 1 0 01-1-1v-2zm0 6a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H10a1 1 0 01-1-1v-2zm1 5a1 1 0 00-1 1v2a1 1 0 001 1h12a1 1 0 001-1v-2a1 1 0 00-1-1H10z",
        fill: `url(#${id}-gradient)`
      }
    ),
    /* @__PURE__ */ jsx("rect", { x: 9, y: 6, width: 14, height: 4, rx: 1, fill: "#171717" }),
    /* @__PURE__ */ jsx("circle", { cx: 16, cy: 16, r: 16, fill: "#A3A3A3", fillOpacity: 0.2 }),
    /* @__PURE__ */ jsx("defs", { children: /* @__PURE__ */ jsxs(
      "linearGradient",
      {
        id: `${id}-gradient`,
        x1: 16,
        y1: 12,
        x2: 16,
        y2: 28,
        gradientUnits: "userSpaceOnUse",
        children: [
          /* @__PURE__ */ jsx("stop", { stopColor: "#737373" }),
          /* @__PURE__ */ jsx("stop", { offset: 1, stopColor: "#737373", stopOpacity: 0 })
        ]
      }
    ) })
  ] });
}
function DeviceClockIcon(props) {
  return /* @__PURE__ */ jsxs("svg", { viewBox: "0 0 32 32", "aria-hidden": "true", ...props, children: [
    /* @__PURE__ */ jsx("circle", { cx: 16, cy: 16, r: 16, fill: "#A3A3A3", fillOpacity: 0.2 }),
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M5 4a4 4 0 014-4h14a4 4 0 014 4v10h-2V4a2 2 0 00-2-2h-1.382a1 1 0 00-.894.553l-.448.894a1 1 0 01-.894.553h-6.764a1 1 0 01-.894-.553l-.448-.894A1 1 0 0010.382 2H9a2 2 0 00-2 2v24a2 2 0 002 2h5v2H9a4 4 0 01-4-4V4z",
        fill: "#737373"
      }
    ),
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M24 32a8 8 0 100-16 8 8 0 000 16zm1-8.414V19h-2v5.414l4 4L28.414 27 25 23.586z",
        fill: "#171717"
      }
    )
  ] });
}
function DeviceListIcon(props) {
  return /* @__PURE__ */ jsxs("svg", { viewBox: "0 0 32 32", fill: "none", "aria-hidden": "true", ...props, children: [
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M9 0a4 4 0 00-4 4v24a4 4 0 004 4h14a4 4 0 004-4V4a4 4 0 00-4-4H9zm0 2a2 2 0 00-2 2v24a2 2 0 002 2h14a2 2 0 002-2V4a2 2 0 00-2-2h-1.382a1 1 0 00-.894.553l-.448.894a1 1 0 01-.894.553h-6.764a1 1 0 01-.894-.553l-.448-.894A1 1 0 0010.382 2H9z",
        fill: "#737373"
      }
    ),
    /* @__PURE__ */ jsx("circle", { cx: 11, cy: 14, r: 2, fill: "#171717" }),
    /* @__PURE__ */ jsx("circle", { cx: 11, cy: 20, r: 2, fill: "#171717" }),
    /* @__PURE__ */ jsx("circle", { cx: 11, cy: 26, r: 2, fill: "#171717" }),
    /* @__PURE__ */ jsx(
      "path",
      {
        d: "M16 14h6M16 20h6M16 26h6",
        stroke: "#737373",
        strokeWidth: 2,
        strokeLinecap: "square"
      }
    ),
    /* @__PURE__ */ jsx("circle", { cx: 16, cy: 16, r: 16, fill: "#A3A3A3", fillOpacity: 0.2 })
  ] });
}
function DeviceLockIcon(props) {
  return /* @__PURE__ */ jsxs("svg", { viewBox: "0 0 32 32", "aria-hidden": "true", ...props, children: [
    /* @__PURE__ */ jsx("circle", { cx: 16, cy: 16, r: 16, fill: "#A3A3A3", fillOpacity: 0.2 }),
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M5 4a4 4 0 014-4h14a4 4 0 014 4v10h-2V4a2 2 0 00-2-2h-1.382a1 1 0 00-.894.553l-.448.894a1 1 0 01-.894.553h-6.764a1 1 0 01-.894-.553l-.448-.894A1 1 0 0010.382 2H9a2 2 0 00-2 2v24a2 2 0 002 2h5v2H9a4 4 0 01-4-4V4z",
        fill: "#737373"
      }
    ),
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M18 19.5a3.5 3.5 0 117 0V22a2 2 0 012 2v6a2 2 0 01-2 2h-7a2 2 0 01-2-2v-6a2 2 0 012-2v-2.5zm2 2.5h3v-2.5a1.5 1.5 0 00-3 0V22z",
        fill: "#171717"
      }
    )
  ] });
}
function DeviceChartIcon(props) {
  return /* @__PURE__ */ jsxs("svg", { viewBox: "0 0 32 32", fill: "none", "aria-hidden": "true", ...props, children: [
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M9 0a4 4 0 00-4 4v24a4 4 0 004 4h14a4 4 0 004-4V4a4 4 0 00-4-4H9zm0 2a2 2 0 00-2 2v24a2 2 0 002 2h14a2 2 0 002-2V4a2 2 0 00-2-2h-1.382a1 1 0 00-.894.553l-.448.894a1 1 0 01-.894.553h-6.764a1 1 0 01-.894-.553l-.448-.894A1 1 0 0010.382 2H9z",
        fill: "#737373"
      }
    ),
    /* @__PURE__ */ jsx(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M23 13.838V26a2 2 0 01-2 2H11a2 2 0 01-2-2V15.65l2.57 3.212a1 1 0 001.38.175L15.4 17.2a1 1 0 011.494.353l1.841 3.681c.399.797 1.562.714 1.843-.13L23 13.837z",
        fill: "#171717"
      }
    ),
    /* @__PURE__ */ jsx(
      "path",
      {
        d: "M10 12h12",
        stroke: "#737373",
        strokeWidth: 2,
        strokeLinecap: "square"
      }
    ),
    /* @__PURE__ */ jsx("circle", { cx: 16, cy: 16, r: 16, fill: "#A3A3A3", fillOpacity: 0.2 })
  ] });
}
function SecondaryFeatures() {
  return /* @__PURE__ */ jsx(
    "section",
    {
      id: "secondary-features",
      "aria-label": "Features for building a portfolio",
      className: "py-20 sm:py-32",
      children: /* @__PURE__ */ jsxs(Container, { children: [
        /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-2xl sm:text-center", children: [
          /* @__PURE__ */ jsx("h2", { className: "text-3xl font-medium tracking-tight text-gray-900", children: "Now is the time to build your portfolio." }),
          /* @__PURE__ */ jsx("p", { className: "mt-2 text-lg text-gray-600", children: "With typical market returns, you have to start young to secure your future. With Pocket, it’s never too late to build your nest egg." })
        ] }),
        /* @__PURE__ */ jsx(
          "ul",
          {
            role: "list",
            className: "mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 text-sm sm:mt-20 sm:grid-cols-2 md:gap-y-10 lg:max-w-none lg:grid-cols-3",
            children: features.map((feature) => /* @__PURE__ */ jsxs(
              "li",
              {
                className: "rounded-clg border border-gray-200 p-8",
                children: [
                  /* @__PURE__ */ jsx(feature.icon, { className: "h-8 w-8" }),
                  /* @__PURE__ */ jsx("h3", { className: "mt-6 font-semibold text-gray-900", children: feature.name }),
                  /* @__PURE__ */ jsx("p", { className: "mt-2 text-gray-700", children: feature.description })
                ]
              },
              feature.name
            ))
          }
        )
      ] })
    }
  );
}

function AppStoreLink({
  color = "black"
}) {
  return /* @__PURE__ */ jsx(
    Link,
    {
      href: "#",
      "aria-label": "Download on the App Store",
      className: clsx(
        "rounded-lg transition-colors",
        color === "black" ? "bg-gray-800 text-white hover:bg-gray-900" : "bg-white text-gray-900 hover:bg-gray-50"
      ),
      children: /* @__PURE__ */ jsx("svg", { viewBox: "0 0 120 40", "aria-hidden": "true", className: "h-10", children: /* @__PURE__ */ jsx(
        "path",
        {
          fill: "currentColor",
          d: "M24.769 20.301a4.947 4.947 0 0 1 2.357-4.152 5.066 5.066 0 0 0-3.992-2.157c-1.679-.177-3.307 1.004-4.163 1.004-.872 0-2.19-.987-3.608-.958a5.315 5.315 0 0 0-4.473 2.728c-1.934 3.349-.491 8.27 1.361 10.976.927 1.326 2.01 2.806 3.428 2.753 1.387-.057 1.905-.884 3.58-.884 1.658 0 2.144.884 3.59.851 1.489-.024 2.426-1.331 3.32-2.669a10.96 10.96 0 0 0 1.52-3.092 4.782 4.782 0 0 1-2.92-4.4ZM22.037 12.211a4.872 4.872 0 0 0 1.115-3.49 4.957 4.957 0 0 0-3.208 1.66 4.635 4.635 0 0 0-1.143 3.36 4.099 4.099 0 0 0 3.236-1.53ZM42.302 27.14H37.57l-1.137 3.356h-2.005l4.484-12.418h2.083l4.483 12.418h-2.039l-1.136-3.356Zm-4.243-1.55h3.752l-1.85-5.446h-.051l-1.85 5.447ZM55.16 25.97c0 2.813-1.506 4.62-3.779 4.62a3.068 3.068 0 0 1-2.848-1.584h-.043v4.485H46.63V21.442h1.8v1.506h.033a3.21 3.21 0 0 1 2.883-1.6c2.298 0 3.813 1.816 3.813 4.622Zm-1.91 0c0-1.833-.948-3.039-2.393-3.039-1.42 0-2.375 1.23-2.375 3.038 0 1.825.955 3.046 2.375 3.046 1.445 0 2.392-1.196 2.392-3.046ZM65.124 25.97c0 2.813-1.505 4.62-3.778 4.62a3.07 3.07 0 0 1-2.848-1.584h-.043v4.485h-1.859V21.442h1.799v1.506h.034a3.21 3.21 0 0 1 2.883-1.6c2.298 0 3.813 1.816 3.813 4.621Zm-1.91 0c0-1.834-.947-3.039-2.392-3.039-1.42 0-2.375 1.23-2.375 3.038 0 1.825.955 3.046 2.375 3.046 1.445 0 2.392-1.196 2.392-3.046ZM71.71 27.036c.138 1.232 1.335 2.04 2.97 2.04 1.566 0 2.693-.808 2.693-1.919 0-.964-.68-1.54-2.29-1.936l-1.609-.388c-2.28-.55-3.339-1.617-3.339-3.348 0-2.142 1.867-3.614 4.519-3.614 2.624 0 4.423 1.472 4.483 3.614h-1.876c-.112-1.239-1.136-1.987-2.634-1.987-1.497 0-2.521.757-2.521 1.858 0 .878.654 1.395 2.255 1.79l1.368.336c2.548.603 3.606 1.626 3.606 3.443 0 2.323-1.85 3.778-4.793 3.778-2.754 0-4.614-1.42-4.734-3.667h1.902ZM83.346 19.3v2.142h1.722v1.472h-1.722v4.991c0 .776.345 1.137 1.102 1.137.204-.004.408-.018.611-.043v1.463c-.34.064-.686.092-1.032.086-1.833 0-2.548-.689-2.548-2.444v-5.19h-1.316v-1.472h1.316V19.3h1.867ZM86.065 25.97c0-2.849 1.678-4.639 4.294-4.639 2.625 0 4.295 1.79 4.295 4.639 0 2.856-1.661 4.638-4.295 4.638-2.633 0-4.294-1.782-4.294-4.638Zm6.695 0c0-1.954-.895-3.108-2.401-3.108-1.506 0-2.4 1.162-2.4 3.108 0 1.962.894 3.106 2.4 3.106 1.506 0 2.401-1.144 2.401-3.106ZM96.186 21.442h1.772v1.541h.043a2.16 2.16 0 0 1 2.178-1.636c.214 0 .428.023.637.07v1.738a2.594 2.594 0 0 0-.835-.112 1.872 1.872 0 0 0-1.937 2.083v5.37h-1.858v-9.054ZM109.384 27.837c-.25 1.643-1.85 2.771-3.898 2.771-2.634 0-4.269-1.764-4.269-4.595 0-2.84 1.644-4.682 4.191-4.682 2.505 0 4.08 1.72 4.08 4.466v.637h-6.395v.112a2.353 2.353 0 0 0 .639 1.832 2.364 2.364 0 0 0 1.797.732 2.045 2.045 0 0 0 2.091-1.273h1.764Zm-6.282-2.702h4.526a2.167 2.167 0 0 0-.608-1.634 2.168 2.168 0 0 0-1.612-.664 2.293 2.293 0 0 0-2.306 2.298ZM37.826 8.731a2.64 2.64 0 0 1 2.808 2.965c0 1.906-1.03 3.002-2.808 3.002h-2.155V8.731h2.155Zm-1.228 5.123h1.125a1.877 1.877 0 0 0 1.967-2.146 1.881 1.881 0 0 0-1.967-2.133h-1.125v4.28ZM41.68 12.445a2.133 2.133 0 1 1 4.248 0 2.132 2.132 0 1 1-4.247 0Zm3.334 0c0-.976-.439-1.547-1.209-1.547-.772 0-1.206.57-1.206 1.547 0 .984.434 1.55 1.207 1.55.769 0 1.208-.57 1.208-1.55ZM51.573 14.697h-.922l-.93-3.316h-.07l-.927 3.316h-.913l-1.242-4.503h.902l.806 3.436h.067l.925-3.436h.853l.926 3.436h.07l.803-3.436h.889l-1.237 4.503ZM53.853 10.195h.856v.715h.066a1.348 1.348 0 0 1 1.344-.802 1.466 1.466 0 0 1 1.559 1.675v2.915h-.889v-2.692c0-.724-.314-1.084-.972-1.084a1.034 1.034 0 0 0-1.075 1.141v2.635h-.889v-4.503ZM59.094 8.437h.888v6.26h-.888v-6.26ZM61.218 12.444a2.133 2.133 0 1 1 4.248 0 2.134 2.134 0 1 1-4.248 0Zm3.333 0c0-.976-.439-1.547-1.208-1.547-.772 0-1.207.57-1.207 1.547 0 .984.435 1.55 1.207 1.55.77 0 1.208-.57 1.208-1.55ZM66.4 13.425c0-.81.604-1.278 1.676-1.344l1.22-.07v-.39c0-.475-.315-.744-.922-.744-.497 0-.84.183-.939.5h-.86c.09-.773.818-1.269 1.84-1.269 1.128 0 1.765.562 1.765 1.514v3.076h-.855v-.633h-.07a1.515 1.515 0 0 1-1.353.707 1.36 1.36 0 0 1-1.501-1.347Zm2.895-.385v-.376l-1.1.07c-.62.041-.9.252-.9.65 0 .405.351.64.834.64a1.062 1.062 0 0 0 1.166-.984ZM71.348 12.444c0-1.423.732-2.324 1.87-2.324a1.484 1.484 0 0 1 1.38.79h.067V8.437h.888v6.26h-.851v-.711h-.07a1.563 1.563 0 0 1-1.415.785c-1.145 0-1.869-.9-1.869-2.327Zm.918 0c0 .955.45 1.53 1.203 1.53.75 0 1.212-.583 1.212-1.526 0-.939-.468-1.53-1.212-1.53-.748 0-1.203.579-1.203 1.526ZM79.23 12.445a2.133 2.133 0 1 1 4.247 0 2.132 2.132 0 1 1-4.247 0Zm3.333 0c0-.976-.439-1.547-1.208-1.547-.773 0-1.207.57-1.207 1.547 0 .984.434 1.55 1.207 1.55.77 0 1.208-.57 1.208-1.55ZM84.67 10.195h.855v.715h.066a1.349 1.349 0 0 1 1.344-.802 1.466 1.466 0 0 1 1.559 1.675v2.915h-.889v-2.692c0-.724-.315-1.084-.972-1.084a1.034 1.034 0 0 0-1.075 1.141v2.635h-.889v-4.503ZM93.515 9.074v1.142h.976v.748h-.976v2.316c0 .472.195.678.637.678.113 0 .226-.007.339-.02v.74c-.16.028-.322.043-.484.045-.988 0-1.382-.348-1.382-1.216v-2.543h-.714v-.748h.715V9.074h.89ZM95.705 8.437h.88v2.481h.07a1.386 1.386 0 0 1 1.374-.807 1.485 1.485 0 0 1 1.55 1.679v2.907h-.889V12.01c0-.719-.335-1.083-.963-1.083a1.05 1.05 0 0 0-1.134 1.141v2.63h-.888v-6.26ZM104.761 13.482a1.823 1.823 0 0 1-1.951 1.302 2.047 2.047 0 0 1-2.08-2.324 2.093 2.093 0 0 1 .071-.88 2.08 2.08 0 0 1 2.005-1.473c1.253 0 2.009.856 2.009 2.27v.31h-3.18v.05a1.19 1.19 0 0 0 1.2 1.29 1.077 1.077 0 0 0 1.071-.545h.855Zm-3.126-1.452h2.275a1.094 1.094 0 0 0-.667-1.084 1.086 1.086 0 0 0-.442-.082 1.151 1.151 0 0 0-1.166 1.166Z"
        }
      ) })
    }
  );
}

function CallToAction() {
  return /* @__PURE__ */ jsxs(
    "section",
    {
      id: "get-free-shares-today",
      className: "relative overflow-hidden bg-mint-500 py-20 sm:py-28",
      children: [
        /* @__PURE__ */ jsx("div", { className: "absolute left-20 top-1/2 -translate-y-1/2 sm:left-1/2 sm:-translate-x-1/2", children: /* @__PURE__ */ jsx(CircleBackground, { color: "#fff", className: "animate-spin-slower" }) }),
        /* @__PURE__ */ jsx(Container, { className: "relative", children: /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-md sm:text-center", children: [
          /* @__PURE__ */ jsx("h2", { className: "text-3xl font-medium tracking-tight text-white sm:text-4xl", children: "Get your first tips today" }),
          /* @__PURE__ */ jsx("p", { className: "mt-4 text-lg text-mint-50", children: "It takes 30 seconds to sign up. Download the app and create an account today and we’ll send you a tip guaranteed to double your first investment." }),
          /* @__PURE__ */ jsx("div", { className: "mt-8 flex justify-center", children: /* @__PURE__ */ jsx(AppStoreLink, { color: "white" }) })
        ] }) })
      ]
    }
  );
}

const $$DemoPage = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "", "classes": "bg-white", "description": "", "purl": "" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "PrimaryFeatures", PrimaryFeatures, { "client:load": true, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/PrimaryFeatures.tsx", "client:component-export": "PrimaryFeatures" })} ${renderComponent($$result2, "SecondaryFeatures", SecondaryFeatures, {})} ${renderComponent($$result2, "CallToAction", CallToAction, {})} ${renderComponent($$result2, "Reviews", Reviews, { "client:load": true, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Reviews.tsx", "client:component-export": "Reviews" })} ` })}`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/demoPage.astro", void 0);

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/demoPage.astro";
const $$url = "/demoPage";

const demoPage = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$DemoPage,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

export { PhoneFrame as P, Reviews as R, demoPage as d };

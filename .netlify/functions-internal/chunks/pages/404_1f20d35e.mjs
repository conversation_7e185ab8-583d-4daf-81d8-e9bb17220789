/* empty css                            */import { createClient } from '@sanity/client';
import { e as createAstro, f as createComponent, r as renderTemplate, g as addAttribute, m as maybeRenderHead, u as unescapeHTML, h as renderSlot, i as renderComponent, j as renderHead } from '../astro_ca9e373b.mjs';
import 'clsx';
import { jsx, Fragment, jsxs } from 'react/jsx-runtime';
import { motion } from 'motion/react';
import { useState } from 'react';
import { Dialog, Disclosure } from '@headlessui/react';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';
import { ArrowUpIcon, ArrowDownIcon, XMarkIcon as XMarkIcon$1 } from '@heroicons/react/20/solid';
/* empty css                            */
const sanityClientInstance = createClient(
            {"apiVersion":"v2023-08-24","projectId":"hyj5exjm","dataset":"production","useCdn":true}
          );

globalThis.sanityClientInstance = sanityClientInstance;

const $$Astro$3 = createAstro("https://www.uown.co");
const $$ViewTransitions = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$3, $$props, $$slots);
  Astro2.self = $$ViewTransitions;
  const { fallback = "animate" } = Astro2.props;
  return renderTemplate`<meta name="astro-view-transitions-enabled" content="true"><meta name="astro-view-transitions-fallback"${addAttribute(fallback, "content")}>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/node_modules/astro/components/ViewTransitions.astro", void 0);

const Button = ({ text, color, type }) => {
  return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsx(
    "button",
    {
      type,
      className: color + " rounded-full px-2.5 py-1 text-lg font-bold text-white shadow-sm transition duration-150 ease-in hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2",
      "aria-label": text,
      children: text
    }
  ) });
};

const learn = [
  {
    name: "The Hub",
    href: "/the-hub"
  },
  {
    name: "Help",
    href: "/help-centre"
  },
  {
    name: "Risk",
    href: "/risk-statement"
  },
  {
    name: "Guides",
    href: "/guides/uk-property-investment-guide"
  }
];
function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}
function Example() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  return /* @__PURE__ */ jsxs(
    motion.header,
    {
      initial: { opacity: 0, y: -50 },
      animate: { opacity: 1, y: 0, duration: 10 },
      className: " isolate z-40 bg-white sticky top-0",
      children: [
        /* @__PURE__ */ jsxs("nav", { className: "mx-auto h-20 flex items-center justify-between px-6 lg:px-8 shadow-nav", "aria-label": "Global", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex lg:flex-1", children: [
            /* @__PURE__ */ jsxs("a", { href: "/", className: "-m-1.5 p-1.5 hidden md:inline-flex lg:inline-flex w-[120px]", children: [
              /* @__PURE__ */ jsx("span", { className: "sr-only", children: "UOWN" }),
              /* @__PURE__ */ jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "131", height: "31", viewBox: "0 0 131 31", fill: "none", children: [
                /* @__PURE__ */ jsx("path", { d: "M102.029 0.382911H97.2713C97.1319 0.380991 96.9935 0.407029 96.8644 0.459487C96.7352 0.511944 96.6179 0.589757 96.5193 0.688327C96.4207 0.786896 96.3429 0.904224 96.2905 1.03338C96.238 1.16253 96.212 1.30089 96.2139 1.44027V18.2196C96.1949 19.6171 95.6246 20.9506 94.6272 21.9296C93.6214 22.8642 92.2993 23.3836 90.9264 23.3836C89.5535 23.3836 88.2314 22.8642 87.2256 21.9296C86.3112 21.0052 85.7502 19.7892 85.6403 18.4936V1.44027C85.6422 1.30089 85.6162 1.16253 85.5637 1.03338C85.5113 0.904224 85.4335 0.786896 85.3349 0.688327C85.2363 0.589757 85.119 0.511944 84.9898 0.459487C84.8607 0.407029 84.7223 0.380991 84.583 0.382911H79.9568C79.8174 0.380991 79.6791 0.407029 79.5499 0.459487C79.4208 0.511944 79.3035 0.589757 79.2049 0.688327C79.1063 0.786896 79.0285 0.904224 78.976 1.03338C78.9236 1.16253 78.8975 1.30089 78.8995 1.44027V18.4893C78.8887 19.1414 78.7422 19.7841 78.4692 20.3765C78.1963 20.9688 77.8029 21.4978 77.3141 21.9296C76.3084 22.8642 74.9863 23.3836 73.6134 23.3836C72.2405 23.3836 70.9184 22.8642 69.9126 21.9296C68.9157 20.9503 68.3459 19.6169 68.3273 18.2196V1.44027C68.3292 1.30089 68.3032 1.16253 68.2507 1.03338C68.1983 0.904224 68.1205 0.786896 68.0219 0.688327C67.9233 0.589757 67.806 0.511944 67.6768 0.459487C67.5477 0.407029 67.4093 0.380991 67.2699 0.382911H62.5125C62.3732 0.380991 62.2348 0.407029 62.1056 0.459487C61.9765 0.511944 61.8592 0.589757 61.7606 0.688327C61.662 0.786896 61.5842 0.904224 61.5318 1.03338C61.4793 1.16253 61.4533 1.30089 61.4552 1.44027V18.2196C61.4512 19.8174 61.763 21.4003 62.3727 22.8773C62.9823 24.3543 63.8778 25.6962 65.0076 26.8261C66.1375 27.9559 67.4794 28.8514 68.9564 29.461C70.4334 30.0707 72.0163 30.3825 73.6141 30.3785C75.2406 30.3698 76.8492 30.0383 78.3465 29.4031C79.8439 28.768 81.2003 27.8419 82.337 26.6785C83.4737 27.8419 84.83 28.768 86.3274 29.4031C87.8248 30.0383 89.4333 30.3698 91.0598 30.3785C92.6575 30.3823 94.2403 30.0704 95.7171 29.4606C97.1939 28.8509 98.5357 27.9554 99.6654 26.8256C100.795 25.6957 101.69 24.3538 102.3 22.877C102.91 21.4001 103.221 19.8173 103.217 18.2196V1.44027C103.149 1.16449 102.999 0.915788 102.787 0.727011C102.575 0.538233 102.31 0.418253 102.029 0.382911Z", fill: "#101010" }),
                /* @__PURE__ */ jsx("path", { d: "M24.3178 18.2196C24.3178 21.4444 23.0368 24.537 20.7566 26.8173C18.4763 29.0975 15.3837 30.3785 12.1589 30.3785C8.93417 30.3785 5.84151 29.0975 3.56127 26.8173C1.28103 24.537 0 21.4444 0 18.2196L0 1.44027C0.019559 1.16631 0.137239 0.90858 0.331454 0.714365C0.525669 0.520151 0.783396 0.40247 1.05736 0.382911H5.81476C5.95415 0.380991 6.09251 0.407029 6.22166 0.459487C6.35081 0.511944 6.46814 0.589757 6.56671 0.688327C6.66528 0.786897 6.74309 0.904224 6.79555 1.03338C6.84801 1.16253 6.87404 1.30089 6.87212 1.44027V18.2196C6.89113 19.6171 7.46143 20.9506 8.45888 21.9296C8.94983 22.4032 9.52931 22.7754 10.1642 23.0249C10.7991 23.2744 11.4769 23.3963 12.1589 23.3837C12.8409 23.3963 13.5188 23.2744 14.1536 23.0249C14.7885 22.7754 15.368 22.4032 15.859 21.9296C16.8564 20.9506 17.4267 19.6171 17.4457 18.2196V1.44027C17.4438 1.30101 17.4698 1.16277 17.5222 1.03371C17.5745 0.904656 17.6522 0.787394 17.7506 0.688843C17.8491 0.590293 17.9662 0.51245 18.0952 0.459907C18.2242 0.407363 18.3624 0.381184 18.5016 0.382911H23.2591C23.3984 0.380991 23.5368 0.407029 23.666 0.459487C23.7951 0.511944 23.9124 0.589757 24.011 0.688327C24.1096 0.786897 24.1874 0.904224 24.2398 1.03338C24.2923 1.16253 24.3183 1.30089 24.3164 1.44027L24.3178 18.2196Z", fill: "#71E5BD" }),
                /* @__PURE__ */ jsx("path", { d: "M106.656 12.5417C106.656 9.31699 107.937 6.22432 110.218 3.94408C112.498 1.66384 115.591 0.382813 118.815 0.382812C122.04 0.382813 125.133 1.66384 127.413 3.94408C129.693 6.22432 130.974 9.31699 130.974 12.5417V29.3254C130.976 29.4647 130.95 29.6031 130.898 29.7322C130.845 29.8614 130.767 29.9787 130.669 30.0773C130.57 30.1759 130.453 30.2537 130.324 30.3061C130.195 30.3586 130.056 30.3846 129.917 30.3827H125.158C125.019 30.3846 124.88 30.3586 124.751 30.3061C124.622 30.2537 124.505 30.1759 124.406 30.0773C124.307 29.9787 124.23 29.8614 124.177 29.7322C124.125 29.6031 124.099 29.4647 124.101 29.3254V12.5417C124.065 11.1529 123.497 9.83083 122.514 8.84841C121.532 7.866 120.21 7.29816 118.821 7.26207C118.139 7.24945 117.461 7.37137 116.826 7.62087C116.191 7.87036 115.612 8.24254 115.121 8.71612C114.126 9.69296 113.557 11.0223 113.536 12.4162V29.1941C113.538 29.3335 113.511 29.4718 113.459 29.601C113.407 29.7301 113.329 29.8475 113.23 29.946C113.132 30.0446 113.014 30.1224 112.885 30.1749C112.756 30.2273 112.618 30.2534 112.478 30.2514H107.714C107.574 30.2534 107.436 30.2273 107.307 30.1749C107.178 30.1224 107.06 30.0446 106.962 29.946C106.863 29.8475 106.785 29.7301 106.733 29.601C106.68 29.4718 106.654 29.3335 106.656 29.1941V12.5417Z", fill: "#101010" }),
                /* @__PURE__ */ jsx("path", { d: "M46.9178 22.8499C45.6637 23.5434 44.2541 23.9072 42.821 23.9072C41.388 23.9072 39.9784 23.5434 38.7243 22.8499C37.3986 22.1228 36.2938 21.0514 35.5263 19.7488C34.7588 18.4461 34.3571 16.9604 34.3636 15.4484C34.3636 13.1702 35.2686 10.9852 36.8796 9.37425C38.4906 7.76329 40.6755 6.85826 42.9537 6.85826C45.232 6.85826 47.4169 7.76329 49.0279 9.37425C50.6389 10.9852 51.5439 13.1702 51.5439 15.4484C51.4714 16.9751 51.0079 18.4575 50.1978 19.7536C49.3878 21.0496 48.2583 22.1158 46.9178 22.8499ZM42.821 0.382821C39.8673 0.382821 36.9799 1.2587 34.524 2.89971C32.068 4.54071 30.1539 6.87313 29.0235 9.60202C27.8932 12.3309 27.5974 15.3337 28.1737 18.2307C28.7499 21.1276 30.1723 23.7887 32.2609 25.8773C34.3495 27.9659 37.0105 29.3882 39.9075 29.9645C42.8045 30.5407 45.8073 30.245 48.5361 29.1146C51.265 27.9843 53.5975 26.0701 55.2385 23.6142C56.8795 21.1583 57.7553 18.2709 57.7553 15.3171C57.7574 13.3553 57.3725 11.4124 56.6227 9.59958C55.873 7.78673 54.773 6.13956 53.3858 4.75237C51.9986 3.36518 50.3514 2.26521 48.5386 1.51542C46.7257 0.765634 44.7828 0.380756 42.821 0.382821Z", fill: "#101010" })
              ] })
            ] }),
            /* @__PURE__ */ jsxs("a", { href: "/", className: "-m-1.5 p-1.5 lg:hidden md:hidden", children: [
              /* @__PURE__ */ jsx("span", { className: "sr-only", children: "UOWN" }),
              /* @__PURE__ */ jsxs("svg", { width: "48", height: "24", viewBox: "0 0 48 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", children: [
                /* @__PURE__ */ jsx("path", { d: "M20.3386 14.2694C20.3386 16.8492 19.3162 19.3234 17.4962 21.1476C15.6763 22.9718 13.2079 23.9966 10.6341 23.9966C8.06037 23.9966 5.592 22.9718 3.77206 21.1476C1.95212 19.3234 0.929688 16.8492 0.929688 14.2694L0.929688 0.845967C0.945298 0.626796 1.03922 0.420614 1.19423 0.265242C1.34924 0.10987 1.55494 0.0157264 1.7736 7.91782e-05H5.57065C5.6819 -0.00145697 5.79233 0.0193733 5.89541 0.0613392C5.99849 0.103305 6.09214 0.165555 6.17081 0.244411C6.24948 0.323267 6.31159 0.417129 6.35345 0.520451C6.39532 0.623773 6.4161 0.734458 6.41457 0.845967V14.2694C6.42974 15.3875 6.88492 16.4542 7.68101 17.2375C8.07287 17.6163 8.53537 17.9141 9.04209 18.1137C9.54881 18.3133 10.0898 18.4108 10.6341 18.4007C11.1785 18.4108 11.7195 18.3133 12.2262 18.1137C12.7329 17.9141 13.1954 17.6163 13.5873 17.2375C14.3834 16.4542 14.8386 15.3875 14.8537 14.2694V0.845967C14.8522 0.734556 14.8729 0.623965 14.9147 0.52072C14.9565 0.417475 15.0185 0.323665 15.0971 0.244825C15.1756 0.165984 15.2692 0.10371 15.3721 0.0616755C15.475 0.0196407 15.5854 -0.00130318 15.6965 7.91782e-05H19.4936C19.6048 -0.00145697 19.7152 0.0193733 19.8183 0.0613392C19.9214 0.103305 20.015 0.165555 20.0937 0.244411C20.1724 0.323267 20.2345 0.417129 20.2764 0.520451C20.3182 0.623773 20.339 0.734458 20.3375 0.845967L20.3386 14.2694Z", fill: "#71E5BD" }),
                /* @__PURE__ */ jsx("path", { d: "M38.3749 17.9737C37.374 18.5285 36.2489 18.8195 35.1052 18.8195C33.9614 18.8195 32.8363 18.5285 31.8354 17.9737C30.7773 17.392 29.8955 16.5349 29.2829 15.4928C28.6704 14.4506 28.3498 13.262 28.355 12.0525C28.355 10.2299 29.0773 8.48193 30.3631 7.19315C31.6488 5.90438 33.3927 5.18035 35.2111 5.18035C37.0294 5.18035 38.7733 5.90438 40.0591 7.19315C41.3448 8.48193 42.0672 10.2299 42.0672 12.0525C42.0093 13.2738 41.6394 14.4598 40.9928 15.4966C40.3463 16.5334 39.4448 17.3864 38.3749 17.9737ZM35.1052 6.61337e-06C32.7477 6.61337e-06 30.4431 0.700712 28.483 2.01351C26.5228 3.32632 24.995 5.19225 24.0929 7.37536C23.1907 9.55847 22.9547 11.9607 23.4146 14.2783C23.8745 16.5959 25.0097 18.7247 26.6767 20.3956C28.3437 22.0665 30.4676 23.2043 32.7798 23.6653C35.0919 24.1263 37.4886 23.8897 39.6666 22.9855C41.8446 22.0812 43.7062 20.5498 45.0159 18.5851C46.3257 16.6204 47.0248 14.3104 47.0248 11.9475C47.0264 10.378 46.7192 8.82369 46.1208 7.37341C45.5224 5.92313 44.6444 4.6054 43.5373 3.49565C42.4301 2.3859 41.1154 1.50592 39.6685 0.906087C38.2216 0.306257 36.6709 -0.00164549 35.1052 6.61337e-06Z", fill: "#101010" })
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "flex lg:hidden", children: /* @__PURE__ */ jsxs(
            "button",
            {
              type: "button",
              className: "-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700",
              onClick: () => setMobileMenuOpen(true),
              children: [
                /* @__PURE__ */ jsx("span", { className: "sr-only", children: "Open main menu" }),
                /* @__PURE__ */ jsx(Bars3Icon, { className: "h-8", "aria-hidden": "true" })
              ]
            }
          ) }),
          /* @__PURE__ */ jsxs("div", { className: "hidden items-center lg:flex gap-x-16 h-full", children: [
            /* @__PURE__ */ jsx("a", { href: "/invest", className: "text-lg font-bold leading-6 text-black tracking-wide", children: "How it works?" }),
            /* @__PURE__ */ jsxs("div", { className: "group flex items-center gap-x-1 h-full", children: [
              /* @__PURE__ */ jsxs("button", { className: "peer flex items-center text-lg font-bold leading-6 text-black h-full", children: [
                "Learn",
                /* @__PURE__ */ jsx(ArrowUpIcon, { className: "group-hover:rotate-180 transition group-hover:duration-200 delay-25 group-hover:delay-25 duration-200 h-5 w-5 flex-none text-black", "aria-hidden": "true" })
              ] }),
              /* @__PURE__ */ jsx(
                motion.div,
                {
                  initial: { opacity: 0, y: 0 },
                  whileInView: { opacity: 1, y: 0, threshold: 0.99 },
                  transition: { duration: 0.5, ease: "easeIn" },
                  className: "hidden peer-hover:flex hover:flex transition peer-hover:duration-1000 delay-1000 peer-hover:delay-1000 absolute inset-x-0 top-0 -z-10 bg-white pt-14 shadow-nav",
                  children: /* @__PURE__ */ jsx("div", { className: "mx-auto grid max-w-md grid-cols-4 gap-x-10 mt-6 px-0 text-right", children: learn.map((item) => /* @__PURE__ */ jsx("div", { className: "group relative rounded-lg mb-6 text-lg leading-6", children: /* @__PURE__ */ jsxs("a", { href: item.href, className: "mt-6 block font-bold text-black", children: [
                    item.name,
                    /* @__PURE__ */ jsx("span", { className: "absolute inset-0" })
                  ] }) }, item.name)) })
                }
              )
            ] }),
            /* @__PURE__ */ jsx("a", { href: "https://app.uown.co/properties", className: "text-lg font-bold leading-6 text-black tracking-wide", children: "Invest" })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "hidden lg:flex lg:flex-1 lg:justify-end items-center", children: [
            /* @__PURE__ */ jsx("a", { href: "https://app.uown.co/login", className: "text-lg font-bold leading-6 text-black pr-6", children: "Login" }),
            /* @__PURE__ */ jsx("a", { href: "https://app.uown.co/register", className: "text-lg font-bold leading-6 text-black", children: /* @__PURE__ */ jsx(Button, { type: "button", color: "btn-black btn-sign-up", text: "Sign Up" }) })
          ] })
        ] }),
        /* @__PURE__ */ jsxs(Dialog, { as: "div", className: "lg:hidden", open: mobileMenuOpen, onClose: setMobileMenuOpen, children: [
          /* @__PURE__ */ jsx("div", { className: "fixed inset-0 z-30" }),
          /* @__PURE__ */ jsxs(Dialog.Panel, { className: "fixed inset-y-0 right-0 z-30 w-full overflow-y-auto bg-white", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between px-5 py-5 shadow-nav", children: [
              /* @__PURE__ */ jsxs("a", { href: "/", className: "-m-1.5 p-1.5 sm:hidden md:inline-flex", children: [
                /* @__PURE__ */ jsx("span", { className: "sr-only", children: "UOWN" }),
                /* @__PURE__ */ jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "104", height: "24", viewBox: "0 0 131 31", fill: "none", children: [
                  /* @__PURE__ */ jsx("path", { d: "M102.029 0.382911H97.2713C97.1319 0.380991 96.9935 0.407029 96.8644 0.459487C96.7352 0.511944 96.6179 0.589757 96.5193 0.688327C96.4207 0.786896 96.3429 0.904224 96.2905 1.03338C96.238 1.16253 96.212 1.30089 96.2139 1.44027V18.2196C96.1949 19.6171 95.6246 20.9506 94.6272 21.9296C93.6214 22.8642 92.2993 23.3836 90.9264 23.3836C89.5535 23.3836 88.2314 22.8642 87.2256 21.9296C86.3112 21.0052 85.7502 19.7892 85.6403 18.4936V1.44027C85.6422 1.30089 85.6162 1.16253 85.5637 1.03338C85.5113 0.904224 85.4335 0.786896 85.3349 0.688327C85.2363 0.589757 85.119 0.511944 84.9898 0.459487C84.8607 0.407029 84.7223 0.380991 84.583 0.382911H79.9568C79.8174 0.380991 79.6791 0.407029 79.5499 0.459487C79.4208 0.511944 79.3035 0.589757 79.2049 0.688327C79.1063 0.786896 79.0285 0.904224 78.976 1.03338C78.9236 1.16253 78.8975 1.30089 78.8995 1.44027V18.4893C78.8887 19.1414 78.7422 19.7841 78.4692 20.3765C78.1963 20.9688 77.8029 21.4978 77.3141 21.9296C76.3084 22.8642 74.9863 23.3836 73.6134 23.3836C72.2405 23.3836 70.9184 22.8642 69.9126 21.9296C68.9157 20.9503 68.3459 19.6169 68.3273 18.2196V1.44027C68.3292 1.30089 68.3032 1.16253 68.2507 1.03338C68.1983 0.904224 68.1205 0.786896 68.0219 0.688327C67.9233 0.589757 67.806 0.511944 67.6768 0.459487C67.5477 0.407029 67.4093 0.380991 67.2699 0.382911H62.5125C62.3732 0.380991 62.2348 0.407029 62.1056 0.459487C61.9765 0.511944 61.8592 0.589757 61.7606 0.688327C61.662 0.786896 61.5842 0.904224 61.5318 1.03338C61.4793 1.16253 61.4533 1.30089 61.4552 1.44027V18.2196C61.4512 19.8174 61.763 21.4003 62.3727 22.8773C62.9823 24.3543 63.8778 25.6962 65.0076 26.8261C66.1375 27.9559 67.4794 28.8514 68.9564 29.461C70.4334 30.0707 72.0163 30.3825 73.6141 30.3785C75.2406 30.3698 76.8492 30.0383 78.3465 29.4031C79.8439 28.768 81.2003 27.8419 82.337 26.6785C83.4737 27.8419 84.83 28.768 86.3274 29.4031C87.8248 30.0383 89.4333 30.3698 91.0598 30.3785C92.6575 30.3823 94.2403 30.0704 95.7171 29.4606C97.1939 28.8509 98.5357 27.9554 99.6654 26.8256C100.795 25.6957 101.69 24.3538 102.3 22.877C102.91 21.4001 103.221 19.8173 103.217 18.2196V1.44027C103.149 1.16449 102.999 0.915788 102.787 0.727011C102.575 0.538233 102.31 0.418253 102.029 0.382911Z", fill: "#101010" }),
                  /* @__PURE__ */ jsx("path", { d: "M24.3178 18.2196C24.3178 21.4444 23.0368 24.537 20.7566 26.8173C18.4763 29.0975 15.3837 30.3785 12.1589 30.3785C8.93417 30.3785 5.84151 29.0975 3.56127 26.8173C1.28103 24.537 0 21.4444 0 18.2196L0 1.44027C0.019559 1.16631 0.137239 0.90858 0.331454 0.714365C0.525669 0.520151 0.783396 0.40247 1.05736 0.382911H5.81476C5.95415 0.380991 6.09251 0.407029 6.22166 0.459487C6.35081 0.511944 6.46814 0.589757 6.56671 0.688327C6.66528 0.786897 6.74309 0.904224 6.79555 1.03338C6.84801 1.16253 6.87404 1.30089 6.87212 1.44027V18.2196C6.89113 19.6171 7.46143 20.9506 8.45888 21.9296C8.94983 22.4032 9.52931 22.7754 10.1642 23.0249C10.7991 23.2744 11.4769 23.3963 12.1589 23.3837C12.8409 23.3963 13.5188 23.2744 14.1536 23.0249C14.7885 22.7754 15.368 22.4032 15.859 21.9296C16.8564 20.9506 17.4267 19.6171 17.4457 18.2196V1.44027C17.4438 1.30101 17.4698 1.16277 17.5222 1.03371C17.5745 0.904656 17.6522 0.787394 17.7506 0.688843C17.8491 0.590293 17.9662 0.51245 18.0952 0.459907C18.2242 0.407363 18.3624 0.381184 18.5016 0.382911H23.2591C23.3984 0.380991 23.5368 0.407029 23.666 0.459487C23.7951 0.511944 23.9124 0.589757 24.011 0.688327C24.1096 0.786897 24.1874 0.904224 24.2398 1.03338C24.2923 1.16253 24.3183 1.30089 24.3164 1.44027L24.3178 18.2196Z", fill: "#71E5BD" }),
                  /* @__PURE__ */ jsx("path", { d: "M106.656 12.5417C106.656 9.31699 107.937 6.22432 110.218 3.94408C112.498 1.66384 115.591 0.382813 118.815 0.382812C122.04 0.382813 125.133 1.66384 127.413 3.94408C129.693 6.22432 130.974 9.31699 130.974 12.5417V29.3254C130.976 29.4647 130.95 29.6031 130.898 29.7322C130.845 29.8614 130.767 29.9787 130.669 30.0773C130.57 30.1759 130.453 30.2537 130.324 30.3061C130.195 30.3586 130.056 30.3846 129.917 30.3827H125.158C125.019 30.3846 124.88 30.3586 124.751 30.3061C124.622 30.2537 124.505 30.1759 124.406 30.0773C124.307 29.9787 124.23 29.8614 124.177 29.7322C124.125 29.6031 124.099 29.4647 124.101 29.3254V12.5417C124.065 11.1529 123.497 9.83083 122.514 8.84841C121.532 7.866 120.21 7.29816 118.821 7.26207C118.139 7.24945 117.461 7.37137 116.826 7.62087C116.191 7.87036 115.612 8.24254 115.121 8.71612C114.126 9.69296 113.557 11.0223 113.536 12.4162V29.1941C113.538 29.3335 113.511 29.4718 113.459 29.601C113.407 29.7301 113.329 29.8475 113.23 29.946C113.132 30.0446 113.014 30.1224 112.885 30.1749C112.756 30.2273 112.618 30.2534 112.478 30.2514H107.714C107.574 30.2534 107.436 30.2273 107.307 30.1749C107.178 30.1224 107.06 30.0446 106.962 29.946C106.863 29.8475 106.785 29.7301 106.733 29.601C106.68 29.4718 106.654 29.3335 106.656 29.1941V12.5417Z", fill: "#101010" }),
                  /* @__PURE__ */ jsx("path", { d: "M46.9178 22.8499C45.6637 23.5434 44.2541 23.9072 42.821 23.9072C41.388 23.9072 39.9784 23.5434 38.7243 22.8499C37.3986 22.1228 36.2938 21.0514 35.5263 19.7488C34.7588 18.4461 34.3571 16.9604 34.3636 15.4484C34.3636 13.1702 35.2686 10.9852 36.8796 9.37425C38.4906 7.76329 40.6755 6.85826 42.9537 6.85826C45.232 6.85826 47.4169 7.76329 49.0279 9.37425C50.6389 10.9852 51.5439 13.1702 51.5439 15.4484C51.4714 16.9751 51.0079 18.4575 50.1978 19.7536C49.3878 21.0496 48.2583 22.1158 46.9178 22.8499ZM42.821 0.382821C39.8673 0.382821 36.9799 1.2587 34.524 2.89971C32.068 4.54071 30.1539 6.87313 29.0235 9.60202C27.8932 12.3309 27.5974 15.3337 28.1737 18.2307C28.7499 21.1276 30.1723 23.7887 32.2609 25.8773C34.3495 27.9659 37.0105 29.3882 39.9075 29.9645C42.8045 30.5407 45.8073 30.245 48.5361 29.1146C51.265 27.9843 53.5975 26.0701 55.2385 23.6142C56.8795 21.1583 57.7553 18.2709 57.7553 15.3171C57.7574 13.3553 57.3725 11.4124 56.6227 9.59958C55.873 7.78673 54.773 6.13956 53.3858 4.75237C51.9986 3.36518 50.3514 2.26521 48.5386 1.51542C46.7257 0.765634 44.7828 0.380756 42.821 0.382821Z", fill: "#101010" })
                ] })
              ] }),
              /* @__PURE__ */ jsxs("a", { href: "/", className: "-m-1.5 p-1.5 lg:hidden md:hidden", children: [
                /* @__PURE__ */ jsx("span", { className: "sr-only", children: "UOWN" }),
                /* @__PURE__ */ jsxs("svg", { width: "48", height: "24", viewBox: "0 0 48 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", children: [
                  /* @__PURE__ */ jsx("path", { d: "M20.3386 14.2694C20.3386 16.8492 19.3162 19.3234 17.4962 21.1476C15.6763 22.9718 13.2079 23.9966 10.6341 23.9966C8.06037 23.9966 5.592 22.9718 3.77206 21.1476C1.95212 19.3234 0.929688 16.8492 0.929688 14.2694L0.929688 0.845967C0.945298 0.626796 1.03922 0.420614 1.19423 0.265242C1.34924 0.10987 1.55494 0.0157264 1.7736 7.91782e-05H5.57065C5.6819 -0.00145697 5.79233 0.0193733 5.89541 0.0613392C5.99849 0.103305 6.09214 0.165555 6.17081 0.244411C6.24948 0.323267 6.31159 0.417129 6.35345 0.520451C6.39532 0.623773 6.4161 0.734458 6.41457 0.845967V14.2694C6.42974 15.3875 6.88492 16.4542 7.68101 17.2375C8.07287 17.6163 8.53537 17.9141 9.04209 18.1137C9.54881 18.3133 10.0898 18.4108 10.6341 18.4007C11.1785 18.4108 11.7195 18.3133 12.2262 18.1137C12.7329 17.9141 13.1954 17.6163 13.5873 17.2375C14.3834 16.4542 14.8386 15.3875 14.8537 14.2694V0.845967C14.8522 0.734556 14.8729 0.623965 14.9147 0.52072C14.9565 0.417475 15.0185 0.323665 15.0971 0.244825C15.1756 0.165984 15.2692 0.10371 15.3721 0.0616755C15.475 0.0196407 15.5854 -0.00130318 15.6965 7.91782e-05H19.4936C19.6048 -0.00145697 19.7152 0.0193733 19.8183 0.0613392C19.9214 0.103305 20.015 0.165555 20.0937 0.244411C20.1724 0.323267 20.2345 0.417129 20.2764 0.520451C20.3182 0.623773 20.339 0.734458 20.3375 0.845967L20.3386 14.2694Z", fill: "#71E5BD" }),
                  /* @__PURE__ */ jsx("path", { d: "M38.3749 17.9737C37.374 18.5285 36.2489 18.8195 35.1052 18.8195C33.9614 18.8195 32.8363 18.5285 31.8354 17.9737C30.7773 17.392 29.8955 16.5349 29.2829 15.4928C28.6704 14.4506 28.3498 13.262 28.355 12.0525C28.355 10.2299 29.0773 8.48193 30.3631 7.19315C31.6488 5.90438 33.3927 5.18035 35.2111 5.18035C37.0294 5.18035 38.7733 5.90438 40.0591 7.19315C41.3448 8.48193 42.0672 10.2299 42.0672 12.0525C42.0093 13.2738 41.6394 14.4598 40.9928 15.4966C40.3463 16.5334 39.4448 17.3864 38.3749 17.9737ZM35.1052 6.61337e-06C32.7477 6.61337e-06 30.4431 0.700712 28.483 2.01351C26.5228 3.32632 24.995 5.19225 24.0929 7.37536C23.1907 9.55847 22.9547 11.9607 23.4146 14.2783C23.8745 16.5959 25.0097 18.7247 26.6767 20.3956C28.3437 22.0665 30.4676 23.2043 32.7798 23.6653C35.0919 24.1263 37.4886 23.8897 39.6666 22.9855C41.8446 22.0812 43.7062 20.5498 45.0159 18.5851C46.3257 16.6204 47.0248 14.3104 47.0248 11.9475C47.0264 10.378 46.7192 8.82369 46.1208 7.37341C45.5224 5.92313 44.6444 4.6054 43.5373 3.49565C42.4301 2.3859 41.1154 1.50592 39.6685 0.906087C38.2216 0.306257 36.6709 -0.00164549 35.1052 6.61337e-06Z", fill: "#101010" })
                ] })
              ] }),
              /* @__PURE__ */ jsxs(
                "button",
                {
                  type: "button",
                  className: "-m-2.5 rounded-md p-2.5 text-gray-700",
                  onClick: () => setMobileMenuOpen(false),
                  children: [
                    /* @__PURE__ */ jsx("span", { className: "sr-only", children: "Close menu" }),
                    /* @__PURE__ */ jsx(XMarkIcon, { className: "h-8 w-8", "aria-hidden": "true" })
                  ]
                }
              )
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "flex flex-col px-8 md:px-20 py-6 h-[88%]", children: [
              /* @__PURE__ */ jsxs("div", { className: "space-y-2 py-6", children: [
                /* @__PURE__ */ jsx(
                  "a",
                  {
                    href: "/invest",
                    className: "-mx-3 block px-3 py-8 text-lg font-bold leading-7 text-black nav-opt-border",
                    children: "How it works?"
                  }
                ),
                /* @__PURE__ */ jsx(Disclosure, { as: "div", className: "-mx-3 py-5 nav-opt-border", children: ({ open }) => /* @__PURE__ */ jsxs(Fragment, { children: [
                  /* @__PURE__ */ jsxs(Disclosure.Button, { className: "flex w-full items-center justify-between py-2 pl-3 pr-3.5 text-lg font-bold leading-7 text-black", children: [
                    "Learn",
                    /* @__PURE__ */ jsx(
                      ArrowDownIcon,
                      {
                        className: classNames(open ? "rotate-180" : "", "h-5 w-5 flex-none"),
                        "aria-hidden": "true"
                      }
                    )
                  ] }),
                  /* @__PURE__ */ jsx(Disclosure.Panel, { className: "mt-2 space-y-2", children: [...learn].map((item) => /* @__PURE__ */ jsx(
                    Disclosure.Button,
                    {
                      as: "a",
                      href: item.href,
                      className: "block py-2 pl-3 pr-3 text-sm font-medium leading-7 text-black",
                      children: item.name
                    },
                    item.name
                  )) })
                ] }) }),
                /* @__PURE__ */ jsx(
                  "a",
                  {
                    href: "https://app.uown.co/properties",
                    className: "-mx-3 block px-3 py-8 text-lg font-bold leading-7 text-black nav-opt-border",
                    children: "Invest"
                  }
                )
              ] }),
              /* @__PURE__ */ jsxs("div", { className: "flex items-end justify-center grow pb-2.5", children: [
                /* @__PURE__ */ jsx("div", { className: "pt-4 pr-6", children: /* @__PURE__ */ jsx(
                  "a",
                  {
                    href: "https://app.uown.co/login",
                    className: "-mx-3 block rounded-lg px-3 pb-3.5 md:text-lg text-lg font-bold leading-7 text-black",
                    children: "Login"
                  }
                ) }),
                /* @__PURE__ */ jsx("div", { className: "pt-6", children: /* @__PURE__ */ jsx(
                  "a",
                  {
                    href: "https://app.uown.co/register",
                    className: "-mx-3 block rounded-lg px-3 font-bold leading-7 text-black",
                    children: /* @__PURE__ */ jsx(Button, { type: "button", color: "btn-black btn-sign-up", text: "Sign Up" })
                  }
                ) })
              ] })
            ] })
          ] })
        ] })
      ]
    }
  );
}

function BannerC() {
  const [showBanner, setShowBanner] = useState(true);
  return /* @__PURE__ */ jsx(
    motion.div,
    {
      initial: { opacity: 0, y: -50 },
      animate: { opacity: 1, y: 0, duration: 10 },
      children: showBanner ? /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-x-6 bg-gray-900 px-6 py-2.5 sm:px-3.5 sm:before:flex-1", children: [
        /* @__PURE__ */ jsx("p", { className: "text-sm/6 text-white", children: /* @__PURE__ */ jsxs("a", { href: "/guides/uk-property-investment-guide", children: [
          /* @__PURE__ */ jsx("strong", { className: "font-semibold", children: "UK Property Investment Guide" }),
          /* @__PURE__ */ jsx("svg", { viewBox: "0 0 2 2", "aria-hidden": "true", className: "mx-2 inline size-0.5 fill-current", children: /* @__PURE__ */ jsx("circle", { r: 1, cx: 1, cy: 1 }) }),
          "Find out what is driving the market in 2025  ",
          /* @__PURE__ */ jsx("span", { "aria-hidden": "true", children: "→" })
        ] }) }),
        /* @__PURE__ */ jsx("div", { className: "flex flex-1 justify-end", children: /* @__PURE__ */ jsxs("button", { type: "button", className: "-m-3 p-3 focus-visible:outline-offset-[-4px]", children: [
          /* @__PURE__ */ jsx("span", { className: "sr-only", children: "Dismiss" }),
          /* @__PURE__ */ jsx(XMarkIcon$1, { "aria-hidden": "true", className: "size-5 text-white" })
        ] }) })
      ] }) : ""
    }
  );
}

const LogoLight = {"src":"/_astro/logo-light.24f9aac7.svg","width":99.74,"height":22.84,"format":"svg"};

const $$Footer = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${maybeRenderHead()}<footer class="bg-black-300" aria-labelledby="footer-heading"> <div class="mx-auto max-w-full px-6 pb-24 pt-16 sm:pt-24 lg:px-28 lg:py-24 md:px-20 md:py-12"> <div class="grid grid-cols-1 lg:grid-cols-3 lg:gap-12 md:grid-cols-2 md:gap-8 md:gap-x-16"> <div> <img class="h-7 md:h-9"${addAttribute(LogoLight.src, "src")} alt="Company name"> <p class="text-base md:text-lg leading-7 text-white !mt-4">
UOWN is a platform offering anyone the ability to invest and
          potentially earn money from property at the click of a button. Giving
          the ownership of UK property back to the people.
</p> <div class="flex space-x-6 mb-8 mt-3 lg:mt-12"> <a href="https://web.facebook.com/uownco?_rdc=1&_rdr"> <span class="sr-only">Facebook</span> <svg xmlns="http://www.w3.org/2000/svg" width="45" height="45" viewBox="0 0 45 45" fill="white" class="h-8 w-8 md:h-10 md:w-10 text-hover-mint"> <path d="M37.8281 0H7.17192C3.21098 0 0 3.21098 0 7.17192V37.8281C0 41.789 3.21098 45 7.17192 45H37.8281C41.789 45 45 41.789 45 37.8281V7.17192C45 3.21098 41.789 0 37.8281 0Z"></path> <path d="M23.7607 36.2701H17.9112C17.9112 36.179 17.9112 36.088 17.9112 36.0122C17.9112 31.5681 17.9112 27.1088 17.9112 22.6647C17.9112 22.6344 17.9112 22.5889 17.9112 22.5585C17.9112 22.4271 17.839 22.3613 17.6946 22.3613C16.8744 22.3613 16.0388 22.3613 15.2186 22.3613C15.002 22.3613 15.002 22.3613 15.002 22.149C15.002 20.6777 15.002 19.2065 15.002 17.7352C15.002 17.5835 15.0329 17.5229 15.2031 17.5229C16.0388 17.5229 16.8899 17.5229 17.741 17.5229C17.8957 17.5229 17.9267 17.4774 17.9267 17.3409C17.9267 16.2336 17.9267 15.1264 17.9576 14.0192C17.9886 12.9119 18.2052 11.835 18.7623 10.8491C19.3504 9.83289 20.217 9.19585 21.3311 8.81666C22.1513 8.54364 22.9869 8.4223 23.8381 8.40713C25.2308 8.39196 26.608 8.40713 28.0008 8.40713C28.0472 8.40713 28.0782 8.40713 28.1401 8.40713V13.2153C28.0782 13.2153 28.0163 13.2153 27.9544 13.2153C27.0413 13.2153 26.1438 13.2153 25.2308 13.2456C25.0606 13.2456 24.8903 13.2759 24.7356 13.3214C24.1321 13.4731 23.8381 13.8068 23.8226 14.4893C23.7916 15.4904 23.8226 16.4915 23.8226 17.5229H28.202C28.171 17.8111 28.1401 18.0689 28.1246 18.3268C28.0472 18.9941 27.9853 19.6463 27.9079 20.3137C27.846 20.9508 27.7687 21.5878 27.7068 22.24C27.7068 22.3765 27.6139 22.3765 27.5211 22.3765C26.3605 22.3765 25.1844 22.3765 24.0238 22.3765C23.9619 22.3765 23.8845 22.3765 23.8071 22.3765V36.3004L23.7607 36.2701Z" fill="#101010"></path> </svg> </a> <a href="https://www.instagram.com/uownco"> <span class="sr-only">Instagram</span> <svg xmlns="http://www.w3.org/2000/svg" width="45" height="45" viewBox="0 0 45 45" fill="white" class="h-8 w-8 md:h-10 md:w-10 text-hover-mint"> <path d="M37.8281 0H7.17192C3.21098 0 0 3.21098 0 7.17192V37.8281C0 41.789 3.21098 45 7.17192 45H37.8281C41.789 45 45 41.789 45 37.8281V7.17192C45 3.21098 41.789 0 37.8281 0Z"></path> <path d="M28.6001 35.188H16.2095C15.9501 35.1422 15.6907 35.127 15.4313 35.0659C12.5625 34.425 10.7161 32.6855 9.92261 29.832C9.8158 29.4657 9.78528 29.069 9.70898 28.6875C9.70898 24.5522 9.70898 20.4322 9.70898 16.2969C9.72424 16.2053 9.7395 16.1138 9.75476 16.0375C9.92261 15.3813 10.0142 14.6794 10.2888 14.069C11.5554 11.3376 13.7222 9.87269 16.7436 9.82691C20.5126 9.78113 24.2817 9.81165 28.0355 9.82691C29.5004 9.82691 30.8432 10.2237 32.0335 11.0629C34.0477 12.4973 35.0548 14.481 35.0701 16.953C35.0701 20.6458 35.0701 24.3538 35.0701 28.0466C35.0701 28.5197 35.0396 29.008 34.948 29.481C34.3071 32.3498 32.5675 34.1961 29.7293 34.9744C29.3631 35.0812 28.9663 35.1117 28.5848 35.188H28.6001ZM22.42 12.07C20.5737 12.07 18.7425 12.07 16.8962 12.07C16.3163 12.07 15.7364 12.1311 15.1871 12.3295C13.1729 13.0466 11.9674 14.771 11.9674 16.953C11.9674 20.6458 11.9674 24.3538 11.9674 28.0466C11.9674 28.596 12.0437 29.1758 12.2268 29.6946C12.9135 31.7089 14.653 32.9296 16.8351 32.9296C20.5279 32.9296 24.2359 32.9296 27.9287 32.9296C28.478 32.9296 29.0579 32.8533 29.5767 32.6702C31.5909 31.9988 32.8117 30.2592 32.8117 28.0924C32.8117 24.3691 32.8117 20.6458 32.8117 16.9225C32.8117 15.2593 32.1403 13.9164 30.7822 12.9398C29.9277 12.3142 28.9511 12.07 27.8982 12.07C26.067 12.07 24.2359 12.07 22.4048 12.07H22.42Z" fill="#101010"></path> <path d="M28.9349 22.5225C28.9197 26.1237 25.9594 29.0687 22.3734 29.0382C18.7569 29.0077 15.8271 26.0474 15.8577 22.4767C15.8882 18.8755 18.8332 15.9609 22.4039 15.9609C26.0051 15.9609 28.9502 18.9365 28.9349 22.5072V22.5225ZM22.4039 18.2193C20.0387 18.2193 18.116 20.142 18.116 22.492C18.116 24.8572 20.0387 26.7798 22.3887 26.7798C24.7539 26.7798 26.6766 24.8572 26.6766 22.5072C26.6766 20.142 24.7539 18.2193 22.4039 18.2193Z" fill="#101010"></path> <path d="M29.2258 17.3644C28.3255 17.3644 27.5625 16.6167 27.5625 15.7164C27.5625 14.8161 28.295 14.0836 29.1953 14.0684C30.1108 14.0684 30.8585 14.8008 30.8585 15.7164C30.8585 16.6167 30.1108 17.3491 29.2258 17.3644Z" fill="#101010"></path> </svg> </a> <a href="https://www.linkedin.com/company/uown.co"> <span class="sr-only">Linkedin</span> <svg xmlns="http://www.w3.org/2000/svg" width="45" height="45" viewBox="0 0 45 45" fill="white" class="h-8 w-8 md:h-10 md:w-10 text-hover-mint"> <path d="M37.8281 0H7.17192C3.21098 0 0 3.21098 0 7.17192V37.8281C0 41.789 3.21098 45 7.17192 45H37.8281C41.789 45 45 41.789 45 37.8281V7.17192C45 3.21098 41.789 0 37.8281 0Z"></path> <path d="M10.6855 34.6837H15.7517V19.3633H10.6855V34.6837Z" fill="#101010"></path> <path d="M24.3134 34.2876C24.3134 31.5257 24.3134 28.779 24.3134 26.017C24.3134 25.7881 24.3134 25.5745 24.344 25.3456C24.4813 23.8654 26.1293 22.7668 27.5484 23.2093C28.7387 23.5755 29.4253 24.5369 29.4253 25.8492C29.4253 28.6569 29.4253 31.4646 29.4253 34.2876V34.6844H34.5372C34.5372 34.5928 34.5372 34.5165 34.5372 34.4555C34.5372 31.2968 34.5372 28.1381 34.5372 24.9946C34.5372 23.1483 33.7895 21.6223 32.4009 20.4473C30.5698 18.9061 28.0062 18.3873 25.7326 19.9896C25.229 20.3405 24.8017 20.8136 24.3134 21.2408V19.3944H19.2168V34.6996H24.3134V34.2876Z" fill="#101010"></path> <path d="M13.204 16.4352C14.8978 16.4352 16.2711 15.0771 16.2711 13.368C16.2711 11.6742 14.8825 10.2856 13.204 10.3009C11.5102 10.3009 10.1216 11.72 10.1368 13.3833C10.1521 15.0771 11.5102 16.4199 13.204 16.4352Z" fill="#101010"></path> </svg> </a> <a href="https://www.messenger.com/login.php?next=https%3A%2F%2Fwww.messenger.com%2Ft%2F1625349364441897%2F%3Fmessaging_source%3Dsource%253Apages%253Amessage_shortlink"> <span class="sr-only">Messenger</span> <svg xmlns="http://www.w3.org/2000/svg" width="45" height="45" viewBox="0 0 45 45" fill="white" class="h-8 w-8 md:h-10 md:w-10 text-hover-mint"> <path d="M37.8281 0H7.17192C3.21098 0 0 3.21098 0 7.17192V37.8281C0 41.789 3.21098 45 7.17192 45H37.8281C41.789 45 45 41.789 45 37.8281V7.17192C45 3.21098 41.789 0 37.8281 0Z"></path> <path d="M14.4849 34.7003C14.4849 34.624 14.4849 34.5629 14.4849 34.5019C14.4849 33.0675 14.4849 31.6484 14.4849 30.214C14.4849 30.0767 14.4392 30.0004 14.3476 29.9241C12.3334 28.2913 10.96 26.2771 10.4107 23.7746C9.78507 20.9821 10.2734 18.3422 11.8451 15.916C13.7525 13.0014 16.5297 11.2466 20.0089 10.5752C23.2591 9.93431 26.372 10.4226 29.2408 12.0554C31.8959 13.566 33.7728 15.7329 34.6273 18.6322C35.6039 21.9587 35.0088 25.0716 32.903 27.8641C30.9956 30.3971 28.3862 31.9078 25.2428 32.5334C23.2591 32.9302 21.2906 32.8539 19.3374 32.3656C19.2611 32.3503 19.1696 32.3656 19.1086 32.3809C17.6284 33.1133 16.1482 33.861 14.6681 34.6087C14.6223 34.624 14.5765 34.6545 14.5002 34.685L14.4849 34.7003Z" fill="#101010"></path> <path d="M30.1878 18.6169C28.0668 20.8296 25.961 23.0422 23.8399 25.27C22.787 24.1866 21.7494 23.1185 20.7118 22.035C18.7891 23.0727 16.8664 24.1103 14.959 25.148V25.1174C17.08 22.9201 19.2316 20.738 21.3832 18.5254C21.6578 18.8001 21.9325 19.0747 22.2072 19.3341C22.9549 20.0513 23.7026 20.7838 24.4503 21.501C24.5419 21.5925 24.6029 21.5925 24.7097 21.5315C26.4645 20.5701 28.2346 19.6241 29.9895 18.6627C30.0505 18.6322 30.1115 18.6017 30.1726 18.5712C30.1726 18.5712 30.1726 18.5864 30.1878 18.6017V18.6169Z"></path> </svg> </a> </div> </div> <div class="md:grid md:grid-cols-1 md:gap-6 text-base md:text-lg leading-7 text-white"> <div> <h3 class="font-bold underline">
HELP AND RESOURCES
</h3> <ul role="list" class="mt-4 space-y-0"> <li> <a href="/contact" class="text-hover-mint">
Contact Us
</a> </li> <li> <a href="/help-centre" class="text-hover-mint">
Help Centre
</a> </li> <li> <a href="/the-hub/1" class="text-hover-mint">
The Hub
</a> </li> </ul> </div> <div class="mt-8 md:mt-0 lg:mt-10 text-base md:text-lg leading-7 text-white"> <h3 class="font-bold underline">
ACCOUNT
</h3> <ul role="list" class="mt-4 space-y-0"> <li> <a href="https://app.uown.co/login" class="text-hover-mint">
Login
</a> </li> <li> <a href="https://app.uown.co/register" class="text-hover-mint">
Register
</a> </li> </ul> </div> </div> <div class="md:grid md:grid-cols-1 md:gap-6 mt-8 md:mt-0 lg:mt-0 text-base md:text-lg leading-7 text-white"> <div> <h3 class="font-bold underline">
LEGAL RESOURCES
</h3> <ul role="list" class="mt-4 space-y-0"> <li> <a href="/terms-and-conditions" class="text-hover-mint">
Terms & Conditions
</a> </li> <li> <a href="/privacy-policy" class="text-hover-mint">
Privacy Policy
</a> </li> <li> <a href="/cookie-policy" class="text-hover-mint">
Cookie Policy
</a> </li> <li> <a href="https://mangopay.com/terms/payment-services_EN_2024_02.pdf" ' class="text-hover-mint">
MangoPay T&Cs
</a> </li> <li> <a href="/risk-statement" class="text-hover-mint">
Risk Statement
</a> </li> </ul> </div> </div> </div> </div> </footer> <div class="bg-black-50 lower text-white"> <div class="mx-auto max-w-full px-8 py-8  md:px-20 md:py-12 lg:px-28 lg:py-24 container space-y-5"> <p>
UOWN is a trading name of U Own Exchange Limited. UOWN, 3rd Floor,
      Northgate, 118 North Street, Leeds, LS2 7PN
</p> <p>
House prices can fall as well as rise and you may not get back all of the
      money you invest. Rates of return quoted on our website are estimates only
      and are not guaranteed. Investments are not protected under the Financial
      Services Compensation Scheme.
</p> <a href="/risk-statement" class="text-hover-mint inline-block">
For more details please see our in-depth risk statement.
</a> </div> </div>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Footer.astro", void 0);

var __freeze$2 = Object.freeze;
var __defProp$2 = Object.defineProperty;
var __template$2 = (cooked, raw) => __freeze$2(__defProp$2(cooked, "raw", { value: __freeze$2(raw || cooked.slice()) }));
var _a$2;
const $$Posthog = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate(_a$2 || (_a$2 = __template$2([`<script async>
  !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys getNextSurveyStep onSessionId".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
  posthog.init(
    'phc_Bw6vfs5tnvJF6RRRpJkZzaPdAmEnuNcxFTc7g1jVzSD',
    {
      api_host:'https://eu.i.posthog.com'
      // the "loaded" argument has been removed
    }
  )
<\/script>`])));
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/posthog.astro", void 0);

var __freeze$1 = Object.freeze;
var __defProp$1 = Object.defineProperty;
var __template$1 = (cooked, raw) => __freeze$1(__defProp$1(cooked, "raw", { value: __freeze$1(raw || cooked.slice()) }));
var _a$1;
const $$Astro$2 = createAstro("https://www.uown.co");
const $$OrganizationJSONLD = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$2, $$props, $$slots);
  Astro2.self = $$OrganizationJSONLD;
  Astro2.props;
  const schema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "UOWN",
    "url": "https://www.uown.co/",
    "description": "Join UOWN, the leading property crowdfunding and investment platform. Explore profitable investment opportunities today.",
    "logo": "https://res.cloudinary.com/uown-sd/image/upload/v1611222904/logo_n1ldrb.webp",
    "sameAs": [
      "https://web.facebook.com/uownco?_rdc=1&_rdr",
      "https://www.instagram.com/uownco",
      "https://www.linkedin.com/company/uown.co",
      "https://twitter.com/uownco"
    ],
    address: "UOWN, 3rd Floor, Northgate, 118 North Street, Leeds, LS2 7PN"
  };
  return renderTemplate(_a$1 || (_a$1 = __template$1(['<script type="application/ld+json">', "<\/script>"])), unescapeHTML(JSON.stringify(schema)));
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/StructuredData/OrganizationJSONLD.astro", void 0);

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(raw || cooked.slice()) }));
var _a;
const $$Astro$1 = createAstro("https://www.uown.co");
const $$Layout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$Layout;
  const { title, description, classes, purl } = Astro2.props;
  const defaultPreviewUrl = Astro2.url.origin + "/own.png";
  const previewUrl = purl != null ? purl : defaultPreviewUrl;
  return renderTemplate(_a || (_a = __template(['<html lang="en"> <head><link rel="sitemap" href="/sitemap-index.xml"><meta charset="UTF-8"><meta name="title"', '><meta property="og:title"', '><meta name="description"', '><meta property="og:description"', '><meta name="viewport" content="width=device-width"><meta property="og:image"', '><meta property="og:image:secure_url"', '><meta property="og:image:url"', '><link rel="canonical"', '><link rel="icon" type="image/x-icon" href="/favicon.ico"><meta name="generator"', "><title>", '</title><script>\n			window.dataLayer = window.dataLayer || [];\n			function gtag() {\n				dataLayer.push(arguments);\n			}\n\n			// Google Tag Manager TOP OF HEAD TAG\n			(function (w, d, s, l, i) {\n				w[l] = w[l] || [];\n				w[l].push({\n					"gtm.start": new Date().getTime(),\n					event: "gtm.js",\n				});\n				var f = d.getElementsByTagName(s)[0],\n					j = d.createElement(s),\n					dl = l != "dataLayer" ? "&l=" + l : "";\n				j.async = true;\n				j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;\n				f.parentNode.insertBefore(j, f);\n			})(window, document, "script", "dataLayer", "GTM-WXD5GDR");\n		<\/script><!-- End Google Tag Manager --><!-- Structured Data slot -->', "", "", "", "", "</head> <body", '> <noscript> <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WXD5GDR" height="0" width="0" style="display:none;visibility:hidden">\n			</iframe> </noscript> ', " ", " ", " ", " ", " ", " </body></html>"])), addAttribute(title, "content"), addAttribute(title, "content"), addAttribute(description, "content"), addAttribute(description, "content"), addAttribute(previewUrl, "content"), addAttribute(previewUrl, "content"), addAttribute(previewUrl, "content"), addAttribute(Astro2.url.href, "href"), addAttribute(Astro2.generator, "content"), title, renderSlot($$result, $$slots["structuredData"]), renderComponent($$result, "OrganizationJSONLD", $$OrganizationJSONLD, {}), renderComponent($$result, "PostHog", $$Posthog, {}), renderComponent($$result, "ViewTransitions", $$ViewTransitions, {}), renderHead(), addAttribute(classes, "class"), renderComponent($$result, "Nav", Example, { "client:load": true, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Nav.jsx", "client:component-export": "default" }), renderComponent($$result, "Banner", BannerC, { "client:load": true, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Banner.jsx", "client:component-export": "default" }), renderSlot($$result, $$slots["default"]), renderComponent($$result, "Footer", $$Footer, {}), renderSlot($$result, $$slots["addJs"]), renderSlot($$result, $$slots["addJs2"]));
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/layouts/Layout.astro", void 0);

const $$Astro = createAstro("https://www.uown.co");
const $$Button = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Button;
  const { color, text, type } = Astro2.props;
  const className = color + " rounded-full px-4 py-2.5 text-lg font-bold text-white shadow-sm transition duration-150 ease-in hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-mint-300";
  return renderTemplate`${maybeRenderHead()}<button${addAttribute(type, "type")}${addAttribute(className, "class")}${addAttribute(text, "aria-label")}>${text}</button>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Button.astro", void 0);

const Error = {"src":"/_astro/404-error.01050a38.png","width":1920,"height":911,"format":"png"};

const ErrorSmall = {"src":"/_astro/404-error-small.022f6abb.png","width":800,"height":1260,"format":"png"};

const $$404 = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Error 404 page", "classes": "", "description": "", "purl": "" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="relative isolate min-h-full h-[88vh]"> <img${addAttribute(Error.src, "src")} alt="" class="hidden lg:block bg-mint-300 absolute inset-0 -z-10 h-full w-full object-cover object-top"> <img${addAttribute(ErrorSmall.src, "src")} alt="" class="lg:hidden bg-mint-300 absolute inset-0 -z-10 h-full w-full object-cover object-top"> <div class="flex flex-col justify-center h-full mx-auto max-w-7xl px-6 py-32 text-center sm:py-40 lg:px-8"> <h1 class="mt-4 m-auto mb-0 text-4xl font-extrabold text-white md:text-5xl lg:text-6xl max-w-[750px] tracking-normal">Sorry. This page does not 
        exist anymore.</h1> <div class="lg:mt-12 mt-6 flex justify-center"> <a href="/">${renderComponent($$result2, "Button", $$Button, { "type": "button", "color": "btn-black btn-homepage", "text": "Home Page" })}</a> </div> </div> </main> ` })}`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/404.astro", void 0);

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/404.astro";
const $$url = "/404";

const _404 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
        __proto__: null,
        default: $$404,
        file: $$file,
        url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

export { $$Layout as $, _404 as _, $$Button as a, $$Footer as b, $$ViewTransitions as c, $$Posthog as d, $$OrganizationJSONLD as e };

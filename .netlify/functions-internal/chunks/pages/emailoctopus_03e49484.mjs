import { PostHog } from 'posthog-node';

let posthogClient = null;

function PostHogNode() {
  if (!posthogClient) {
    posthogClient = new PostHog('phc_Bw6vfs5tnvJF6RRRpJkZzaPdAmEnuNcxFTc7g1jVzSD', {
      host: 'https://eu.i.posthog.com',
    });
  }
  return posthogClient;
}

const POST = async ({ request }) => {
  const data = await request.formData();
  const firstname = data.get("firstName");
  const lastName = data.get("lastName");
  const email = data.get("email");
  const phone = data.get("phoneNo");
  const distinctid = data.get("distinctid");
  const userAgent = data.get("userAgent");
  const variant = data.get("variant");
  const gScore = data.get("gScore");
  const fbc = data.get("fbc");
  var fbclid = "";
  if (fbc !== "") {
    const fbcA = fbc.split(".");
    fbclid = fbcA[3];
  }
  const response = await fetch(`https://api.emailoctopus.com/lists/${"891e0428-de3e-11ef-9635-73101c13194a"}/contacts`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": `Bearer ${"eo_d048defc7b110221f7624976371324325a4021f99cd6626c9f8856b87a27cc77"}`
    },
    body: JSON.stringify({
      "email_address": email,
      "fields": {
        "FirstName": firstname,
        "LastName": lastName,
        "PhoneNumber": phone
      },
      "tags": {},
      "status": "subscribed"
    })
  });
  console.log(response);
  if (response.status == 201) {
    await PostHogNode().capture({
      distinctId: distinctid,
      event: "Successful form submission",
      properties: {
        em: email,
        first_name: firstname,
        ln: lastName,
        distinct_id: distinctid,
        client_user_agent: userAgent,
        recaptcha_score: gScore,
        _fbc: fbc,
        fbclid,
        "$feature/uk-investment-guide-landing-page-1": variant
      }
    });
    return new Response(
      JSON.stringify({
        message: "success"
      }),
      { status: 200 }
    );
  } else {
    return new Response(
      JSON.stringify({
        message: "error"
      }),
      { status: 500 }
    );
  }
};

const emailoctopus = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  POST
}, Symbol.toStringTag, { value: 'Module' }));

export { PostHogNode as P, emailoctopus as e };

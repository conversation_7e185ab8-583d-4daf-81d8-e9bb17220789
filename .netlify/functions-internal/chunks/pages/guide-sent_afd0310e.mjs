import { $ as $$Layout } from './404_1f20d35e.mjs';
import { f as createComponent, r as renderTemplate, i as renderComponent } from '../astro_ca9e373b.mjs';
import 'clsx';
import { jsx, jsxs } from 'react/jsx-runtime';
/* empty css                            */import '@sanity/client';
import 'motion/react';
import 'react';
import '@headlessui/react';
import '@heroicons/react/24/outline';
import '@heroicons/react/20/solid';
/* empty css                            */import 'html-escaper';

const ThumbsUp = {"src":"/_astro/thumbs-up.9a080146.png","width":201,"height":200,"format":"png"};

function GuideSent() {
  return /* @__PURE__ */ jsx("section", { className: "py-16 p-10 md:p-16", children: /* @__PURE__ */ jsxs("div", { className: "text-black-200 flex flex-col gap-y-3 md:gap-y-6 justify-center items-center", children: [
    /* @__PURE__ */ jsx("img", { className: "w-[150px] h-[150px] md:w-[200px] md:h-[200px]", src: ThumbsUp.src }),
    /* @__PURE__ */ jsx("h1", { className: "uppercase text-4xl md:text-6xl mlg:text-8xl font-extrabold", children: "Thank you!" }),
    /* @__PURE__ */ jsx("p", { className: "text-md md:text-xl mlg:text-4xl font-extrabold", children: "Your guide is on it's way" }),
    /* @__PURE__ */ jsx("p", { className: "text-base mlg:text-lg pb-3", children: "We have sent the guide to your inbox." }),
    /* @__PURE__ */ jsx("a", { href: "/", className: "cursor-pointer py-1.5 px-4 text-base mlg:text-lg text-white bg-mint-300 rounded-full", children: "Go back to homepage" })
  ] }) });
}

const $$GuideSent = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "", "classes": "bg-white", "description": "", "purl": "" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "GuideSents", GuideSent, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/GuideSent.jsx", "client:component-export": "default" })} ` })}`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/guide-sent.astro", void 0);

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/guide-sent.astro";
const $$url = "/guide-sent";

export { $$GuideSent as default, $$file as file, $$url as url };

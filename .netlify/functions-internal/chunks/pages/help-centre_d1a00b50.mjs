import { $ as $$Layout } from './404_1f20d35e.mjs';
import { e as createAstro, f as createComponent, r as renderTemplate, i as renderComponent, m as maybeRenderHead, g as addAttribute } from '../astro_ca9e373b.mjs';
import { d as getHelpArticlesByTopic, e as getAllTopics, f as $$HelpHeroSection, h as $$AllHelpTopicsBar, j as $$ArticleTile, a as getReadingTime, b as getFullUrl, u as urlForImage, k as $$PopularArticles, l as $$GotMoreQuestions, $ as $$StartJourneyCTA } from './_slug__d49fd2f5.mjs';
/* empty css                            */import '@sanity/client';
import 'clsx';
import 'react/jsx-runtime';
import 'motion/react';
import 'react';
import '@headlessui/react';
import '@heroicons/react/24/outline';
import '@heroicons/react/20/solid';
/* empty css                            */import 'html-escaper';
import '@astrojs/internal-helpers/path';
/* empty css                            */import '@portabletext/react';
import '../astro-assets-services_967ef4fc.mjs';
/* empty css                                 */import '@sanity/image-url';
import 'groq';
/* empty css                                 */
const $$Astro = createAstro("https://www.uown.co");
const $$HelpCentre = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$HelpCentre;
  const helpArticles = await getHelpArticlesByTopic(
    "8d593721-544f-48da-8204-1cd00f634900"
  );
  const topics = await getAllTopics();
  const purl = Astro2.url.origin + "/previews/help-center.png";
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Help Centre | Support & FAQs | UOWN", "classes": "", "description": "Find answers to your questions at the UOWN Help Centre. Get support and answers about our crowdfunding and investment platform.", "purl": purl }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main> ${renderComponent($$result2, "HeroSection", $$HelpHeroSection, { "isIpadHidden": true })} <section class="hidden lg:block bg-gray-pages text-center"> <p class="max-w-4xl lg:text-5xl md:text-4xl tracking-normal font-extrabold py-24 mx-auto">
Getting Started
</p> <div class="flex px-28 pb-24 lxl:px-36 xl:px-60 "> ${renderComponent($$result2, "TopicsBar", $$AllHelpTopicsBar, { "isSticky": false, "hasBorder": false, "selectedTopic": "Getting Started" })} <div class="hidden lg:flex flex-wrap mb-auto justify-left"> ${helpArticles.map((article) => renderTemplate`${renderComponent($$result2, "ArticleTile", $$ArticleTile, { "readingTime": getReadingTime("article", article.slug.current), "postHref": getFullUrl(Astro2.url.origin, article.fullSlug), "title": article.title, "sectionType": "topic" })}`)} </div> </div> </section> <section class="block lg:hidden bg-gray-pages text-center"> <p class="max-w-4xl md:text-4xl text-3xl tracking-normal font-extrabold md:pt-24 md:pb-4 py-12 mx-auto">
Help Centre
</p> <p class="max-w-4xl hidden md:block md:text-xl tracking-normal font-regular pb-24 mx-auto">
Take a look through our helpful articles?
</p> <div class="flex px-7 md:px-20 pb-24"> <div class="flex w-full lg:hidden flex-wrap mb-auto justify-center gap-x-7"> ${topics.map((topic) => renderTemplate`<a class="grow bg-white rounded-csm flex justify-between items-center text-left text-lg tracking-wide font-bold px-6 mb-6 w-full md:w-[306px] h-[100px] shadow-tile"${addAttribute(getFullUrl(Astro2.url.origin, topic.full_slug), "href")}> <span>${topic.name}</span> <img class="max-h-[42px]"${addAttribute(urlForImage(topic.icon).url(), "src")}> </a>`)} </div> </div> </section> ${renderComponent($$result2, "PopularArticles", $$PopularArticles, { "isHelpCentreMainPage": true })} ${renderComponent($$result2, "MoreQuestions", $$GotMoreQuestions, { "isTopicPage": false })} ${renderComponent($$result2, "StartJourneyCTA", $$StartJourneyCTA, { "colorClass": "" })} </main> ` })}`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/help-centre.astro", void 0);

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/help-centre.astro";
const $$url = "/help-centre";

export { $$HelpCentre as default, $$file as file, $$url as url };

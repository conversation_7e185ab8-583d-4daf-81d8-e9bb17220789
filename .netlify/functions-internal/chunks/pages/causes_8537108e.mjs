import { $ as $$Layout } from './404_1f20d35e.mjs';
import { e as createAstro, f as createComponent, r as renderTemplate, m as maybeRenderHead, g as addAttribute, i as renderComponent } from '../astro_ca9e373b.mjs';
import 'clsx';
import { g as getCategoryPosts, a as getReadingTime, b as getFullUrl, u as urlForImage, $ as $$StartJourneyCTA } from './_slug__d49fd2f5.mjs';
/* empty css                            */
const $$Astro$1 = createAstro("https://www.uown.co");
const $$PostTile = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$PostTile;
  const { readingTime, postHref, imgSrc, title, color } = Astro2.props;
  const imgClass = "z-0 rounded-t-[20px] hidden md:block object-cover h-[150px] lg:h-[200px] border-solid border-b-[10px] " + color;
  return renderTemplate`${maybeRenderHead()}<a class="flex flex-col flex-wrap shrink-0 w-full md:w-[222px] lg:w-[389px] mb-6 md:mb-12"${addAttribute(postHref, "href")}> <img${addAttribute(imgClass, "class")}${addAttribute(imgSrc, "src")}> <div${addAttribute("flex flex-col justify-between md:h-[150px] lg:h-[200px] p-3 lg:p-6 rounded-[20px] md:rounded-t-none  md:rounded-b-[20px] shadow-articletile md:border-0 border-solid border-[1.4px] " + color, "class")}> <p class="text-lg lg:text-2xl tracking-normal font-bold pb-6 md:pb-0">${title}</p> <p class="text-sm lg:text-lg tracking-wide font-medium"> ${readingTime} min read<span class="pl-3">→</span></p> </div> </a>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/PostTile.astro", void 0);

const $$Astro = createAstro("https://www.uown.co");
const $$Causes = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Causes;
  const categoryPosts = await getCategoryPosts("e17e0246-9f28-4830-b7f3-1b3385557704");
  const purl = Astro2.url.origin + "/previews/causes.png";
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Property Crowdfunding for Good Causes | The Hub | UOWN", "classes": "", "description": "Discover how UOWN supports various causes through crowdfunding. Make a difference with your money.", "purl": purl, "data-astro-cid-f6pfmuu3": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main data-astro-cid-f6pfmuu3> <section class="hero bg-no-repeat bg-cover bg-salmon-200 flex flex-col justify-center items-center text-center text-white lg:px-0 px-11" data-astro-cid-f6pfmuu3> <p class="max-w-4xl lg:text-7xl md:text-6xl text-5xl tracking-normal font-extrabold pb-4" data-astro-cid-f6pfmuu3>
Causes Hub
</p> <p class="max-w-2xl md:text-2xl text-xl tracking-normal font-regular pb-12" data-astro-cid-f6pfmuu3>
The Hub is our knowledge center featuring useful and
				inspirational articles.
</p> </section> <section class="flex flex-wrap gap-x-7 justify-start md:pt-12 lg:pt-24 lg:px-32 md:px-16 px-8 pb-24 pt-8" data-astro-cid-f6pfmuu3> ${categoryPosts.map((post) => renderTemplate`${renderComponent($$result2, "PostTile", $$PostTile, { "readingTime": getReadingTime("post", post.slug.current), "postHref": getFullUrl(Astro2.url.origin, post.fullSlug), "imgSrc": urlForImage(post.thumbnail).width(350).height(200).url(), "title": post.title, "color": "border-salmon-300", "data-astro-cid-f6pfmuu3": true })}`)} </section> ${renderComponent($$result2, "StartJourneyCTA", $$StartJourneyCTA, { "colorClass": "bg-salmon-gradient", "data-astro-cid-f6pfmuu3": true })} </main> ` })} `;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/category/causes.astro", void 0);

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/category/causes.astro";
const $$url = "/category/causes";

const causes = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
    __proto__: null,
    default: $$Causes,
    file: $$file,
    url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

export { $$PostTile as $, causes as c };

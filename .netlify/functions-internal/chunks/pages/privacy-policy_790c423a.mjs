import { $ as $$Layout } from './404_1f20d35e.mjs';
import { _ as __astro_tag_component__, F as Fragment, l as createVNode } from '../astro_ca9e373b.mjs';
import { c as $$Image } from './_slug__d49fd2f5.mjs';
import { $ as $$PolicyLayout, a as $$PolicyHeadings, b as $$PoliciesPara } from './cookie-policy_cdb223cb.mjs';
import 'clsx';
/* empty css                            */import '@sanity/client';
import 'react/jsx-runtime';
import 'motion/react';
import 'react';
import '@headlessui/react';
import '@heroicons/react/24/outline';
import '@heroicons/react/20/solid';
/* empty css                            */import 'html-escaper';
import '@astrojs/internal-helpers/path';
/* empty css                            */import '@portabletext/react';
import '../astro-assets-services_967ef4fc.mjs';
/* empty css                                 */import '@sanity/image-url';
import 'groq';
/* empty css                                 *//* empty css                                   */
const __0____assets_images_legal_privacy_policy_1_png__ = {"src":"/_astro/privacy-policy-1.0d5e7ee7.png","width":1256,"height":502,"format":"png"};

const __1____assets_images_legal_privacy_policy_2_png__ = {"src":"/_astro/privacy-policy-2.cbb00720.png","width":1264,"height":1186,"format":"png"};

const frontmatter = {
  "title": "Privacy Policy",
  "description": "The UOWN site has a privacy policy that covers your use the website. Click to read and find out more."
};
function getHeadings() {
  return [];
}
const __usesAstroImage = true;
function _createMdxContent(props) {
  const _components = Object.assign({
      a: "a",
      p: "p",
      "astro-image": "astro-image"
    }, props.components),
    _component0 = _components["astro-image"];
  return createVNode($$Layout, {
    title: "Privacy Policy | Your Data Protection | UOWN",
    classes: "bg-gray",
    description: "Read UOWN's privacy policy to understand how we protect your personal data and information.",
    purl: "",
    children: createVNode($$PolicyLayout, {
      heading: "Privacy Policy",
      subheading: "Please read this terms & conditions of website use carefully before using this website.",
      children: [createVNode($$PolicyHeadings, {
        text: "BACKGROUND:"
      }), createVNode($$PoliciesPara, {
        text: "UOWN understands that your privacy is important to you and that you care about how your personal data is used and shared online. We respect, and value the privacy of everyone who visits this website, www.UOWN.co (\u201COur Site\u201D) and will only collect and use personal data in ways that are described here, and in a manner that is consistent with Our obligations and your rights under the law."
      }), createVNode($$PoliciesPara, {
        text: "Please read this Privacy Policy carefully and ensure that you understand it. Your acceptance of Our Privacy Policy is deemed to occur upon your first use of Our Site and/or You will be required to read and accept this Privacy Policy when signing up for an Account. If you do not accept and agree with this Privacy Policy, you must stop using Our Site immediately."
      }), createVNode($$PolicyHeadings, {
        text: "1. Definitions and Interpretation"
      }), createVNode($$PoliciesPara, {
        text: "In this Policy, the following terms shall have the following meanings:"
      }), createVNode($$PoliciesPara, {
        text: " \u201CAccount\u201D "
      }), createVNode($$PoliciesPara, {
        text: "means an account required to  access and/or use certain areas and features of Our Site;"
      }), createVNode($$PoliciesPara, {
        text: " \u201CCookie\u201D "
      }), createVNode($$PoliciesPara, {
        text: "means a small text file  placed on your computer or device by Our Site when you visit certain parts of  Our Site and/or when you use certain features of Our Site. Details of the  Cookies used by Our Site are set out in section 13, below;"
      }), createVNode($$PoliciesPara, {
        text: " \u201CCookie Law\u201D "
      }), createVNode($$PoliciesPara, {
        text: "means the relevant parts of  the Privacy and Electronic Communications (EC Directive) Regulations 2003;"
      }), createVNode($$PoliciesPara, {
        text: "\u201C personal data \u201D"
      }), createVNode($$PoliciesPara, {
        text: "means any and all data that  relates to an identifiable person who can be directly or indirectly  identified from that data. In this case, it means personal data that you give  to Us via  Our Site. This definition shall, where applicable, incorporate the  definitions provided in the EU Regulation 2016/679 \u2013 the General Data  Protection Regulation (\u201CGDPR\u201D); and"
      }), createVNode($$PoliciesPara, {
        text: " \u201CWe/Us/Our\u201D "
      }), createVNode($$PoliciesPara, {
        text: "means U OWN Exchange Limited,  a limited company registered in England under company number 10001410, whose  registered address is UOWN, 3rd Floor, Northgate, 118 North Street, Leeds, LS2 7PN."
      }), createVNode($$PolicyHeadings, {
        text: "2. Information About Us"
      }), createVNode($$PoliciesPara, {
        text: "2.1         Our Site is owned and operated by U OWN Exchange Limited, a limited company registered in England under company number 10001410, whose registered address is UOWN, 3rd Floor, Northgate, 118 North Street, Leeds, LS2 7PN."
      }), createVNode($$PoliciesPara, {
        text: "2.2         Our Data Protection Manager is Haaris Ahmed, and can be contacted by email at <a class='link-underlined' href='mailto:<EMAIL>'><EMAIL></a>, by telephone on <a href='tel:02035045678'>02035045678</a>, or by post at UOWN, 3rd Floor, Northgate, 118 North Street, Leeds, LS2 7PN."
      }), createVNode($$PoliciesPara, {
        text: "2.3         We are a member of UK Crowdfunding Association (UKCFA)."
      }), createVNode($$PolicyHeadings, {
        text: "3. What Does This Policy Cover?"
      }), createVNode($$PoliciesPara, {
        text: "This Privacy Policy applies only to your use of Our Site. Our Site may contain links to other websites. Please note that We have no control over how your data is collected, stored, or used by other websites and We advise you to check the privacy policies of any such websites before providing any data to them."
      }), createVNode($$PoliciesPara, {
        text: " 3. Your Rights"
      }), createVNode($$PoliciesPara, {
        text: " 3.1 As a data subject, you have the following rights under the GDPR, which this Policy and Our use of personal data have been designed to uphold:"
      }), createVNode($$PoliciesPara, {
        text: " 3.1.1 The right to be informed about Our collection and use of personal data;"
      }), createVNode($$PoliciesPara, {
        text: " 3.1.2 The right of access to the personal data We hold about you (see section 12);"
      }), createVNode($$PoliciesPara, {
        text: " 3.1.3 The right to recertification if any personal data We hold about you is inaccurate or incomplete (please contact Us using the details in section 14);"
      }), createVNode($$PoliciesPara, {
        text: " 3.1.4 The right to be forgotten \u2013 i.e. the right to ask Us to delete any personal data We hold about you (We only hold your personal data for a limited time, as explained in section 6 but if you would like Us to delete it sooner, please contact Us using the details in section 14);"
      }), createVNode($$PoliciesPara, {
        text: " 3.1.5 The right to restrict (i.e. prevent) the processing of your personal data;"
      }), createVNode($$PoliciesPara, {
        text: " 3.1.6 The right to data portability (obtaining a copy of your personal data to re-use with another service or organisation);"
      }), createVNode($$PoliciesPara, {
        text: " 3.1.7 The right to object to Us using your personal data for particular purposes; and"
      }), createVNode($$PoliciesPara, {
        text: " 3.1.8  Rights with respect to automated decision making and profiling."
      }), createVNode($$PoliciesPara, {
        text: " 3.2  If you have any cause for complaint about Our use of your personal data, please contact Us using the details provided in section 14 and We will do Our best to solve the problem for you. If We are unable to help, you also have the right to lodge a complaint with the UK\u2019s supervisory authority, the Information Commissioner\u2019s Office."
      }), createVNode($$PoliciesPara, {
        text: " 3.3         For further information about your rights, please contact the Information Commissioner\u2019s Office or your local Citizens Advice Bureau."
      }), createVNode($$PolicyHeadings, {
        text: " 4. What Data Do We Collect?"
      }), createVNode($$PoliciesPara, {
        text: "Depending upon your use of Our Site, We may collect some or all of the following personal and non-personal data (please also see section 13 on Our use of Cookies and similar technologies and Our Cookie Policy www.uown.co/cookie-policy):"
      }), createVNode($$PoliciesPara, {
        text: " 4.1         name;"
      }), createVNode($$PoliciesPara, {
        text: " 4.2         date of birth;"
      }), createVNode($$PoliciesPara, {
        text: " 4.3         gender;"
      }), createVNode($$PoliciesPara, {
        text: " 4.4         business/company name"
      }), createVNode($$PoliciesPara, {
        text: " 4.5         job title;"
      }), createVNode($$PoliciesPara, {
        text: " 4.6         profession;"
      }), createVNode($$PoliciesPara, {
        text: " 4.7         contact information such as email addresses and telephone numbers;"
      }), createVNode($$PoliciesPara, {
        text: " 4.8         demographic information such as post code, nationality, preferences, and interests;"
      }), createVNode($$PoliciesPara, {
        text: " 4.9         financial information such as bank details, or credit / debit card numbers;"
      }), createVNode($$PoliciesPara, {
        text: " 4.10      IP address;"
      }), createVNode($$PoliciesPara, {
        text: " 4.11      web browser type and version;"
      }), createVNode($$PoliciesPara, {
        text: " 4.12      operating system;"
      }), createVNode($$PoliciesPara, {
        text: " 4.13      a list of URLs starting with a referring site, your activity on Our Site, and the site you exit to; an/or"
      }), createVNode($$PoliciesPara, {
        text: " 4.14      investor profiling information such as your self-categorised investor type or the amount you are looking to invest."
      }), createVNode($$PoliciesPara, {
        text: " 4.15      Site use data such as last login, number of logins, or investments you have looked at or made."
      }), createVNode($$PolicyHeadings, {
        text: " 5. How Do We Use Your Data?"
      }), createVNode($$PoliciesPara, {
        text: " 5.1         All personal data is processed and stored securely, for no longer than is necessary in light of the reason(s) for which it was first collected. We will comply with Our obligations and safeguard your rights under the GDPRat all times. For more details on security see section 7, below."
      }), createVNode($$PoliciesPara, {
        text: " 5.2         Our use of your personal data will always have a lawful basis, either because it is necessary for Our performance of a contract with you, because you have consented to Our use of your personal data (e.g. by subscribing to emails), or because it is in Our legitimate interests. Specifically, We may use your data for the following purposes:"
      }), createVNode($$PoliciesPara, {
        text: " 5.2.1   Providing and managing your Account;"
      }), createVNode($$PoliciesPara, {
        text: " 5.2.2   Providing and managing your access to Our Site;"
      }), createVNode($$PoliciesPara, {
        text: " 5.2.3   Personalising and tailoring your experience on Our Site;"
      }), createVNode($$PoliciesPara, {
        text: " 5.2.4   Supplying Our services to you (please note that We require your personal data in order to enter into a contract with you);"
      }), createVNode($$PoliciesPara, {
        text: " 5.2.5   Personalising and tailoring Our services for you;"
      }), createVNode($$PoliciesPara, {
        text: " 5.2.6   Replying to emails from you;"
      }), createVNode($$PoliciesPara, {
        text: " 5.2.7   Supplying you with emails that you have opted into (you may unsubscribe or opt-out at any time by clicking the unsubscribe link at in the footer of all marketing emails;"
      }), createVNode($$PoliciesPara, {
        text: " 5.2.8   Market research;"
      }), createVNode($$PoliciesPara, {
        text: " 5.2.9   Analysing your use of Our Site and gathering feedback to enable Us to continually improve Our Site and your user experience;"
      }), createVNode($$PoliciesPara, {
        text: " 5.2.10 Offering you rewards and offers based on your use of the site and"
      }), createVNode($$PoliciesPara, {
        text: " 5.2.11 Logs of conversations via email, text, phone, or post that relate to investing in the platform."
      }), createVNode($$PoliciesPara, {
        text: " 5.3         With your permission and/or where permitted by law, We may also use your data for marketing purposes which may include contacting you by email or telephone ortext message orpost with information, news and offers on Our services. We will not, however, send you any unsolicited marketing or spam and will take all reasonable steps to ensure that We fully protect your rights and comply with Our obligations under the GDPR and the Privacy and Electronic Communications (EC Directive) Regulations 2003."
      }), createVNode($$PoliciesPara, {
        text: " 5.4         Third parties (including MangoPay, Facebook, and Google) whose content appears on Our Site may use third party Cookies, as detailed below in section  12. Please refer to section 13 for more information on controlling Cookies. Please note that We do not control the activities of such third parties, nor the data they collect and use and advise you to check the privacy policies of any such third parties."
      }), createVNode($$PoliciesPara, {
        text: " 5.5         You have the right to withdraw your consent to Us using your personal data at any time, and to request that We delete it. You can make this request <NAME_EMAIL>."
      }), createVNode($$PoliciesPara, {
        text: " 5.6         We do not keep your personal data for any longer than is necessary in light of the reason(s) for which it was first collected. Data will therefore be retained for the following periods (or its retention will be determined on the following bases):"
      }), createVNode($$PoliciesPara, {
        text: " 5. 5.1   If you invest your personal data, logs of your investment activity, and logs of any communications will be stored for a minimum of five years after you stop using the platform in case it is needed to help fight financial crime;"
      }), createVNode($$PoliciesPara, {
        text: " 5. 5.2   Your personal data such as your email, personal details, and contact information will be stored until or unless you decide to close your UOWN account or you ask for it to be removed or changed."
      }), createVNode($$PolicyHeadings, {
        text: " 6. How and Where Do We Store Your Data?"
      }), createVNode($$PoliciesPara, {
        text: " 6.1         We only keep your personal data for as long as We need to in order to use it as described above in section 6, and/or for as long as We have your permission to keep it."
      }), createVNode($$PoliciesPara, {
        text: " 6.2         Your data will only be stored within the European Economic Area (\u201Cthe EEA\u201D) (The EEA consists of all EU member states, plus Norway, Iceland, and Liechtenstein)."
      }), createVNode($$PoliciesPara, {
        text: " 6.3         Data security is very important to Us, and to protect your data We have taken suitable measures to safeguard and secure data collected through Our Site."
      }), createVNode($$PoliciesPara, {
        text: " 6.4         Steps We take to secure and protect your data include:"
      }), createVNode($$PoliciesPara, {
        text: " 6. 4.1   We encrypt all data that is held on our website servers."
      }), createVNode($$PoliciesPara, {
        text: " 6. 4.2   We take cyber security very seriously and we have had the site stress tested by cyber security specialists \u2013 the NCC Group. We will continuously review our cyber security to ensure it is up to date."
      }), createVNode($$PoliciesPara, {
        text: " 6. 4.3   Access to website customer data is restricted to the directors of UOWN and is secured with 2-Factor-Authentication."
      }), createVNode($$PoliciesPara, {
        text: " 6. 4.4   Any computer or email that holds sensitive customer data is secured with a strong password and the passwords are updated regularly."
      }), createVNode($$PolicyHeadings, {
        text: " 7. Do We Share Your Data?"
      }), createVNode($$PoliciesPara, {
        text: " 7.1         We may share your data with other companies in Our group to provide you a seamless service. This includes U OWN Nominee Limited (Company Number 10325139), and CROWDR Limited (Company Number 10172722)."
      }), createVNode($$PoliciesPara, {
        text: " 7.2         We may sometimes contract with third parties to supply services to you on Our behalf. These may include payment processing, search engine facilities, advertising, and marketing. In some cases, the third parties may require access to some or all of your data. Where any of your data is required for such a purpose, We will take all reasonable steps to ensure that your data will be handled safely, securely, and in accordance with your rights, Our obligations, and the obligations of the third party under the law."
      }), createVNode($$PoliciesPara, {
        text: " 7.3         We may compile statistics about the use of Our Site including data on traffic, usage patterns, user numbers, sales, and other information. All such data will be anonymised and will not include any personally identifying data, or any anonymised data that can be combined with other data and used to identify you. We may from time to time share such data with third parties such as prospective investors, affiliates, partners, and advertisers. Data will only be shared and used within the bounds of the law."
      }), createVNode($$PoliciesPara, {
        text: " 7.4         We may sometimes use third party data processors that are located outside of the European Economic Area (\u201Cthe EEA\u201D) (The EEA consists of all EU member states, plus Norway, Iceland, and Liechtenstein). Where We transfer any personal data outside the EEA, We will take all reasonable steps to ensure that your data is treated as safely and securely as it would be within the UK and under GDPR including:"
      }), createVNode($$PoliciesPara, {
        text: " 7. 4.1   MangoPay (held within EEA);"
      }), createVNode($$PoliciesPara, {
        text: " 7. 4.2   Dubit (held within EEA);"
      }), createVNode($$PoliciesPara, {
        text: " 7. 4.3   Google Analytics (held within EEA);"
      }), createVNode($$PoliciesPara, {
        text: " 7. 4.4   Amazon Web Services (held within EEA); and"
      }), createVNode($$PoliciesPara, {
        text: " 7. 4.5   Webflow (can transfer Data outside of the EEA) ."
      }), createVNode($$PoliciesPara, {
        text: " 7. 4.6   MailChimp (can transfer Data outside of the EEA)."
      }), createVNode($$PoliciesPara, {
        text: " 7.5         In certain circumstances, We may be legally required to share certain data held by Us, which may include your personal data, for example, where We are involved in legal proceedings, where We are complying with legal requirements, a court order, or a governmental authority."
      }), createVNode($$PolicyHeadings, {
        text: " 8. What Happens If Our Business Changes Hands?"
      }), createVNode($$PoliciesPara, {
        text: " 8.1         We may, from time to time, expand or reduce Our business and this may involve the sale and/or the transfer of control of all or part of Our business. Any personal data that you have provided will, where it is relevant to any part of Our business that is being transferred, be transferred along with that part and the new owner or newly controlling party will, under the terms of this Privacy Policy, be permitted to use that data only for the same purposes for which it was originally collected by Us."
      }), createVNode($$PoliciesPara, {
        text: " 8.2         In the event that any of your data is to be transferred in such a manner, you will be contacted in advance and informed of the changes. When contacted you will not be given the choice to have your data deleted or withheld from the new owner or controller."
      }), createVNode($$PolicyHeadings, {
        text: " 9. How Can You Control Your Data?"
      }), createVNode($$PoliciesPara, {
        text: " 9.1      In addition to your rights under the GDPR, set out in section 4, when you submit personal data via Our Site, you may be given options to restrict Our use of your data. In particular, We aim to give you strong controls on Our use of your data for direct marketing purposes (including the ability to opt-out of receiving emails from Us which you may do by unsubscribing using the links provided in Our emailsandat the point of providing your detailsand by managing your Account)."
      }), createVNode($$PoliciesPara, {
        text: " 9.2      You may also wish to sign up to one or more of the preference services operating in the UK: The Telephone Preference Service (\u201Cthe TPS\u201D), the Corporate Telephone Preference Service (\u201Cthe CTPS\u201D), and the Mailing Preference Service (\u201Cthe MPS\u201D). These may help to prevent you receiving unsolicited marketing. Please note, however, that these services will not prevent you from receiving marketing communications that you have consented to receiving."
      }), createVNode($$PolicyHeadings, {
        text: " 10. Your Right to Withhold Information"
      }), createVNode($$PoliciesPara, {
        text: " 10.1      You may access certain areas of Our Site without providing any data at all. However, to use all features and functions available on Our Site you may be required to submit or allow for the collection of certain data."
      }), createVNode($$PoliciesPara, {
        text: " 10.2      You may restrict Our use of Cookies. For more information, see section  12."
      }), createVNode($$PolicyHeadings, {
        text: " 11. How Can You Access Your Data?"
      }), createVNode("p", {
        class: "text-sm tracking-widest md:text-base md:tracking-wider lg:text-xl lg:tracking-normal font-regular",
        children: [createVNode("span", {
          children: ["You have the right to ask for a copy of any of your personal data held by Us (where such data is held.Under the GDPR, no fee is payable and We will provide any and all information in response to your request free of charge. Please contact Us for more details at ", createVNode(_components.a, {
            href: "mailto:<EMAIL>",
            children: "<EMAIL>"
          }), ", or using the contact details below in section 1 3. Alternatively, please refer to Our Data Protection Policy, a copy of which can be obtained from our Data Protection Manager at "]
        }), "\n", createVNode("a", {
          style: "color:#71E5BD;",
          href: "mailto:<EMAIL>.",
          target: "_blank",
          rel: "noreferrer",
          children: [createVNode(_components.a, {
            href: "mailto:<EMAIL>",
            children: "<EMAIL>"
          }), "."]
        })]
      }), createVNode($$PolicyHeadings, {
        text: " 12. Our Use of Cookies"
      }), createVNode($$PoliciesPara, {
        text: " 12.1      Our Site may place and access certain first party Cookies on your computer or device. First party Cookies are those placed directly by Us and are used only by Us. We use Cookies to facilitate and improve your experience of Our Site and to provide and improve Our services. We have carefully chosen these Cookies and have taken steps to ensure that your privacy and personal data is protected and respected at all times."
      }), createVNode($$PoliciesPara, {
        text: " 12.2      By using Our Site you may also receive certain third party Cookies on your computer or device. Third party Cookies are those placed by websites, services, and/or parties other than Us. Third party Cookies are used on Our Site for advertising or analytics. For more details, please refer to section 6, above, and to section  12.6 below. These Cookies are not integral to the functioning of Our Site and your use and experience of Our Site will not be impaired by refusing consent to them."
      }), createVNode($$PoliciesPara, {
        text: " 12.3      All Cookies used by and on Our Site are used in accordance with current Cookie Law."
      }), createVNode($$PoliciesPara, {
        text: " 12.4      Before Cookies are placed on your computer or device, you will be shown a Cookie Warning pop-up requesting your consent to set those Cookies. By giving your consent to the placing of Cookies you are enabling Us to provide the best possible experience and service to you. You may, if you wish, deny consent to the placing of Cookies; however certain features of Our Site may not function fully or as intended. You will be given the opportunity to allow only first party Cookies and block third party Cookies."
      }), createVNode($$PoliciesPara, {
        text: " 12.5      Certain features of Our Site depend on Cookies to function. Cookie Law deems these Cookies to be \u201Cstrictly necessary\u201D. These Cookies are shown below in section  12. 5. Your consent will not be sought to place these Cookies, but it is still important that you are aware of them. You may still block these Cookies by changing your internet browser\u2019s settings as detailed below in section  12.10, but please be aware that Our Site may not work properly if you do so. We have taken great care to ensure that your privacy is not at risk by allowing them."
      }), createVNode($$PoliciesPara, {
        text: " 12.6      The following first party Cookies may be placed on your computer or device:"
      }), createVNode(_components.p, {
        children: createVNode(_component0, {
          src: __0____assets_images_legal_privacy_policy_1_png__,
          alt: ""
        })
      }), createVNode($$PoliciesPara, {
        text: "and the following third party Cookies may be placed on your computer or device:"
      }), createVNode($$PoliciesPara, {
        text: " 12.7      Our Site uses analytics services provided by Google Analytics. Website analytics refers to a set of tools used to collect and analyse anonymous usage information, enabling Us to better understand how Our Site is used. This, in turn, enables Us to improve Our Site and the services offered through it. You do not have to allow Us to use these Cookies, however whilst Our use of them does not pose any risk to your privacy or your safe use of Our Site, it does enable Us to continually improve Our Site, making it a better and more useful experience for you."
      }), createVNode($$PoliciesPara, {
        text: " 12.8      The analytics service(s) used by Our Site use(s) Cookies to gather the required information."
      }), createVNode($$PoliciesPara, {
        text: " 12.9      The analytics service(s) used by Our Site use(s) the following Cookies:"
      }), createVNode(_components.p, {
        children: createVNode(_component0, {
          src: __1____assets_images_legal_privacy_policy_2_png__,
          alt: ""
        })
      }), createVNode($$PoliciesPara, {
        text: " 12.10  In addition to the controls that We provide, you can choose to enable or disable Cookies in your internet browser. Most internet browsers also enable you to choose whether you wish to disable all cookies or only third party Cookies. By default, most internet browsers accept Cookies but this can be changed. For further details, please consult the help menu in your internet browser or the documentation that came with your device."
      }), createVNode($$PoliciesPara, {
        text: " 12.11  You can choose to delete Cookies on your computer or device at any time, however you may lose any information that enables you to access Our Site more quickly and efficiently including, but not limited to, login and personalisation settings."
      }), createVNode($$PoliciesPara, {
        text: " 12.12  It is recommended that you keep your internet browser and operating system up-to-date and that you consult the help and guidance provided by the developer of your internet browser and manufacturer of your computer or device if you are unsure about adjusting your privacy settings."
      }), createVNode($$PolicyHeadings, {
        text: "13. Contacting Us"
      }), createVNode("div", {
        children: createVNode($$PoliciesPara, {
          text: "13.1      If you have any questions about Our Site or this Privacy Policy, please contact Us by email at <a class='link-underlined' href='mailto:<EMAIL>'><EMAIL></a> by telephone on <a href='tel:02035045678'>02035045678</a>, or by post at UOWN, 3rd Floor, Northgate, 118 North Street, Leeds, LS2 7PN. Please ensure that your query is clear, particularly if it is a request for information about the data We hold about you (as under section 12, above)."
        })
      }), createVNode("p", {
        class: "text-sm tracking-widest md:text-base md:tracking-wider lg:text-xl lg:tracking-normal font-regular",
        children: [createVNode("span", {
          children: "13.1      If you have any questions about Our Site or this Privacy Policy, please contact Us by email at "
        }), "\n", createVNode("a", {
          style: "color:#71E5BD; text-decoration:underline;",
          href: "mailto:<EMAIL>.",
          target: "_blank",
          rel: "noreferrer",
          children: [createVNode(_components.a, {
            href: "mailto:<EMAIL>",
            children: "<EMAIL>"
          }), " "]
        }), "\n", createVNode("span", {
          children: "by telephone on "
        }), createVNode("a", {
          style: "color:#71E5BD; text-decoration:underline;",
          href: "tel:02035045678",
          children: "02035045678"
        })]
      }), createVNode($$PolicyHeadings, {
        text: "14. Changes to Our Privacy Policy"
      }), createVNode($$PoliciesPara, {
        text: "We may change this Privacy Policy from time to time (for example, if the law changes). Any changes will be immediately posted on Our Site and you will be deemed to have accepted the terms of the Privacy Policy on your first use of Our Site following the alterations. We recommend that you check this page regularly to keep up-to-date."
      })]
    })
  });
}
function MDXContent(props = {}) {
  const {
    wrapper: MDXLayout
  } = props.components || {};
  return MDXLayout ? createVNode(MDXLayout, {
    ...props,
    children: createVNode(_createMdxContent, {
      ...props
    })
  }) : _createMdxContent(props);
}

__astro_tag_component__(getHeadings, "astro:jsx");
__astro_tag_component__(MDXContent, "astro:jsx");
const url = "/privacy-policy";
const file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/privacy-policy.mdx";
const Content = (props = {}) => MDXContent({
											...props,
											components: { Fragment, ...props.components, "astro-image":  props.components?.img ?? $$Image },
										});
Content[Symbol.for('mdx-component')] = true;
Content[Symbol.for('astro.needsHeadRendering')] = !Boolean(frontmatter.layout);
Content.moduleId = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/privacy-policy.mdx";

export { Content, __usesAstroImage, Content as default, file, frontmatter, getHeadings, url };

const post = async ({ request }) => {
  const data = await request.formData();
  const r = data.get("number");
  var recipient = "";
  if (r) {
    recipient = r.replace(/\s/g, "");
  }
  const response = await fetch("https://api.d7networks.com/whatsapp/v2/send", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": `Bearer ${"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJhdXRoLWJhY2tlbmQ6YXBwIiwic3ViIjoiYzcyMzRiMDUtYjI5ZC00OWM3LWE5YWEtNTEwZjdmOWU3NDVjIn0.2sbLFAYc2rW-bLAaynEGOssVaCsdVxTCqL_KnV2DbUM"}`
    },
    body: JSON.stringify({
      "messages": [
        {
          "originator": "+442035045678",
          "content": {
            "message_type": "TEMPLATE",
            "template": {
              "template_id": "roomzzz_image_brochure",
              "language": "en",
              "media": {
                "media_type": "image",
                "media_url": "https://d8nxin2dl65ja.cloudfront.net/roomzzz-glasgow/Roomzzz_Glasgow.png"
              }
            }
          },
          "recipients": [
            {
              "recipient": recipient,
              "recipient_type": "individual"
            }
          ]
        }
      ]
    })
  });
  return response;
};

export { post };

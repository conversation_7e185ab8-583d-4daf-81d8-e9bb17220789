import { $ as $$Layout } from './404_1f20d35e.mjs';
import { e as createAstro, f as createComponent, r as renderTemplate, i as renderComponent, m as maybeRenderHead } from '../astro_ca9e373b.mjs';
import 'clsx';
import { c as $$Image } from './_slug__d49fd2f5.mjs';
import { jsx, jsxs } from 'react/jsx-runtime';
import { useState } from 'react';
import { Transition, Dialog } from '@headlessui/react';
import { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import 'react-dom';
import { createRoot } from 'react-dom/client';
/* empty css                             */
function DialogOverlay({ heading, content, button, classes, id = "", type = "success" }) {
  const [open, setOpen] = useState(true);
  return /* @__PURE__ */ jsx(Transition, { show: open, children: /* @__PURE__ */ jsxs(Dialog, { id, className: "relative z-[41]", onClose: setOpen, children: [
    /* @__PURE__ */ jsx(
      Transition.Child,
      {
        enter: "ease-out duration-500",
        enterFrom: "opacity-0",
        enterTo: "opacity-100",
        leave: "ease-in duration-300",
        leaveFrom: "opacity-100",
        leaveTo: "opacity-0",
        children: /* @__PURE__ */ jsx("div", { className: "fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" })
      }
    ),
    /* @__PURE__ */ jsx("div", { className: "fixed inset-0 z-10 w-screen overflow-y-auto", children: /* @__PURE__ */ jsx("div", { className: "flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0", children: /* @__PURE__ */ jsx(
      Transition.Child,
      {
        enter: "ease-out duration-300",
        enterFrom: "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95",
        enterTo: "opacity-100 translate-y-0 sm:scale-100",
        leave: "ease-in duration-200",
        leaveFrom: "opacity-100 translate-y-0 sm:scale-100",
        leaveTo: "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95",
        children: /* @__PURE__ */ jsxs(Dialog.Panel, { className: "relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6", children: [
          /* @__PURE__ */ jsxs("div", { children: [
            type === "success" ? /* @__PURE__ */ jsx("div", { className: "mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100", children: /* @__PURE__ */ jsx(CheckIcon, { className: "h-6 w-6 text-green-600", "aria-hidden": "true" }) }) : /* @__PURE__ */ jsx("div", { className: "mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100", children: /* @__PURE__ */ jsx(XMarkIcon, { className: "h-6 w-6 text-red-600", "aria-hidden": "true" }) }),
            /* @__PURE__ */ jsxs("div", { className: "mt-3 text-center sm:mt-5", children: [
              /* @__PURE__ */ jsx(Dialog.Title, { as: "h3", className: "text-base font-semibold leading-6 text-gray-900", children: heading }),
              /* @__PURE__ */ jsx("div", { className: "mt-2", children: /* @__PURE__ */ jsx("p", { className: "text-sm text-gray-500", children: content }) })
            ] })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "mt-5 sm:mt-6", children: /* @__PURE__ */ jsx(
            "button",
            {
              type: "button",
              className: "rounded-cxl inline-flex w-full justify-center items-center px-3 py-2 text-sm font-semibold shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 " + classes,
              onClick: () => {
                setOpen(false);
                if (window.location.href.includes("?")) {
                  window.location.href = location.protocol + "//" + location.host + location.pathname;
                }
              },
              children: button
            }
          ) })
        ] })
      }
    ) }) })
  ] }) });
}

const LoadingState = {"src":"/_astro/three-dots.1f123b97.svg","width":120,"height":30,"format":"svg"};

const CheckMark = {"src":"/_astro/check-1.a6828a15.svg","width":512,"height":512,"format":"svg"};

const Cross = {"src":"/_astro/times-solid.258568e0.svg","width":352,"height":512,"format":"svg"};

function ContactForm() {
  const [formData, setFormData] = useState({ name: "", email: "", message: "" });
  const delay = (ms) => new Promise(
    (resolve) => setTimeout(resolve, ms)
  );
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevFormData) => ({ ...prevFormData, [name]: value }));
  };
  const submit = async (e) => {
    function showErrorDialog() {
      var dialogDiv = document.getElementById("dialog-container");
      if (dialogDiv) {
        const root = createRoot(dialogDiv);
        root.render(/* @__PURE__ */ jsx(
          DialogOverlay,
          {
            heading: "Submission failed",
            content: "Oops... Something has gone wrong. Please try again.",
            button: "Close",
            classes: "bg-salmon-200 text-black hover:bg-salmon-300 focus-visible:outline-salmon-300",
            type: "error"
          }
        ));
      }
    }
    function showSuccessDialog() {
      var dialogDiv = document.getElementById("dialog-container");
      if (dialogDiv) {
        const root = createRoot(dialogDiv);
        root.render(/* @__PURE__ */ jsx(
          DialogOverlay,
          {
            heading: "Submission Successful",
            content: "Our team will get back to you soon!",
            button: "Close",
            classes: "bg-mint-300 text-black hover:bg-mint-500 focus-visible:outline-mint-500"
          }
        ));
      }
    }
    e.preventDefault();
    const submitbtn = document.getElementById("submit");
    const loading = document.getElementById("loading");
    if (submitbtn && loading) {
      submitbtn.style.display = "none";
      loading.style.display = "flex";
    }
    const formData2 = new FormData(e.target);
    const response = await fetch("/api/feedback", {
      method: "POST",
      body: formData2
    });
    const data = await response.json();
    if (data.message === "success" && loading && submitbtn) {
      loading.style.display = "none";
      await delay(1e3);
      submitbtn.style.display = "inline";
      showSuccessDialog();
    } else if (data.message === "fail" && loading && submitbtn) {
      loading.style.display = "none";
      await delay(1e3);
      submitbtn.style.display = "inline";
      showErrorDialog();
    }
  };
  return /* @__PURE__ */ jsxs("form", { method: "POST", onSubmit: submit, id: "contact-form", className: "form max-w-6xl mx-auto grid gap-x-6 gap-y-8 grid-cols-1 lg:grid-cols-2 mt-16 px-8 lg:px-8 md:px-28 z-10 relative", children: [
    /* @__PURE__ */ jsx(
      "input",
      {
        type: "text",
        id: "name",
        name: "name",
        value: formData.name,
        onChange: handleChange,
        placeholder: "Name",
        required: true,
        className: "flex flex-col w-full text-lg py-3.5 px-8 border border-gray-300 placeholder-gray-500 shadow-sm focus:border-mint-200 focus:ring-mint-300 text-black-s1 rounded-full"
      }
    ),
    /* @__PURE__ */ jsx(
      "input",
      {
        type: "email",
        placeholder: "Email",
        name: "email",
        value: formData.email,
        onChange: handleChange,
        required: true,
        id: "email",
        className: "flex flex-col w-full z-2 text-lg py-3.5 px-8 border border-gray-300 placeholder-gray-500 shadow-sm focus:border-mint-200 focus:ring-mint-300 text-black-s1 rounded-full"
      }
    ),
    /* @__PURE__ */ jsx(
      "textarea",
      {
        cols: 20,
        rows: 5,
        placeholder: "Enter message",
        name: "message",
        value: formData.message,
        onChange: handleChange,
        required: true,
        id: "message",
        className: "flex flex-col w-full h-[26rem] z-2 message-contact-form rounded-cmd text-lg py-3.5 px-8 border border-gray-300 placeholder-gray-500 shadow-sm focus:border-mint-200 focus:ring-mint-300 text-black-s1"
      }
    ),
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col w-full submit-contact-form items-center", children: [
      /* @__PURE__ */ jsxs("button", { type: "submit", className: "btn-black-1 btn-contact-submit rounded-full px-2.5 py-1 text-lg font-bold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2", children: [
        /* @__PURE__ */ jsx("span", { id: "submit", className: "text-submit", children: "Submit" }),
        /* @__PURE__ */ jsx("span", { id: "loading", className: "flex justify-center items-center loading-state hidden", children: /* @__PURE__ */ jsx("img", { src: LoadingState.src, alt: "", className: "absolute w-14" }) }),
        /* @__PURE__ */ jsx("span", { id: "checkmark", className: "flex justify-center items-center checkmark hidden", children: /* @__PURE__ */ jsx("img", { src: CheckMark.src, alt: "", className: "absolute w-6" }) }),
        /* @__PURE__ */ jsx("span", { id: "cross", className: "flex justify-center items-center cross hidden", children: /* @__PURE__ */ jsx("img", { src: Cross.src, alt: "", className: "absolute w-6" }) })
      ] }),
      /* @__PURE__ */ jsx("div", { id: "dialog-container" })
    ] })
  ] });
}

const Cylinder = {"src":"/_astro/img-half-cylinder.d279f373.png","width":466,"height":260,"format":"png"};

const Sphere = {"src":"/_astro/img-large-sphere.64e9ba59.png","width":569,"height":449,"format":"png"};

const Circle = {"src":"/_astro/img-half-circle.1f901c7a.png","width":491,"height":411,"format":"png"};

const $$Astro = createAstro("https://www.uown.co");
const $$Contact = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Contact;
  const purl = Astro2.url.origin + "/previews/contact.png";
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Contact UOWN | Get in Touch with Us | UOWN", "classes": "bg-gray", "description": "Get in touch with UOWN for any queries or support. We're here to help with you.", "purl": purl, "data-astro-cid-uw5kdbxl": true }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="main relative py-28 lg:py-40 overflow-hidden" data-astro-cid-uw5kdbxl> ${renderComponent($$result2, "Image", $$Image, { "src": Cylinder, "alt": "", "class": "top-[4rem] right-[15%] w-20 h-11 absolute lg:w-28 lg:h-16 z-0", "data-astro-cid-uw5kdbxl": true })} ${renderComponent($$result2, "Image", $$Image, { "src": Sphere, "alt": "", "class": "bottom-[5rem] left-[81%] w-24 h-20 absolute xl:w-64 xl:h-52 z-0", "data-astro-cid-uw5kdbxl": true })} ${renderComponent($$result2, "Image", $$Image, { "src": Circle, "alt": "", "class": "top-[24rem] right-[90%] w-28 h-24 absolute rotate-270 lg:w-40 lg:h-32 z-0", "data-astro-cid-uw5kdbxl": true })} <div class="mx-auto max-w-2xl text-center mb-28 w-9/12 text-black-100" data-astro-cid-uw5kdbxl> <h2 class="text-5xl font-bold tracking-normal lg:text-6xl mb-4 z-0" data-astro-cid-uw5kdbxl>
Get in touch!
</h2> <p class="mt-2 text-2xl leading-8" data-astro-cid-uw5kdbxl>
Our team is on hand to answer any questions you may have.
</p> </div> ${renderComponent($$result2, "ContactForm", ContactForm, { "client:load": true, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/ContactForm.tsx", "client:component-export": "default", "data-astro-cid-uw5kdbxl": true })} </main> ` })} `;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/contact.astro", void 0);

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/contact.astro";
const $$url = "/contact";

const contact = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Contact,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

export { DialogOverlay as D, contact as c };

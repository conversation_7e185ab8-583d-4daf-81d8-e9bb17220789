import { a as $$Button, $ as $$Layout } from './404_1f20d35e.mjs';
import { f as createComponent, r as renderTemplate, m as maybeRenderHead, i as renderComponent } from '../astro_ca9e373b.mjs';
import 'clsx';
import { jsx, jsxs, Fragment } from 'react/jsx-runtime';
import { useScroll, useTransform, motion } from 'motion/react';
import { useInView } from 'react-intersection-observer';
import { CheckIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { H as Home$1 } from './demoPage2_2cede3ec.mjs';
import { Accordion, AccordionItem } from '@szhsin/react-accordion';
import { c as $$Image } from './_slug__d49fd2f5.mjs';
import { useState } from 'react';
import { R as Reviews } from './demoPage_fe3d8eb4.mjs';

const Mobile = {"src":"/_astro/uown-mobile.4430f4dd.webp","width":1000,"height":2013,"format":"webp"};

const bg = {"src":"/_astro/hero.df75b47d.png","width":820,"height":410,"format":"png"};

function HeroSlice() {
  const { scrollY } = useScroll();
  const y = useTransform(scrollY, [0, 500], [0, -100]);
  const [ref] = useInView({
    threshold: 0.5,
    triggerOnce: false
  });
  return /* @__PURE__ */ jsx("section", { className: "p-6 md:p-10 max-w-7xl mx-auto", children: /* @__PURE__ */ jsxs("div", { className: "flex flex-col lg:flex-row justify-center items-center gap-x-20 gap-y-10", children: [
    /* @__PURE__ */ jsxs(motion.div, { ref, className: "relative flex justify-center items-center w-full lg:basis-1/2 max-w-[700px] lg:max-w-[553px] h-[600px] order-2 overflow-hidden", children: [
      /* @__PURE__ */ jsx("div", { className: "absolute lg:max-w-[584px] w-full h-full", children: /* @__PURE__ */ jsx("img", { loading: "lazy", src: bg.src, className: "object-cover rounded-clg w-full h-full opacity-60", alt: "Background" }) }),
      /* @__PURE__ */ jsx(
        motion.div,
        {
          style: { y },
          initial: { opacity: 0, y: 0 },
          whileInView: { opacity: 1 },
          transition: { duration: 0.4, ease: "easeIn" },
          className: "relative w-[261px] h-[520px] md:w-[300px] md:h-full py-20",
          children: /* @__PURE__ */ jsx("img", { loading: "lazy", src: Mobile.src, className: "absolute w-full h-full", alt: "Mobile frame" })
        }
      ),
      /* @__PURE__ */ jsx(
        motion.div,
        {
          initial: { opacity: 0, y: 0 },
          whileInView: { opacity: 1 },
          transition: { duration: 0.5, ease: "easeIn" },
          className: "absolute w-[280px] h-[40px] smd:w-[320px] smd:h-[57px] bottom-[20%] md:right-[2%] md:top-[25%]",
          children: /* @__PURE__ */ jsxs("div", { className: "relative flex gap-x-2 items-center rounded-cmd bg-white shadow-stake py-6 px-1 smd:px-3 w-full h-full", children: [
            /* @__PURE__ */ jsxs("svg", { width: "30", height: "15", viewBox: "0 0 48 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", children: [
              /* @__PURE__ */ jsx("path", { d: "M20.3386 14.2694C20.3386 16.8492 19.3162 19.3234 17.4962 21.1476C15.6763 22.9718 13.2079 23.9966 10.6341 23.9966C8.06037 23.9966 5.592 22.9718 3.77206 21.1476C1.95212 19.3234 0.929688 16.8492 0.929688 14.2694L0.929688 0.845967C0.945298 0.626796 1.03922 0.420614 1.19423 0.265242C1.34924 0.10987 1.55494 0.0157264 1.7736 7.91782e-05H5.57065C5.6819 -0.00145697 5.79233 0.0193733 5.89541 0.0613392C5.99849 0.103305 6.09214 0.165555 6.17081 0.244411C6.24948 0.323267 6.31159 0.417129 6.35345 0.520451C6.39532 0.623773 6.4161 0.734458 6.41457 0.845967V14.2694C6.42974 15.3875 6.88492 16.4542 7.68101 17.2375C8.07287 17.6163 8.53537 17.9141 9.04209 18.1137C9.54881 18.3133 10.0898 18.4108 10.6341 18.4007C11.1785 18.4108 11.7195 18.3133 12.2262 18.1137C12.7329 17.9141 13.1954 17.6163 13.5873 17.2375C14.3834 16.4542 14.8386 15.3875 14.8537 14.2694V0.845967C14.8522 0.734556 14.8729 0.623965 14.9147 0.52072C14.9565 0.417475 15.0185 0.323665 15.0971 0.244825C15.1756 0.165984 15.2692 0.10371 15.3721 0.0616755C15.475 0.0196407 15.5854 -0.00130318 15.6965 7.91782e-05H19.4936C19.6048 -0.00145697 19.7152 0.0193733 19.8183 0.0613392C19.9214 0.103305 20.015 0.165555 20.0937 0.244411C20.1724 0.323267 20.2345 0.417129 20.2764 0.520451C20.3182 0.623773 20.339 0.734458 20.3375 0.845967L20.3386 14.2694Z", fill: "#71E5BD" }),
              /* @__PURE__ */ jsx("path", { d: "M38.3749 17.9737C37.374 18.5285 36.2489 18.8195 35.1052 18.8195C33.9614 18.8195 32.8363 18.5285 31.8354 17.9737C30.7773 17.392 29.8955 16.5349 29.2829 15.4928C28.6704 14.4506 28.3498 13.262 28.355 12.0525C28.355 10.2299 29.0773 8.48193 30.3631 7.19315C31.6488 5.90438 33.3927 5.18035 35.2111 5.18035C37.0294 5.18035 38.7733 5.90438 40.0591 7.19315C41.3448 8.48193 42.0672 10.2299 42.0672 12.0525C42.0093 13.2738 41.6394 14.4598 40.9928 15.4966C40.3463 16.5334 39.4448 17.3864 38.3749 17.9737ZM35.1052 6.61337e-06C32.7477 6.61337e-06 30.4431 0.700712 28.483 2.01351C26.5228 3.32632 24.995 5.19225 24.0929 7.37536C23.1907 9.55847 22.9547 11.9607 23.4146 14.2783C23.8745 16.5959 25.0097 18.7247 26.6767 20.3956C28.3437 22.0665 30.4676 23.2043 32.7798 23.6653C35.0919 24.1263 37.4886 23.8897 39.6666 22.9855C41.8446 22.0812 43.7062 20.5498 45.0159 18.5851C46.3257 16.6204 47.0248 14.3104 47.0248 11.9475C47.0264 10.378 46.7192 8.82369 46.1208 7.37341C45.5224 5.92313 44.6444 4.6054 43.5373 3.49565C42.4301 2.3859 41.1154 1.50592 39.6685 0.906087C38.2216 0.306257 36.6709 -0.00164549 35.1052 6.61337e-06Z", fill: "#101010" })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "w-full flex flex-col gap-y-0.5 text-black-100", children: [
              /* @__PURE__ */ jsxs("p", { className: "flex justify-between uppercase text-xs smd:text-sm", children: [
                /* @__PURE__ */ jsx("span", { className: "font-bold", children: "uown" }),
                /* @__PURE__ */ jsx("span", { className: "text-gray-400", children: "4:10 PM" })
              ] }),
              /* @__PURE__ */ jsx("p", { className: "text-xs smd:text-sm", children: "It's pay day! You just recieved £45.68" })
            ] })
          ] })
        }
      ),
      /* @__PURE__ */ jsx(
        motion.div,
        {
          initial: { opacity: 0, y: 0 },
          whileInView: { opacity: 1 },
          transition: { duration: 0.6, ease: "easeIn" },
          className: "absolute hidden md:block smd:w-[340px] smd:h-[57px] left-[2%] top-[65%]",
          children: /* @__PURE__ */ jsxs("div", { className: "relative flex gap-x-2 items-center rounded-cmd bg-white shadow-stake py-6 px-1 smd:px-3 w-full h-full", children: [
            /* @__PURE__ */ jsxs("svg", { width: "30", height: "15", viewBox: "0 0 48 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", children: [
              /* @__PURE__ */ jsx("path", { d: "M20.3386 14.2694C20.3386 16.8492 19.3162 19.3234 17.4962 21.1476C15.6763 22.9718 13.2079 23.9966 10.6341 23.9966C8.06037 23.9966 5.592 22.9718 3.77206 21.1476C1.95212 19.3234 0.929688 16.8492 0.929688 14.2694L0.929688 0.845967C0.945298 0.626796 1.03922 0.420614 1.19423 0.265242C1.34924 0.10987 1.55494 0.0157264 1.7736 7.91782e-05H5.57065C5.6819 -0.00145697 5.79233 0.0193733 5.89541 0.0613392C5.99849 0.103305 6.09214 0.165555 6.17081 0.244411C6.24948 0.323267 6.31159 0.417129 6.35345 0.520451C6.39532 0.623773 6.4161 0.734458 6.41457 0.845967V14.2694C6.42974 15.3875 6.88492 16.4542 7.68101 17.2375C8.07287 17.6163 8.53537 17.9141 9.04209 18.1137C9.54881 18.3133 10.0898 18.4108 10.6341 18.4007C11.1785 18.4108 11.7195 18.3133 12.2262 18.1137C12.7329 17.9141 13.1954 17.6163 13.5873 17.2375C14.3834 16.4542 14.8386 15.3875 14.8537 14.2694V0.845967C14.8522 0.734556 14.8729 0.623965 14.9147 0.52072C14.9565 0.417475 15.0185 0.323665 15.0971 0.244825C15.1756 0.165984 15.2692 0.10371 15.3721 0.0616755C15.475 0.0196407 15.5854 -0.00130318 15.6965 7.91782e-05H19.4936C19.6048 -0.00145697 19.7152 0.0193733 19.8183 0.0613392C19.9214 0.103305 20.015 0.165555 20.0937 0.244411C20.1724 0.323267 20.2345 0.417129 20.2764 0.520451C20.3182 0.623773 20.339 0.734458 20.3375 0.845967L20.3386 14.2694Z", fill: "#71E5BD" }),
              /* @__PURE__ */ jsx("path", { d: "M38.3749 17.9737C37.374 18.5285 36.2489 18.8195 35.1052 18.8195C33.9614 18.8195 32.8363 18.5285 31.8354 17.9737C30.7773 17.392 29.8955 16.5349 29.2829 15.4928C28.6704 14.4506 28.3498 13.262 28.355 12.0525C28.355 10.2299 29.0773 8.48193 30.3631 7.19315C31.6488 5.90438 33.3927 5.18035 35.2111 5.18035C37.0294 5.18035 38.7733 5.90438 40.0591 7.19315C41.3448 8.48193 42.0672 10.2299 42.0672 12.0525C42.0093 13.2738 41.6394 14.4598 40.9928 15.4966C40.3463 16.5334 39.4448 17.3864 38.3749 17.9737ZM35.1052 6.61337e-06C32.7477 6.61337e-06 30.4431 0.700712 28.483 2.01351C26.5228 3.32632 24.995 5.19225 24.0929 7.37536C23.1907 9.55847 22.9547 11.9607 23.4146 14.2783C23.8745 16.5959 25.0097 18.7247 26.6767 20.3956C28.3437 22.0665 30.4676 23.2043 32.7798 23.6653C35.0919 24.1263 37.4886 23.8897 39.6666 22.9855C41.8446 22.0812 43.7062 20.5498 45.0159 18.5851C46.3257 16.6204 47.0248 14.3104 47.0248 11.9475C47.0264 10.378 46.7192 8.82369 46.1208 7.37341C45.5224 5.92313 44.6444 4.6054 43.5373 3.49565C42.4301 2.3859 41.1154 1.50592 39.6685 0.906087C38.2216 0.306257 36.6709 -0.00164549 35.1052 6.61337e-06Z", fill: "#101010" })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "w-full flex flex-col gap-y-0.5 text-black-100", children: [
              /* @__PURE__ */ jsxs("p", { className: "flex justify-between uppercase text-xs smd:text-sm", children: [
                /* @__PURE__ */ jsx("span", { className: "font-bold", children: "uown" }),
                /* @__PURE__ */ jsx("span", { className: "text-gray-400", children: "9:41 AM" })
              ] }),
              /* @__PURE__ */ jsx("p", { className: "text-xs smd:text-sm", children: "We've added a new project. Check it out." })
            ] })
          ] })
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mlg:order-1 flex flex-col mlg:basis-1/2 pt-10 gap-y-6", children: [
      /* @__PURE__ */ jsxs(
        motion.h2,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.4, ease: "easeIn" },
          className: "text-4xl md:text-5xl mlg:text-6xl text-black pb-6",
          children: [
            /* @__PURE__ */ jsx("span", { className: "anchor-text", children: "Earn" }),
            " from property, without owning a house"
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.5, ease: "easeIn" },
          className: "text-lg flex items-center",
          children: [
            /* @__PURE__ */ jsx("div", { className: "flex items-center shrink-0 justify-center font-bold bg-yellow-200 w-10 h-10 text-base rounded-full mr-3", children: /* @__PURE__ */ jsx(CheckIcon, { className: "h-6 w-6 text-black-100", "aria-hidden": "true" }) }),
            "Average return on investment of 17%"
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.5, ease: "easeIn" },
          className: "text-lg flex items-center",
          children: [
            /* @__PURE__ */ jsx("div", { className: "flex items-center shrink-0 justify-center font-bold bg-yellow-200 w-10 h-10 text-base rounded-full mr-3", children: /* @__PURE__ */ jsx(CheckIcon, { className: "h-6 w-6 text-black-100", "aria-hidden": "true" }) }),
            "Monthly dividends on selected projects"
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.5, ease: "easeIn" },
          className: "text-lg flex items-center",
          children: [
            /* @__PURE__ */ jsx("div", { className: "flex items-center shrink-0 justify-center font-bold bg-yellow-200 w-10 h-10 text-base rounded-full mr-3", children: /* @__PURE__ */ jsx(CheckIcon, { className: "h-6 w-6 text-black-100", "aria-hidden": "true" }) }),
            "Quality investments hand picked by experts"
          ]
        }
      ),
      /* @__PURE__ */ jsx(
        motion.button,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.6, ease: "easeIn" },
          type: "button",
          className: "mt-10 btn-black max-w-[200px] lxl:text-xl rounded-full px-4 py-2.5 text-lg font-bold text-white shadow-sm transition duration-150 ease-in hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-mint-300",
          children: "Get Started"
        }
      )
    ] })
  ] }) });
}

const Hdiw = {"src":"/_astro/hdiw.07f1d7ed.png","width":1176,"height":1176,"format":"png"};

function HDIWSlice() {
  const AccordionItem$1 = ({ id, header, ...rest }) => /* @__PURE__ */ jsx(
    AccordionItem,
    {
      ...rest,
      header: ({ state: { isEnter } }) => /* @__PURE__ */ jsxs(Fragment, { children: [
        /* @__PURE__ */ jsxs("p", { className: "flex items-center justify-center", children: [
          /* @__PURE__ */ jsx("span", { className: "flex items-center justify-center bg-yellow-200 w-8 h-8 text-center rounded-full mr-3", children: id }),
          header
        ] }),
        /* @__PURE__ */ jsx(ChevronDownIcon, { className: `ml-auto transition-transform duration-200 ease-out ${isEnter && "rotate-180"}` })
      ] }),
      className: "border-b font-bold",
      buttonProps: {
        className: ({ isEnter }) => `flex w-full p-4 text-left hover:bg-mint-50 ${isEnter}`
      },
      contentProps: {
        className: "text-base font-light transition-height duration-200 ease-out"
      },
      panelProps: { className: "p-4" }
    }
  );
  return /* @__PURE__ */ jsxs("section", { className: "p-6 md:p-10 max-w-7xl mx-auto", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col justify-center items-center text-center max-w-2xl mx-auto gap-y-4 pb-20", children: [
      /* @__PURE__ */ jsx(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.3, ease: "easeIn" },
          className: "text-sm font-bold tracking-widest uppercase px-2.5 py-2 bg-mint-100 text-black-50 rounded-full",
          children: "How does it work?"
        }
      ),
      /* @__PURE__ */ jsx(
        motion.h2,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.4, ease: "easeIn" },
          className: "text-4xl md:text-5xl lxl:text-6xl text-black",
          children: "Invest in income-generating real estate with ease."
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col mlg:flex-row justify-center items-center gap-x-20", children: [
      /* @__PURE__ */ jsx("img", { loading: "lazy", src: Hdiw.src, className: "flex mlg:basis-1/2 rounded-clg object-cover mlg:max-w-[500px]", alt: "" }),
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col mlg:basis-1/2 pt-10 gap-y-10", children: [
        /* @__PURE__ */ jsxs(Accordion, { transition: true, transitionTimeout: 200, children: [
          /* @__PURE__ */ jsx(AccordionItem$1, { id: "1", header: "Create your profile in less than 1 minute.", initialEntered: true, children: "It's super quick and easy to get setup. Whats more it's free and allows you to see all the details for each project." }),
          /* @__PURE__ */ jsx(AccordionItem$1, { id: "2", header: "Invest in one of our real estate projects.", children: "Once you have an account find the project you like and invest. You can pay directly from your bank account in seconds." }),
          /* @__PURE__ */ jsx(AccordionItem$1, { id: "3", header: "View your portfolio and track your returns in the dashboard.", children: "Once you have made your investment you can view your portfolio and track your returns in our dashboard." })
        ] }),
        /* @__PURE__ */ jsx("button", { type: "button", className: "mt-10 btn-black max-w-[200px] lxl:text-xl rounded-full px-4 py-2.5 text-lg font-bold text-white shadow-sm transition duration-150 ease-in hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-mint-300", children: "Get Started" })
      ] })
    ] })
  ] });
}

const Benefit = {"src":"/_astro/benefits.e579b77a.webp","width":1600,"height":1600,"format":"webp"};

const Checkmark = {"src":"/_astro/checkmark.62e51395.png","width":97,"height":80,"format":"png"};

const Home = {"src":"/_astro/home.606c6099.png","width":59,"height":80,"format":"png"};

const Shield = {"src":"/_astro/shield.1aa150d4.png","width":73,"height":80,"format":"png"};

const Lock = {"src":"/_astro/lock.f062b023.png","width":62,"height":80,"format":"png"};

function Benefits() {
  return /* @__PURE__ */ jsx("section", { className: "max-w-7xl mx-auto", children: /* @__PURE__ */ jsxs("div", { className: "p-6 md:p-10 mlg:py-24", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col justify-center items-center text-center max-w-2xl mx-auto gap-y-4 pb-20", children: [
      /* @__PURE__ */ jsx(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.3, ease: "easeIn" },
          className: "text-sm font-bold tracking-widest uppercase px-2.5 py-2 bg-mint-100 text-black-50 rounded-full",
          children: "Benefits"
        }
      ),
      /* @__PURE__ */ jsx(
        motion.h2,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.4, ease: "easeIn" },
          className: "text-4xl md:text-5xl",
          children: "More accessible and secure than going solo"
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col mlg:flex-row justify-center items-center gap-x-20", children: [
      /* @__PURE__ */ jsx("img", { loading: "lazy", src: Benefit.src, className: "mlg:order-2 flex mlg:basis-1/2 rounded-clg object-cover mlg:max-w-[500px]", alt: "" }),
      /* @__PURE__ */ jsxs("div", { className: "mlg:order-1 flex flex-col mlg:basis-1/2 pt-10 gap-y-10", children: [
        /* @__PURE__ */ jsxs("div", { className: "text-lg flex items-center", children: [
          /* @__PURE__ */ jsx("div", { className: "flex items-center justify-center shrink-0 font-bold bg-yellow-200 w-10 h-10 text-base rounded-full mr-3", children: /* @__PURE__ */ jsx("svg", { width: "22", height: "20", viewBox: "-1 -1 22 20", fill: "none", children: /* @__PURE__ */ jsx("path", { d: "M2.99993 10C2.99993 11.6484 3.66466 13.1415 4.74067 14.226C4.84445 14.3305 4.89633 14.3828 4.92696 14.4331C4.95619 14.4811 4.9732 14.5224 4.98625 14.5771C4.99993 14.6343 4.99993 14.6995 4.99993 14.8298V17.2C4.99993 17.48 4.99993 17.62 5.05443 17.727C5.10236 17.8211 5.17885 17.8976 5.27293 17.9455C5.37989 18 5.5199 18 5.79993 18H7.69993C7.97996 18 8.11997 18 8.22693 17.9455C8.32101 17.8976 8.3975 17.8211 8.44543 17.727C8.49993 17.62 8.49993 17.48 8.49993 17.2V16.8C8.49993 16.52 8.49993 16.38 8.55443 16.273C8.60236 16.1789 8.67885 16.1024 8.77293 16.0545C8.87989 16 9.0199 16 9.29993 16H10.6999C10.98 16 11.12 16 11.2269 16.0545C11.321 16.1024 11.3975 16.1789 11.4454 16.273C11.4999 16.38 11.4999 16.52 11.4999 16.8V17.2C11.4999 17.48 11.4999 17.62 11.5544 17.727C11.6024 17.8211 11.6789 17.8976 11.7729 17.9455C11.8799 18 12.0199 18 12.2999 18H14.2C14.48 18 14.62 18 14.727 17.9455C14.8211 17.8976 14.8976 17.8211 14.9455 17.727C15 17.62 15 17.48 15 17.2V16.2243C15 16.0223 15 15.9212 15.0288 15.8401C15.0563 15.7624 15.0911 15.708 15.15 15.6502C15.2114 15.59 15.3155 15.5417 15.5237 15.445C16.5059 14.989 17.344 14.2751 17.9511 13.3902C18.0579 13.2346 18.1112 13.1568 18.1683 13.1108C18.2228 13.0668 18.2717 13.0411 18.3387 13.021C18.4089 13 18.4922 13 18.6587 13H19.2C19.48 13 19.62 13 19.727 12.9455C19.8211 12.8976 19.8976 12.8211 19.9455 12.727C20 12.62 20 12.48 20 12.2V8.78575C20 8.51916 20 8.38586 19.9505 8.28303C19.9013 8.181 19.819 8.09867 19.717 8.04953C19.6141 8 19.4808 8 19.2143 8C19.0213 8 18.9248 8 18.8471 7.9738C18.7633 7.94556 18.7045 7.90798 18.6437 7.84377C18.5874 7.78422 18.5413 7.68464 18.4493 7.48547C18.1538 6.84622 17.7492 6.26777 17.2593 5.77404C17.1555 5.66945 17.1036 5.61716 17.073 5.56687C17.0437 5.51889 17.0267 5.47759 17.0137 5.42294C17 5.36567 17 5.30051 17 5.17018V4.06058C17 3.70053 17 3.52051 16.925 3.39951C16.8593 3.29351 16.7564 3.21588 16.6365 3.18184C16.4995 3.14299 16.3264 3.19245 15.9802 3.29136L13.6077 3.96922C13.5673 3.98074 13.5472 3.9865 13.5267 3.99054C13.5085 3.99414 13.4901 3.99671 13.4716 3.99826C13.4508 4 13.4298 4 13.3879 4H12.959M2.99993 10C2.99993 7.69594 4.29864 5.6952 6.20397 4.6899M2.99993 10H2C0.89543 10 0 9.10457 0 8C0 7.25972 0.402199 6.61337 1 6.26756M13 3.5C13 5.433 11.433 7 9.5 7C7.567 7 6 5.433 6 3.5C6 1.567 7.567 0 9.5 0C11.433 0 13 1.567 13 3.5Z", stroke: "black", strokeWidth: "1", strokeLinecap: "round", strokeLinejoin: "round" }) }) }),
          /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-3", children: [
            /* @__PURE__ */ jsx("div", { className: "font-bold text-lg lxl:text-xl", children: "Start without the need for large sums" }),
            /* @__PURE__ */ jsx("div", { className: "text-base text-[#737373]", children: "Begin with just £50, instead of purchasing an entire property." })
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "text-lg flex items-center", children: [
          /* @__PURE__ */ jsx("div", { className: "flex items-center justify-center shrink-0 font-bold bg-yellow-200 w-10 h-10 text-base rounded-full mr-3", children: /* @__PURE__ */ jsx("svg", { width: "20", height: "22", viewBox: "-1 -1 20 22", fill: "none", children: /* @__PURE__ */ jsx("path", { d: "M5.59 11.51L12.42 15.49M12.41 4.51L5.59 8.49M18 3C18 4.65685 16.6569 6 15 6C13.3431 6 12 4.65685 12 3C12 1.34315 13.3431 0 15 0C16.6569 0 18 1.34315 18 3ZM6 10C6 11.6569 4.65685 13 3 13C1.34315 13 0 11.6569 0 10C0 8.34315 1.34315 7 3 7C4.65685 7 6 8.34315 6 10ZM18 17C18 18.6569 16.6569 20 15 20C13.3431 20 12 18.6569 12 17C12 15.3431 13.3431 14 15 14C16.6569 14 18 15.3431 18 17Z", stroke: "black", strokeWidth: "1", strokeLinecap: "round", strokeLinejoin: "round" }) }) }),
          /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-3", children: [
            /* @__PURE__ */ jsx("div", { className: "font-bold text-lg", children: "Forget about operational management" }),
            /* @__PURE__ */ jsx("div", { className: "text-base text-[#737373]", children: "No tenant management or unexpected site issues. We take care of it." })
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "text-lg flex items-center", children: [
          /* @__PURE__ */ jsx("div", { className: "flex items-center justify-center shrink-0 font-bold bg-yellow-200 w-10 h-10 text-base rounded-full mr-3", children: /* @__PURE__ */ jsx("svg", { width: "22", height: "20", viewBox: "-1 -1 22 20", fill: "none", children: /* @__PURE__ */ jsx("path", { d: "M0 11C0 11 0.12132 11.8492 3.63604 15.364C7.15076 18.8787 12.8492 18.8787 16.364 15.364C17.6092 14.1187 18.4133 12.5993 18.7762 11M0 11V17M0 11H6M20 7C20 7 19.8787 6.15076 16.364 2.63604C12.8492 -0.87868 7.15076 -0.87868 3.63604 2.63604C2.39076 3.88131 1.58669 5.40072 1.22383 7M20 7V1M20 7H14", stroke: "black", strokeWidth: "1", strokeLinecap: "round", strokeLinejoin: "round" }) }) }),
          /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-3", children: [
            /* @__PURE__ */ jsx("div", { className: "font-bold text-lg", children: "Get returns from day one" }),
            /* @__PURE__ */ jsx("div", { className: "text-base text-[#737373]", children: "Many projects pay out monthly, so you don't have to wait around." })
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "text-lg flex items-center", children: [
          /* @__PURE__ */ jsx("div", { className: "flex items-center justify-center shrink-0 font-bold bg-yellow-200 w-10 h-10 text-base rounded-full mr-3", children: /* @__PURE__ */ jsx("svg", { width: "22", height: "22", viewBox: "-1 -1 22 22", fill: "none", children: /* @__PURE__ */ jsx("path", { d: "M6.3767 13.6172L0.719849 19.274M9.69439 4.64267L8.1335 6.20356C8.00618 6.33088 7.94252 6.39454 7.86999 6.44513C7.80561 6.49003 7.73616 6.52719 7.66309 6.55585C7.58077 6.58814 7.49249 6.6058 7.31592 6.64111L3.65145 7.37401C2.69915 7.56447 2.223 7.6597 2.00024 7.91075C1.80617 8.12946 1.71755 8.42215 1.75771 8.71178C1.8038 9.04423 2.14715 9.38759 2.83387 10.0743L9.91961 17.16C10.6063 17.8468 10.9497 18.1901 11.2821 18.2362C11.5718 18.2764 11.8645 18.1877 12.0832 17.9937C12.3342 17.7709 12.4294 17.2948 12.6199 16.3425L13.3528 12.678C13.3881 12.5014 13.4058 12.4131 13.4381 12.3308C13.4667 12.2578 13.5039 12.1883 13.5488 12.1239C13.5994 12.0514 13.663 11.9877 13.7904 11.8604L15.3512 10.2995C15.4326 10.2181 15.4734 10.1774 15.5181 10.1419C15.5578 10.1103 15.5999 10.0818 15.644 10.0566C15.6936 10.0283 15.7465 10.0056 15.8523 9.96026L18.3467 8.89125C19.0744 8.57938 19.4383 8.42344 19.6035 8.17146C19.7481 7.9511 19.7998 7.6826 19.7474 7.42433C19.6875 7.12899 19.4076 6.84907 18.8478 6.28925L13.7047 1.14611C13.1448 0.586286 12.8649 0.306372 12.5696 0.246501C12.3113 0.194145 12.0428 0.245853 11.8225 0.390385C11.5705 0.555662 11.4145 0.919512 11.1027 1.64721L10.0337 4.14157C9.9883 4.24739 9.96563 4.30029 9.93728 4.34991C9.9121 4.39398 9.88361 4.43607 9.85204 4.47582C9.8165 4.52056 9.7758 4.56126 9.69439 4.64267Z", stroke: "black", strokeWidth: "1", strokeLinecap: "round", strokeLinejoin: "round" }) }) }),
          /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-3", children: [
            /* @__PURE__ */ jsx("div", { className: "font-bold text-lg", children: "Benefit from diversification" }),
            /* @__PURE__ */ jsx("div", { className: "text-base text-[#737373]", children: "Avoid concentrating your capital in a single project." })
          ] })
        ] }),
        /* @__PURE__ */ jsx("button", { type: "button", className: "mt-10 btn-black max-w-[200px] lxl:text-xl rounded-full px-4 py-2.5 text-lg font-bold text-white shadow-sm transition duration-150 ease-in hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-mint-300", children: "Get Started" })
      ] })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "grid lg:grid-cols-4 md:grid-cols-2 grid-cols-1 gap-6 pt-40", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col justify-between items-start rounded-clg bg-yellow-10 p-6 h-[338px]", children: [
        /* @__PURE__ */ jsx("img", { loading: "lazy", src: Home.src, className: "h-[97px]", alt: "home" }),
        /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-3", children: [
          /* @__PURE__ */ jsx("div", { className: "text-2xl", children: "Impact" }),
          /* @__PURE__ */ jsx("div", { className: "text-base text-[#737373]", children: "We want to enable everyone to build wealth through real estate, not just the rich." })
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col justify-between items-start rounded-clg bg-yellow-10 p-6 h-[338px]", children: [
        /* @__PURE__ */ jsx("img", { loading: "lazy", src: Shield.src, className: "h-[97px]", alt: "shield" }),
        /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-3", children: [
          /* @__PURE__ */ jsx("div", { className: "text-2xl", children: "Transparency" }),
          /* @__PURE__ */ jsx("div", { className: "text-base text-[#737373]", children: "We operate with utmost transparency with our investors. No surprises." })
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col justify-between items-start rounded-clg bg-yellow-10 p-6 h-[338px]", children: [
        /* @__PURE__ */ jsx("img", { loading: "lazy", src: Lock.src, className: "h-[97px]", alt: "lock" }),
        /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-3", children: [
          /* @__PURE__ */ jsx("div", { className: "text-2xl", children: "Simplicity" }),
          /* @__PURE__ */ jsx("div", { className: "text-base text-[#737373] pr-4", children: "We focus our energies on making the complex world of property investment simple." })
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col justify-between items-start rounded-clg bg-yellow-10 p-6 h-[338px]", children: [
        /* @__PURE__ */ jsx("img", { loading: "lazy", src: Checkmark.src, className: "h-[97px]", alt: "checkmark" }),
        /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-3", children: [
          /* @__PURE__ */ jsx("div", { className: "text-2xl", children: "Reliability" }),
          /* @__PURE__ */ jsx("div", { className: "text-base text-[#737373]", children: "We work hard to provide you with the best and safest investments." })
        ] })
      ] })
    ] })
  ] }) });
}

function OptimizeRR() {
  return /* @__PURE__ */ jsx("section", { className: "max-w-7xl mx-auto", children: /* @__PURE__ */ jsxs("div", { className: "flex flex-col justify-center items-center border border-dashed border-[#e9e9e9] p-10 m-6 rounded-clg", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col mlg:flex-row gap-x-6 items-center pb-20", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col justify-left items-left text-left gap-y-6 pb-6", children: [
        /* @__PURE__ */ jsx(
          motion.div,
          {
            initial: { opacity: 0, y: 50 },
            whileInView: { opacity: 1, y: 0, threshold: 0.99 },
            transition: { duration: 0.3, ease: "easeIn" },
            className: "text-sm font-bold tracking-widest uppercase px-2.5 py-2 bg-mint-100 text-black-50 rounded-full w-fit",
            children: "Savings"
          }
        ),
        /* @__PURE__ */ jsxs(
          motion.h2,
          {
            initial: { opacity: 0, y: 50 },
            whileInView: { opacity: 1, y: 0, threshold: 0.99 },
            transition: { duration: 0.4, ease: "easeIn" },
            className: "text-2xl md:text-5xl lxl:text-6xl text-black",
            children: [
              /* @__PURE__ */ jsx("span", { className: "text-mint-500", children: "Optimise your returns" }),
              " and get more from your investment"
            ]
          }
        )
      ] }),
      /* @__PURE__ */ jsx(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.6, ease: "easeIn" },
          className: "text-base mlg:text-lg lxl:text-xl text-[#737373] mlg:max-w-lg",
          children: "Secure premium profits. We seamlessly provide you with the highest returns. Keep up to an extra 17.6% after taxes. Some of your earnings could be exempt from local and city taxes."
        }
      )
    ] }),
    /* @__PURE__ */ jsx("div", { className: "border border-dashed w-full border-[#e9e9e9]" }),
    /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 mlg:grid-cols-3 text-left gap-10 max-w-[1100px] pt-6", children: [
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "easeIn" },
          className: "flex flex-col gap-y-4 p-6 text-center",
          children: [
            /* @__PURE__ */ jsx("p", { className: "text-6xl", children: "£70m+" }),
            /* @__PURE__ */ jsx("p", { className: "text-base lg:text-lg text-[#737373]", children: "We have built more than £70m worth of property over the last 10 years. Delivering results for our investors." })
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "easeIn" },
          className: "flex flex-col gap-y-4 p-6 text-center",
          children: [
            /* @__PURE__ */ jsx("p", { className: "text-6xl", children: "17.7%" }),
            /* @__PURE__ */ jsx("p", { className: "text-base  lg:text-lg text-[#737373]", children: "Earn an average of 17.7% on your investments. This is our historical average return per project." })
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "easeIn" },
          className: "flex flex-col gap-y-4 p-6 text-center",
          children: [
            /* @__PURE__ */ jsx("p", { className: "text-6xl", children: "2%" }),
            /* @__PURE__ */ jsx("p", { className: "text-base  lg:text-lg text-[#737373]", children: "We charge you a clear 2% fee and take none of your profits, menaing you keep more of your money." })
          ]
        }
      )
    ] })
  ] }) });
}

const $$GotQuestions = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${maybeRenderHead()}<section class="max-w-7xl mx-auto w-full px-6 py-10"> <div class="flex flex-col md:flex-row rounded-clg bg-mint-50 md:gap-x-5 gap-y-4 px-10 py-16 items-center justify-between"> <div class="text-left items-start py-10 lg:py-0 basis-1/2 flex flex-col gap-y-8 lg:gap-y-8 justify-center"> <p class="text-4xl lxl:text-6xl text-black font-bold">Still got questions?</p> <p class="text-xl lxl:text-2xl text-[#737373]">If you don't find an answer to your question, contact us, and our team will get in touch with you.</p> </div> ${renderComponent($$result, "Button", $$Button, { "type": "button", "color": "btn-black max-w-full md:max-w-[300px] lxl:text-2xl", "text": "Get in touch" })} </div> </section>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/GotQuestions.astro", void 0);

function FAQs() {
  return /* @__PURE__ */ jsxs("section", { className: "max-w-7xl mx-auto px-10 py-20 sm:py-28", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col justify-center items-center mx-auto text-center gap-y-8 max-w-3xl pb-12", children: [
      /* @__PURE__ */ jsx(
        motion.h2,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.3, ease: "easeIn" },
          className: "text-5xl lxl:text-7xl text-black",
          children: "FAQs"
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.4, ease: "easeIn" },
          children: [
            /* @__PURE__ */ jsx("p", { className: "text-lg lxl:text-xl text-[#737373]", children: "Still got questions? We've got answers. " }),
            /* @__PURE__ */ jsx("p", { className: "text-lg lxl:text-xl text-[#737373]", children: "Can't find the answer to your question? Reach out to our team!" })
          ]
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 mlg:grid-cols-3 text-left gap-10", children: [
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "easeIn" },
          className: "flex flex-col gap-y-4",
          children: [
            /* @__PURE__ */ jsx("p", { className: "text-xl", children: "Are you a regulated entity?" }),
            /* @__PURE__ */ jsx("p", { className: "text-base lxl:text-lg text-[#737373]", children: "Our current business model is not a regulated activity. We are in the process of gaining authorisation with the FCA to enhance our investment offering." })
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "easeIn" },
          className: "flex  flex-col gap-y-4",
          children: [
            /* @__PURE__ */ jsx("p", { className: "text-xl", children: "How can you guarantee a 10% yield?" }),
            /* @__PURE__ */ jsx("p", { className: "text-base lxl:text-lg text-[#737373]", children: "We don't guarantee returns unless the project specifically states there is a corporate guarantee from the borrower. Beyond that we only select with most profitable projects. If you want to learn more about our process, contact us." })
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "easeIn" },
          className: "flex  flex-col gap-y-4",
          children: [
            /* @__PURE__ */ jsx("p", { className: "text-xl", children: "Can I exit whenever I want?" }),
            /* @__PURE__ */ jsx("p", { className: "text-base lxl:text-lg text-[#737373]", children: "Development projects will exit at the end of the project, which can be delayed. Returns paid as monthly dividends are yours to reinvest or withdraw." })
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "easeIn" },
          className: "flex  flex-col gap-y-4",
          children: [
            /* @__PURE__ */ jsx("p", { className: "text-xl", children: "Are there any fees?" }),
            /* @__PURE__ */ jsx("p", { className: "text-base lxl:text-lg text-[#737373]", children: "At UOWN we have one fee: 2% transaction fee - this fee is charged when you invest into any project on UOWN. For example if you invest £50 you will be charged a fee of £1." })
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "easeIn" },
          className: "flex flex-col  gap-y-4",
          children: [
            /* @__PURE__ */ jsx("p", { className: "text-xl", children: "In what type of projects do you invest?" }),
            /* @__PURE__ */ jsx("p", { className: "text-base lxl:text-lg text-[#737373]", children: "We invest in development projects, either our own, or from heavily vetted developers that we trust have a track record of success." })
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "easeIn" },
          className: "flex  flex-col gap-y-4",
          children: [
            /* @__PURE__ */ jsx("p", { className: "text-xl", children: "What tax implications are there?" }),
            /* @__PURE__ */ jsxs("p", { className: "text-base lxl:text-lg text-[#737373]", children: [
              "Every property investment in UOWN is owned by a UK limited company (SPV). Corporation tax is payable on the taxable profit made by each SPV. To see the Corporation Tax rate in the UK: ",
              /* @__PURE__ */ jsx("a", { href: "https://www.gov.uk/government/publications/rates-and-allowances-corporation-tax/rates-and-allowances-corporation-tax", children: "Click here." }),
              " ",
              /* @__PURE__ */ jsx("br", {}),
              "Please note that tax rates are subject to future change, and the above does not constitute tax advice. We do provide any tax advice and if you are not certain then you should consult an accountant."
            ] })
          ]
        }
      )
    ] })
  ] });
}

const RealEstate = {"src":"/_astro/slice-real-estate.d3291519.png","width":2352,"height":2352,"format":"png"};

const $$SliceRealEstateSection = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${maybeRenderHead()}<section class="max-w-7xl mx-auto w-full px-6 py-10"> <div class="flex flex-col mlg:flex-row rounded-clg bg-yellow-50 lg:gap-x-5 gap-y-4 p-6 mlg::pl-10 pb-0"> <div class="text-center lg:text-left items-center lg:items-start pt-10 lg:py-0 basis-1/2 flex flex-col gap-y-12 lg:gap-y-8 justify-center"> <p class="text-3xl md:text-5xl lxl:text-6xl text-black-100 font-bold">Invest in real estate today and start build your wealth</p> ${renderComponent($$result, "Button", $$Button, { "type": "button", "color": "btn-mint max-w-[200px] lxl:text-xl", "text": "Get Started" })} </div> <div class="relative flex mlg:basis-1/2 object-cover overflow-hidden"> ${renderComponent($$result, "Image", $$Image, { "src": RealEstate, "class": "object-cover", "alt": "Real estate image" })} <div class="absolute hidden md:block md:w-[320px] md:h-[70px] bottom-[20%] md:right-[2%] md:top-[30%]"> <div class="relative flex gap-x-2 items-center rounded-cmd bg-white shadow-stake py-6 px-1 smd:px-3 w-full h-full"> <svg width="30" height="15" viewBox="0 0 48 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.3386 14.2694C20.3386 16.8492 19.3162 19.3234 17.4962 21.1476C15.6763 22.9718 13.2079 23.9966 10.6341 23.9966C8.06037 23.9966 5.592 22.9718 3.77206 21.1476C1.95212 19.3234 0.929688 16.8492 0.929688 14.2694L0.929688 0.845967C0.945298 0.626796 1.03922 0.420614 1.19423 0.265242C1.34924 0.10987 1.55494 0.0157264 1.7736 7.91782e-05H5.57065C5.6819 -0.00145697 5.79233 0.0193733 5.89541 0.0613392C5.99849 0.103305 6.09214 0.165555 6.17081 0.244411C6.24948 0.323267 6.31159 0.417129 6.35345 0.520451C6.39532 0.623773 6.4161 0.734458 6.41457 0.845967V14.2694C6.42974 15.3875 6.88492 16.4542 7.68101 17.2375C8.07287 17.6163 8.53537 17.9141 9.04209 18.1137C9.54881 18.3133 10.0898 18.4108 10.6341 18.4007C11.1785 18.4108 11.7195 18.3133 12.2262 18.1137C12.7329 17.9141 13.1954 17.6163 13.5873 17.2375C14.3834 16.4542 14.8386 15.3875 14.8537 14.2694V0.845967C14.8522 0.734556 14.8729 0.623965 14.9147 0.52072C14.9565 0.417475 15.0185 0.323665 15.0971 0.244825C15.1756 0.165984 15.2692 0.10371 15.3721 0.0616755C15.475 0.0196407 15.5854 -0.00130318 15.6965 7.91782e-05H19.4936C19.6048 -0.00145697 19.7152 0.0193733 19.8183 0.0613392C19.9214 0.103305 20.015 0.165555 20.0937 0.244411C20.1724 0.323267 20.2345 0.417129 20.2764 0.520451C20.3182 0.623773 20.339 0.734458 20.3375 0.845967L20.3386 14.2694Z" fill="#71E5BD"></path><path d="M38.3749 17.9737C37.374 18.5285 36.2489 18.8195 35.1052 18.8195C33.9614 18.8195 32.8363 18.5285 31.8354 17.9737C30.7773 17.392 29.8955 16.5349 29.2829 15.4928C28.6704 14.4506 28.3498 13.262 28.355 12.0525C28.355 10.2299 29.0773 8.48193 30.3631 7.19315C31.6488 5.90438 33.3927 5.18035 35.2111 5.18035C37.0294 5.18035 38.7733 5.90438 40.0591 7.19315C41.3448 8.48193 42.0672 10.2299 42.0672 12.0525C42.0093 13.2738 41.6394 14.4598 40.9928 15.4966C40.3463 16.5334 39.4448 17.3864 38.3749 17.9737ZM35.1052 6.61337e-06C32.7477 6.61337e-06 30.4431 0.700712 28.483 2.01351C26.5228 3.32632 24.995 5.19225 24.0929 7.37536C23.1907 9.55847 22.9547 11.9607 23.4146 14.2783C23.8745 16.5959 25.0097 18.7247 26.6767 20.3956C28.3437 22.0665 30.4676 23.2043 32.7798 23.6653C35.0919 24.1263 37.4886 23.8897 39.6666 22.9855C41.8446 22.0812 43.7062 20.5498 45.0159 18.5851C46.3257 16.6204 47.0248 14.3104 47.0248 11.9475C47.0264 10.378 46.7192 8.82369 46.1208 7.37341C45.5224 5.92313 44.6444 4.6054 43.5373 3.49565C42.4301 2.3859 41.1154 1.50592 39.6685 0.906087C38.2216 0.306257 36.6709 -0.00164549 35.1052 6.61337e-06Z" fill="#101010"></path></svg> <div class="w-full flex flex-col gap-y-0.5 text-black-100"> <p class="text-xs md:text-sm">
Congratulations! You just invested £450.
</p> </div> </div> </div> </div> </div> </section>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/SliceRealEstateSection.astro", void 0);

function Features() {
  return /* @__PURE__ */ jsxs("section", { className: "flex flex-col justify-center items-center px-6 md:px-10 py-20 sm:py-28 mx-auto", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col justify-center items-center text-center max-w-2xl mx-auto gap-y-4 pb-20", children: [
      /* @__PURE__ */ jsx(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.3, ease: "easeIn" },
          className: "text-sm font-bold tracking-widest uppercase px-2.5 py-2 bg-mint-100 text-black-50 rounded-full",
          children: "Features"
        }
      ),
      /* @__PURE__ */ jsx(
        motion.h2,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.4, ease: "easeIn" },
          className: "text-4xl md:text-5xl",
          children: "Property is the cornerstone to build your wealth."
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 mlg:grid-cols-3 text-left gap-10 max-w-[1100px]", children: [
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "easeIn" },
          className: "flex flex-col gap-y-4 p-6 rounded-clg bg-yellow-10 justify-between h-[370px] mlg:max-w-[350px]",
          children: [
            /* @__PURE__ */ jsx("div", { className: "bg-mint-300 p-4 rounded-csm w-fit", children: /* @__PURE__ */ jsx("svg", { className: "w-7 h-7", xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 256 256", focusable: "false", color: "rgb(0, 0, 0)", style: { "userSelect": "none", "display": "inline-block", "fill": "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", "color": "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", "flexShrink": "0" }, children: /* @__PURE__ */ jsx("g", { color: "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", weight: "regular", children: /* @__PURE__ */ jsx("path", { d: "M232,208a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V48a8,8,0,0,1,16,0V200H224A8,8,0,0,1,232,208ZM132,160a12,12,0,1,0-12-12A12,12,0,0,0,132,160Zm-24-56A12,12,0,1,0,96,92,12,12,0,0,0,108,104ZM76,176a12,12,0,1,0-12-12A12,12,0,0,0,76,176Zm96-48a12,12,0,1,0-12-12A12,12,0,0,0,172,128Zm24-40a12,12,0,1,0-12-12A12,12,0,0,0,196,88Zm-20,76a12,12,0,1,0,12-12A12,12,0,0,0,176,164Z" }) }) }) }),
            /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-5", children: [
              /* @__PURE__ */ jsx("p", { className: "text-2xl", children: "Improved stability" }),
              /* @__PURE__ */ jsx("p", { className: "text-xl text-[#737373]", children: "Historically, real estate has provided lower volatility compared to stock investments." })
            ] })
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "easeIn" },
          className: "flex flex-col gap-y-4 p-6 rounded-clg bg-yellow-10 justify-between h-[370px] mlg:max-w-[350px]",
          children: [
            /* @__PURE__ */ jsx("div", { className: "bg-mint-300 p-4 rounded-csm w-fit", children: /* @__PURE__ */ jsx("svg", { className: "w-7 h-7", xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 256 256", focusable: "false", color: "rgb(0, 0, 0)", style: { "userSelect": "none", "display": "inline-block", "fill": "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", "color": "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", "flexShrink": "0" }, children: /* @__PURE__ */ jsx("g", { color: "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", weight: "regular", children: /* @__PURE__ */ jsx("path", { d: "M222.29,123.06l-88-112a8,8,0,0,0-12.58,0l-88,112a8,8,0,0,0,0,9.88l88,112a8,8,0,0,0,12.58,0l88-112A8,8,0,0,0,222.29,123.06ZM136,39.13l67.42,85.8L136,155.58ZM120,155.58,52.58,124.93,120,39.13Zm0,17.57v43.72l-53.43-68Zm16,0,53.43-24.29-53.43,68Z" }) }) }) }),
            /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-5", children: [
              /* @__PURE__ */ jsx("p", { className: "text-2xl", children: "Steady income" }),
              /* @__PURE__ */ jsx("p", { className: "text-xl text-[#737373]", children: "Generate a consistent income without being as exposed to recessions." })
            ] })
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "easeIn" },
          className: "flex flex-col gap-y-4 p-6 rounded-clg bg-yellow-10 justify-between h-[370px] mlg:max-w-[350px]",
          children: [
            /* @__PURE__ */ jsx("div", { className: "bg-mint-300 p-4 rounded-csm w-fit", children: /* @__PURE__ */ jsx("svg", { className: "w-7 h-7", xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 256 256", focusable: "false", color: "rgb(0, 0, 0)", style: { "userSelect": "none", "display": "inline-block", "fill": "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", "color": "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", "flexShrink": "0" }, children: /* @__PURE__ */ jsx("g", { color: "var(--token-33722da1-56ef-4815-82ed-442105eb06b1, rgb(0, 0, 0))", weight: "regular", children: /* @__PURE__ */ jsx("path", { d: "M248,128a56,56,0,0,1-95.6,39.6l-.33-.35L92.12,99.55a40,40,0,1,0,0,56.9l8.52-9.62a8,8,0,1,1,12,10.61l-8.69,9.81-.33.35a56,56,0,1,1,0-79.2l.33.35,59.95,67.7a40,40,0,1,0,0-56.9l-8.52,9.62a8,8,0,1,1-12-10.61l8.69-9.81.33-.35A56,56,0,0,1,248,128Z" }) }) }) }),
            /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-5", children: [
              /* @__PURE__ */ jsx("p", { className: "text-2xl", children: "Inflation protection" }),
              /* @__PURE__ */ jsx("p", { className: "text-xl text-[#737373]", children: "Safeguard your investment against inflation with our built-in inflation projections." })
            ] })
          ]
        }
      )
    ] })
  ] });
}

function PRSlice() {
  const [value, setValue] = useState(1e3);
  const [final, setFinal] = useState(0);
  const [interest, setInterest] = useState(0);
  function compoundWithPMT(P, PMT, annualRate, year) {
    if (annualRate === 0) {
      return P + PMT * (12 * year);
    } else {
      const n = 12;
      const r = annualRate;
      const exponent = n * year;
      const growthFactor = Math.pow(1 + r / n, exponent);
      return P * growthFactor + PMT * ((growthFactor - 1) / (r / n));
    }
  }
  function CalculateInterest(e) {
    setValue(e.target.value.toLocaleString());
    const P = Number(document.getElementById("balance").value);
    const rUown = 0.1;
    const t = 5;
    const data = [];
    var uownValue = 0;
    var finalUown = 0;
    for (let year = 0; year <= t; year++) {
      uownValue = compoundWithPMT(P, 0, rUown, year);
      data.push({
        x: `Year ${year}`,
        uown: uownValue
      });
    }
    finalUown = data[5].uown.toFixed(2);
    setFinal(finalUown.toLocaleString());
    setInterest((finalUown - P).toLocaleString());
  }
  return /* @__PURE__ */ jsx("section", { className: "p-6 md:p-10 max-w-7xl mx-auto", children: /* @__PURE__ */ jsx("div", { className: "flex flex-col justify-center items-center bg-yellow-50 px-3 md:px-6 mlg:px-12 py-16 rounded-clg", children: /* @__PURE__ */ jsxs("div", { className: "w-full flex flex-col lg:flex-row gap-x-6 lg:gap-x-10 items-center justify-between", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col lg:basis-1/3 justify-left items-left text-left gap-y-4 pb-6", children: [
      /* @__PURE__ */ jsx(
        motion.h2,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.4, ease: "easeIn" },
          className: "text-2xl md:text-4xl lxl:text-5xl text-black",
          children: "Calculate your potential returns"
        }
      ),
      /* @__PURE__ */ jsx(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.6, ease: "easeIn" },
          className: "text-base mlg:text-lg lxl:text-xl text-[#737373] mlg:max-w-lg",
          children: "Whether it's building a passive income stream through monthly dividends or capitalising on property appreciation."
        }
      )
    ] }),
    /* @__PURE__ */ jsxs(
      motion.div,
      {
        initial: { opacity: 0, y: 50 },
        whileInView: { opacity: 1, y: 0, threshold: 0.99 },
        transition: { duration: 0.6, ease: "easeIn" },
        className: "flex flex-col w-full lg:basis-2/3 rounded-clg bg-white border border-[#737373] p-10 gap-y-8",
        children: [
          /* @__PURE__ */ jsxs("div", { id: "initial-balance", className: "flex justify-between text-lg font-extrabold", children: [
            /* @__PURE__ */ jsx("label", { className: "text-sm md:text-lg", children: "Initial Investment" }),
            /* @__PURE__ */ jsxs("span", { children: [
              "£ ",
              value
            ] })
          ] }),
          /* @__PURE__ */ jsx("input", { className: "", style: {
            WebkitAppearance: "none",
            width: "100%",
            height: "24px",
            background: "#e8eff0",
            outline: "none",
            borderRadius: "50px",
            marginTop: "24px",
            marginBottom: "24px",
            position: "relative"
          }, id: "balance", type: "range", min: "1000", max: "100000", step: "1000", value, onChange: CalculateInterest }),
          /* @__PURE__ */ jsxs("div", { style: {
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center"
          }, children: [
            /* @__PURE__ */ jsx("div", { id: "earned", className: "text-sm md:text-lg", children: "Your interest in 5 years" }),
            /* @__PURE__ */ jsxs("div", { className: "flex font-extrabold text-2xl md:text-5xl gap-x-2", children: [
              /* @__PURE__ */ jsx("span", { className: "text-gray-50", children: "£" }),
              /* @__PURE__ */ jsx("span", { id: "earned-value", children: interest })
            ] })
          ] }),
          /* @__PURE__ */ jsxs("div", { id: "result", className: "flex justify-between text-lg pt-4", children: [
            /* @__PURE__ */ jsx("label", { className: "text-sm md:text-lg font-extrabold", children: "Future value" }),
            /* @__PURE__ */ jsxs("span", { children: [
              "£ ",
              final
            ] })
          ] })
        ]
      }
    )
  ] }) }) });
}

const $$DemoPage4 = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Property Crowdfunding & Investment Platform | UOWN", "classes": "bg-white", "description": " from property, without owning a house", "purl": "" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "HeroSection", HeroSlice, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HeroSlice.jsx", "client:component-export": "default" })} ${renderComponent($$result2, "InfiniteScroll", Home$1, { "bgClass": "", "title": "As featured in...", "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/InfiniteScrollSlice", "client:component-export": "default" })} ${renderComponent($$result2, "HDIWSlice", HDIWSlice, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HDIWSlice.jsx", "client:component-export": "default" })} ${renderComponent($$result2, "PRSlice", PRSlice, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/PRSlice", "client:component-export": "default" })} ${renderComponent($$result2, "BenefitsSlice", Benefits, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/BenefitsSlice.jsx", "client:component-export": "default" })} ${renderComponent($$result2, "OptimizeRR", OptimizeRR, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/OptimizeRR.jsx", "client:component-export": "default" })} ${renderComponent($$result2, "Features", Features, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Features.jsx", "client:component-export": "default" })} ${renderComponent($$result2, "FAQs", FAQs, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/FAQs.jsx", "client:component-export": "default" })} ${renderComponent($$result2, "Reviews", Reviews, { "client:load": true, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Reviews", "client:component-export": "Reviews" })} ${renderComponent($$result2, "GotQuestions", $$GotQuestions, {})} ${renderComponent($$result2, "SliceRealEstateSection", $$SliceRealEstateSection, {})} ` })}`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/demoPage4.astro", void 0);

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/demoPage4.astro";
const $$url = "/demoPage4";

const demoPage4 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
    __proto__: null,
    default: $$DemoPage4,
    file: $$file,
    url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

export { Mobile as M, demoPage4 as d };

import '@astrojs/internal-helpers/path';
import { $ as $$Layout, a as $$Button } from './404_1f20d35e.mjs';
import { e as createAstro, f as createComponent, A as AstroError, k as ImageMissingAlt, r as renderTemplate, m as maybeRenderHead, g as addAttribute, s as spreadAttributes, i as renderComponent, u as unescapeHTML } from '../astro_ca9e373b.mjs';
/* empty css                            */import { jsx, jsxs } from 'react/jsx-runtime';
import 'react';
import { PortableText } from '@portabletext/react';
import 'clsx';
import { i as isESMImportedImage, g as getImage$1 } from '../astro-assets-services_967ef4fc.mjs';
/* empty css                                 */import imageUrlBuilder from '@sanity/image-url';
import groq from 'groq';
/* empty css                                 */
const $$Astro$e = createAstro("https://www.uown.co");
const $$Image = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$e, $$props, $$slots);
  Astro2.self = $$Image;
  const props = Astro2.props;
  if (props.alt === void 0 || props.alt === null) {
    throw new AstroError(ImageMissingAlt);
  }
  if (typeof props.width === "string") {
    props.width = parseInt(props.width);
  }
  if (typeof props.height === "string") {
    props.height = parseInt(props.height);
  }
  const image = await getImage(props);
  const additionalAttributes = {};
  if (image.srcSet.values.length > 0) {
    additionalAttributes.srcset = image.srcSet.attribute;
  }
  return renderTemplate`${maybeRenderHead()}<img${addAttribute(image.src, "src")}${spreadAttributes(additionalAttributes)}${spreadAttributes(image.attributes)}>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/node_modules/astro/components/Image.astro", void 0);

const $$Astro$d = createAstro("https://www.uown.co");
const $$Picture = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$d, $$props, $$slots);
  Astro2.self = $$Picture;
  const defaultFormats = ["webp"];
  const defaultFallbackFormat = "png";
  const specialFormatsFallback = ["gif", "svg"];
  const { formats = defaultFormats, pictureAttributes = {}, fallbackFormat, ...props } = Astro2.props;
  if (props.alt === void 0 || props.alt === null) {
    throw new AstroError(ImageMissingAlt);
  }
  const optimizedImages = await Promise.all(
    formats.map(
      async (format) => await getImage({ ...props, format, widths: props.widths, densities: props.densities })
    )
  );
  let resultFallbackFormat = fallbackFormat ?? defaultFallbackFormat;
  if (!fallbackFormat && isESMImportedImage(props.src) && specialFormatsFallback.includes(props.src.format)) {
    resultFallbackFormat = props.src.format;
  }
  const fallbackImage = await getImage({
    ...props,
    format: resultFallbackFormat,
    widths: props.widths,
    densities: props.densities
  });
  const additionalAttributes = {};
  if (fallbackImage.srcSet.values.length > 0) {
    additionalAttributes.srcset = fallbackImage.srcSet.attribute;
  }
  return renderTemplate`${maybeRenderHead()}<picture${spreadAttributes(pictureAttributes)}> ${Object.entries(optimizedImages).map(([_, image]) => {
    const srcsetAttribute = props.densities || !props.densities && !props.widths ? `${image.src}${image.srcSet.values.length > 0 ? ", " + image.srcSet.attribute : ""}` : image.srcSet.attribute;
    return renderTemplate`<source${addAttribute(srcsetAttribute, "srcset")}${addAttribute("image/" + image.options.format, "type")}>`;
  })} <img${addAttribute(fallbackImage.src, "src")}${spreadAttributes(additionalAttributes)}${spreadAttributes(fallbackImage.attributes)}> </picture>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/node_modules/astro/components/Picture.astro", void 0);

const imageConfig = {"service":{"entrypoint":"astro/assets/services/sharp","config":{}},"domains":[],"remotePatterns":[]};
					new URL("file:///Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/dist/");
					const getImage = async (options) => await getImage$1(options, imageConfig);

function l() {
  return globalThis.sanityClientInstance || console.error(
    "[@sanity/astro]: sanityClientInstance has not been initialized correctly"
  ), globalThis.sanityClientInstance;
}

const sanityClient = l();
const imageBuilder = imageUrlBuilder(sanityClient);
function urlForImage(source) {
  return imageBuilder.image(source);
}
function getFullUrl(baseUrl, slug) {
  return baseUrl + "/" + slug;
}
async function getHubPost(slug) {
  const query = groq`*[_type == "post" && slug.current =="` + slug + `"][0]`;
  const hubPost = await sanityClient.fetch(query);
  const authorQuery = groq`*[_type == "author" && _id =="` + hubPost.author._ref + `"][0]`;
  const author = await sanityClient.fetch(authorQuery);
  hubPost.author = author;
  const categoryQuery = groq`*[_type == "category" && _id =="` + hubPost.category._ref + `"][0]`;
  const category = await sanityClient.fetch(categoryQuery);
  const categoryImage = urlForImage(category.mainImage);
  hubPost.category = category;
  hubPost.category.mainImage = categoryImage.url();
  return hubPost;
}
async function getReadingTime(type, slug) {
  let textType = "";
  if (type == "article") {
    textType = "contentHtml";
  } else {
    textType = "bodyText";
  }
  const query = groq`*[
        _type == "` + type + `" &&
        slug.current == "` + slug + `"
      ]{
        "estimatedReadingTime": round(length(pt::text(` + textType + `)) / 5 / 180 )
      }[0]`;
  const readingTime = await sanityClient.fetch(query);
  if (readingTime.estimatedReadingTime == "0") {
    return "1";
  }
  return readingTime.estimatedReadingTime;
}
async function getCategoryPosts(categoryId) {
  const query = groq`*[_type == "post" && category._ref =="` + categoryId + `"]`;
  const hubPosts = await sanityClient.fetch(query);
  return hubPosts;
}
async function getCategoryPostsWithLimit(categoryId) {
  const query = groq`*[_type == "post" && category._ref =="` + categoryId + `"]  | order(category._ref asc)[0...6]`;
  const hubPosts = await sanityClient.fetch(query);
  return hubPosts;
}
async function getAllTopics() {
  const query = groq`*[_type == "topic"]  | order(order asc)`;
  const topics = await sanityClient.fetch(query);
  return topics;
}
async function getHelpCentreTopic(ref) {
  const query = groq`*[_type == "topic" && _id =="` + ref + `"][0]`;
  const topics = await sanityClient.fetch(query);
  return topics;
}
async function getHelpArticlesByTopic(topicId) {
  const query = groq`*[_type == "article"  && topic._ref =="` + topicId + `"]`;
  const articles = await sanityClient.fetch(query);
  return articles;
}
async function getPopularHelpArticles() {
  const query = groq`*[_type == "article"][0..7]`;
  const articles = await l().fetch(query);
  return articles;
}
async function getHelpArticle(slug) {
  const query = groq`*[_type == "article" && slug.current =="` + slug + `"][0]`;
  const articles = await l().fetch(query);
  return articles;
}
async function getRelatedHelpArticles(topicId, slug) {
  const query = groq`*[_type == "article"  && topic._ref =="` + topicId + `" && slug.current != "` + slug + `"]`;
  const articles = await l().fetch(query);
  return articles;
}
async function getTopicBySlug(slug) {
  const query = groq`*[_type == "topic" && slug.current =="` + slug + `"][0]`;
  const topic = await l().fetch(query);
  return topic;
}

const $$Astro$c = createAstro("https://www.uown.co");
const $$AllHelpTopicsBar = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$c, $$props, $$slots);
  Astro2.self = $$AllHelpTopicsBar;
  const topics = await getAllTopics();
  const { isSticky, hasBorder, selectedTopic } = Astro2.props;
  const divClass = "hidden w-[280px] max-h-[620px] lg:flex lg:gap-y-8 shrink-0 flex-col text-left p-8 rounded-cmd bg-white " + (isSticky ? " sticky top-24" : "") + (hasBorder ? " border-blk" : " topics-shadow");
  return renderTemplate`${maybeRenderHead()}<div${addAttribute(divClass, "class")} data-astro-cid-lbkq32wh> ${topics.map((topic) => selectedTopic == topic.name ? renderTemplate`<a class="text-xl tracking-normal font-bold"${addAttribute(getFullUrl(Astro2.url.origin, topic.full_slug), "href")} data-astro-cid-lbkq32wh> ${topic.name} </a>` : renderTemplate`<a class="text-xl tracking-normal font-medium"${addAttribute(getFullUrl(Astro2.url.origin, topic.full_slug), "href")} data-astro-cid-lbkq32wh> ${topic.name} </a>`)} </div> `;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/AllHelpTopicsBar.astro", void 0);

const $$Astro$b = createAstro("https://www.uown.co");
const $$ArticleTile = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$b, $$props, $$slots);
  Astro2.self = $$ArticleTile;
  const { readingTime, postHref, title, sectionType } = Astro2.props;
  const borderClass = "bg-white flex flex-col justify-between h-[130px] md:h-[168px] lg:h-[200px] p-4 md:p-6 rounded-cmd " + (sectionType == "topic" ? "shadow-articletile" : "border-blk");
  return renderTemplate`${maybeRenderHead()}<a class="grow flex flex-col flex-wrap w-full md:w-[306px] lg:w-[389px] mb-6 md:ml-6 text-left transform hover:scale-95 transition duration-300"${addAttribute(postHref, "href")}> <div${addAttribute(borderClass, "class")}> <p class="lg:text-2xl md:text-xl text-lg tracking-normal font-bold">${title}</p> <p class="lg:text-lg md:text-base text-sm tracking-wide font-medium"> ${readingTime} min read<span class="pl-3">→</span></p> </div> </a>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/ArticleTile.astro", void 0);

let count$1 = 0;
const SampleImageComponent = (children) => {
  const value = children.value;
  const url = urlForImage(value).url();
  return /* @__PURE__ */ jsx("img", { className: "py-6 w-full", src: url, loading: "lazy" });
};
const components$1 = {
  types: {
    image: SampleImageComponent,
    youtube: (node) => {
      const url = node.value.url;
      return /* @__PURE__ */ jsx("iframe", { allow: "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share", title: 'UOWN Testimonial | Tracey - "The Buy-To-Let Investor"', width: "100%", height: "432px", src: url });
    }
  },
  marks: {
    // Ex. 1: custom renderer for the em / italics decorator
    em: ({ children }) => /* @__PURE__ */ jsx("em", { className: "text-gray-600 font-semibold", children }),
    internalLink: ({ value, children }) => {
      const target = (value?.href || "").startsWith("http") ? "_blank" : void 0;
      return /* @__PURE__ */ jsx("a", { href: value?.href, target, rel: target === "_blank" && "noindex nofollow", children });
    },
    // Ex. 2: rendering a custom `link` annotation
    link: ({ value, children }) => {
      const target = (value?.href || "").startsWith("http") ? "_blank" : void 0;
      return /* @__PURE__ */ jsx("a", { href: value?.href, target, rel: target === "_blank" && "noindex nofollow", children });
    }
  },
  block: {
    // Ex. 1: customizing common block types
    h1: ({ children }) => /* @__PURE__ */ jsx("h1", { className: "text-2xl lg:text-3xl tracking-normal font-bold text-left", children }),
    h2: ({ children }) => /* @__PURE__ */ jsx("div", { id: "h" + count$1++, children: /* @__PURE__ */ jsx("h2", { className: "text-2xl lg:text-3xl tracking-normal font-bold pt-12 pb-3 text-left", children }) }),
    h3: ({ children }) => /* @__PURE__ */ jsx("h3", { className: "text-xl tracking-normal font-regular pb-6 text-left", children }),
    h4: ({ children }) => /* @__PURE__ */ jsx("h4", { className: "text-xl tracking-normal font-regular pb-6 text-left", children }),
    h5: ({ children }) => /* @__PURE__ */ jsx("h5", { className: "text-xl tracking-normal font-regular pb-6 text-left", children }),
    h6: ({ children }) => /* @__PURE__ */ jsx("h6", { className: "text-xl tracking-normal font-regular pb-6 text-left", children }),
    normal: ({ children }) => /* @__PURE__ */ jsx("p", { className: "text-base lg:text-lg tracking-normal font-regular pb-6 text-left", children }),
    blockquote: ({ children }) => /* @__PURE__ */ jsx("blockquote", { className: "border-l-purple-500", children }),
    // Ex. 2: rendering custom styles
    customHeading: ({ children }) => /* @__PURE__ */ jsx("h2", { className: "text-lg text-primary text-purple-700", children })
  },
  list: {
    // Ex. 1: customizing common list types
    bullet: ({ children }) => /* @__PURE__ */ jsx("ul", { className: "text-left pl-4 md:pl-14 lg:pl-24 text-base lg:text-lg tracking-normal font-regular py-6", children }),
    number: ({ children }) => /* @__PURE__ */ jsx("ol", { className: "text-left text-xl tracking-normal font-regular pt-6", children }),
    // Ex. 2: rendering custom lists
    checkmarks: ({ children }) => /* @__PURE__ */ jsx("ol", { className: "m-auto text-lg", children })
  },
  listItem: {
    // Ex. 1: customizing common list types
    bullet: ({ children }) => /* @__PURE__ */ jsx("li", { style: { listStyleType: "disc" }, children }),
    number: ({ children }) => /* @__PURE__ */ jsx("ol", { className: "text-xl tracking-normal font-regular pt-6", children }),
    // Ex. 2: rendering custom list items
    checkmarks: ({ children }) => /* @__PURE__ */ jsxs("li", { children: [
      "✅ ",
      children
    ] })
  }
};
const content$1 = (post) => {
  let value, postContent = post.post;
  if (postContent.bodyText) {
    value = postContent.bodyText;
  } else {
    value = postContent.contentHtml;
  }
  count$1 = 0;
  return /* @__PURE__ */ jsx("div", { children: /* @__PURE__ */ jsx(PortableText, { value, components: components$1 }) });
};

const ContactUs = {"src":"/_astro/contact-us.d6cec8de.png","width":512,"height":512,"format":"png"};

const $$Astro$a = createAstro("https://www.uown.co");
const $$slug$2 = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$a, $$props, $$slots);
  Astro2.self = $$slug$2;
  const { slug } = Astro2.params;
  const helpArticle = await getHelpArticle(slug);
  if (!helpArticle) {
    return Astro2.redirect("/404");
  }
  const helpTopic = await getHelpCentreTopic(helpArticle.topic._ref);
  const relatedArticles = await getRelatedHelpArticles(
    helpArticle.topic._ref,
    slug
  );
  const purl = Astro2.url.origin + "/previews/help-center.png";
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Help Centre | Support & FAQs | UOWN", "classes": "", "description": helpArticle.seoDescription, "purl": purl, "data-astro-cid-mehffs7l": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="" data-astro-cid-mehffs7l> <section class="pb-6" data-astro-cid-mehffs7l> <div class="text-center max-w-7xl mx-auto" data-astro-cid-mehffs7l> <p class="hidden md:block max-w-4xl lg:text-5xl md:text-4xl text-3xl tracking-normal font-extrabold py-24 mx-auto" data-astro-cid-mehffs7l> ${helpTopic.name} </p> <div class="flex px-6 md:px-20 lg:px-36 pb-12 pt-12 md:pt-0 lg:pb-40 gap-x-10" data-astro-cid-mehffs7l> ${renderComponent($$result2, "TopicsBar", $$AllHelpTopicsBar, { "isSticky": true, "hasBorder": true, "selectedTopic": helpTopic.name, "data-astro-cid-mehffs7l": true })} <div class="flex flex-col justify-left mb-auto lg:max-w-[78%] gap-y-6" data-astro-cid-mehffs7l> <div class="text-left text-lg lg:text-2xl font-bold" data-astro-cid-mehffs7l> ${helpArticle.title} </div> ${renderComponent($$result2, "Serializer", content$1, { "client:load": true, "post": helpArticle, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/BlockSerializer.jsx", "client:component-export": "default", "data-astro-cid-mehffs7l": true })} </div> </div> </div> </section> <section class="text-left px-6 md:px-20 lg:px-36 pb-24 pt-12 md:pt-24 lg:pb-40 bg-gray" data-astro-cid-mehffs7l> <div class="max-w-7xl mx-auto" data-astro-cid-mehffs7l> <p class="hidden lg:block max-w-4xl text-3xl tracking-normal font-extrabold pb-24" data-astro-cid-mehffs7l>
Related help articles:
</p> <p class="lg:hidden max-w-4xl text-2xl tracking-normal font-extrabold pb-12 mx-auto" data-astro-cid-mehffs7l>
Take a look at some of our other articles:
</p> <div class="flex flex-wrap justify-left pb-12" data-astro-cid-mehffs7l> ${relatedArticles.map((relatedArticle) => renderTemplate`${renderComponent($$result2, "ArticleTile", $$ArticleTile, { "readingTime": getReadingTime(
    "article",
    relatedArticle.slug.current
  ), "postHref": getFullUrl(
    Astro2.url.origin,
    relatedArticle.fullSlug
  ), "title": relatedArticle.title, "sectionType": "topic", "data-astro-cid-mehffs7l": true })}`)} </div> <div class="flex flex-col md:flex-row items-center justify-between w-full md:h-[100px] bg-mint-500 rounded-cmd text-white py-6 px-5 lg:px-12" data-astro-cid-mehffs7l> <div class="flex flex-col md:flex-row items-center" data-astro-cid-mehffs7l> <img class="md:mr-5 lg:mr-14 mb-8 md:mb-0 md:h-[60px] md:w-[60px]"${addAttribute(ContactUs.src, "src")} width="100" height="100" alt="" data-astro-cid-mehffs7l> <div class="lg:text-2xl text-xl tracking-normal font-bold pb-6 md:pb-0 text-center" data-astro-cid-mehffs7l>
Have more questions?
<p class="lg:text-xl text-base font-light" data-astro-cid-mehffs7l>
Don't hesitate to get in touch
</p> </div> </div> <a href="https://app.uown.co/contact" data-astro-cid-mehffs7l>${renderComponent($$result2, "Button", $$Button, { "type": "button", "color": "btn-white btn-email", "text": "Email us", "data-astro-cid-mehffs7l": true })}</a> </div> </div> </section> </main> ` })} `;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/help-centre/[slug].astro", void 0);

const $$file$2 = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/help-centre/[slug].astro";
const $$url$2 = "/help-centre/[slug]";

const _slug_$2 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug$2,
  file: $$file$2,
  url: $$url$2
}, Symbol.toStringTag, { value: 'Module' }));

const $$Astro$9 = createAstro("https://www.uown.co");
const $$CallToAction = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$9, $$props, $$slots);
  Astro2.self = $$CallToAction;
  const { className, text1, text2, imgsrc } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<section${addAttribute(className, "class")}> <div class="heading mx-auto text-center max-w-2xl  lg:px-0 px-11"> <p class="text-3xl md:text-4xl lg:text-5xl tracking-normal font-extrabold"> ${text1} </p> <p class="text-3xl md:text-4xl lg:text-5xl tracking-normal font-extrabold pb-12"> ${text2} </p> <a href="https://app.uown.co/register">${renderComponent($$result, "Button", $$Button, { "type": "button", "color": "btn-black btn-get-started", "text": "Get Started" })}</a> </div> ${renderComponent($$result, "Image", $$Image, { "src": imgsrc, "class": "mx-auto pt-24 md:max-w-xl", "alt": "" })} </section>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/CallToAction.astro", void 0);

const StartJourney = {"src":"/_astro/start-journey-cta.6403c8d7.png","width":637,"height":528,"format":"png"};

const $$Astro$8 = createAstro("https://www.uown.co");
const $$StartJourneyCTA = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$8, $$props, $$slots);
  Astro2.self = $$StartJourneyCTA;
  const { colorClass } = Astro2.props;
  const className = colorClass + " py-40";
  return renderTemplate`${renderComponent($$result, "CTA", $$CallToAction, { "className": className, "text1": "Start your UOWN", "text2": "investment journey", "imgsrc": StartJourney })}`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/StartJourneyCTA.astro", void 0);

const $$Astro$7 = createAstro("https://www.uown.co");
const $$HelpHeroSection = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$7, $$props, $$slots);
  Astro2.self = $$HelpHeroSection;
  const { isIpadHidden } = Astro2.props;
  const sectionClass = "hero h-[88vh] bg-center bg-no-repeat bg-cover flex-col justify-center items-center text-center lg:px-0 px-11 " + (isIpadHidden == true ? "hidden lg:flex" : "flex");
  return renderTemplate`${maybeRenderHead()}<section${addAttribute(sectionClass, "class")} data-astro-cid-woqydxpj> <p class="max-w-4xl lg:text-7xl text-5xl tracking-normal font-extrabold pb-4" data-astro-cid-woqydxpj>
How can we help?
</p> <p class="max-w-2xl md:text-2xl text-xl tracking-normal font-regular pb-12" data-astro-cid-woqydxpj>
Our Help Centre is built to help answer any questions you might
    have.
</p> </section> `;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HelpHeroSection.astro", void 0);

const gotMoreQuestions = {"src":"/_astro/got_more_questions.5d5b7835.png","width":457,"height":444,"format":"png"};

const $$Astro$6 = createAstro("https://www.uown.co");
const $$GotMoreQuestions = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$6, $$props, $$slots);
  Astro2.self = $$GotMoreQuestions;
  const { isTopicPage } = Astro2.props;
  const sectionClass = "bg-gray-pages lg:py-52 py-12 " + (isTopicPage == true ? "hidden lg:block" : "");
  return renderTemplate`${maybeRenderHead()}<section${addAttribute(sectionClass, "class")}> <div class="max-w-7xl flex flex-wrap flex-row items-center mx-auto px-11"> <div class="flex flex-col grow bg-white shadow-tile rounded-clg text-center lg:text-left mx-auto py-12 px-10 lg:pl-12 lg:pr-20 order-2 lg:order-1"> <h5 class="text-3xl md:text-4xl lg:text-5xl tracking-normal font-extrabold pb-4">
Got any more questions?
</h5> <p class="text-base md:text-xl lg:text-2xl tracking-normal font-regular pb-12">
Dont hestitate to get in touch about your queries.
</p> <a href="https://app.uown.co/contact">${renderComponent($$result, "Button", $$Button, { "type": "button", "color": "btn-black btn-email", "text": "Email us" })}</a> </div> <img class="mx-auto lg:ml-0 order-1 lg:order-2 md:py-24 "${addAttribute(gotMoreQuestions.src, "src")} alt=""> </div> </section>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/GotMoreQuestions.astro", void 0);

const CircleBlack = {"src":"/_astro/img-quarter-circle-black.512323bc.png","width":489,"height":384,"format":"png"};

const SphereGreen = {"src":"/_astro/img-sphere-green.967c1dc0.png","width":424,"height":355,"format":"png"};

const SphereGold = {"src":"/_astro/img-sphere-half-gold.4984d7a9.png","width":589,"height":481,"format":"png"};

const $$Astro$5 = createAstro("https://www.uown.co");
const $$PopularArticles = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$5, $$props, $$slots);
  Astro2.self = $$PopularArticles;
  const popularHelpArticles = await getPopularHelpArticles();
  const { isHelpCentreMainPage } = Astro2.props;
  const sectionClass = "lg:py-64 md:py-24 py-12 pb-24 relative md:px-10 lg:px-20 lxl:px-36 xl:px-60 overflow-hidden" + (isHelpCentreMainPage == false ? " hidden lg:block" : "");
  return renderTemplate`${maybeRenderHead()}<section${addAttribute(sectionClass, "class")}> ${renderComponent($$result, "Image", $$Image, { "class": "absolute z-0 top-[5%] right-[92%] lg:w-56 lg:h-44 md:w-24 md:h-20", "width": "74", "height": "59", "src": CircleBlack, "alt": "" })} ${renderComponent($$result, "Image", $$Image, { "class": "absolute z-0 top-[20%] left-[80%] lg:w-40 lg:h-32 md:w-24 md:h-20", "width": "75", "height": "63", "src": SphereGreen, "alt": "" })} ${renderComponent($$result, "Image", $$Image, { "class": "absolute z-0 top-[85%] right-[20%] lg:w-40 lg:h-32 md:w-24 md:h-20", "width": "74", "height": "61", "src": SphereGold, "alt": "" })} <p class="max-w-4xl lg:text-5xl md:text-4xl text-3xl letterspacing-none font-extrabold md:pb-24 pb-12 text-center mx-auto">
Most Popular Articles
</p> <div class="flex flex-wrap justify-center p-7"> ${popularHelpArticles.map((helpArticle) => renderTemplate`${renderComponent($$result, "ArticleTile", $$ArticleTile, { "readingTime": getReadingTime("article", helpArticle.slug.current), "postHref": getFullUrl(Astro2.url.origin, helpArticle.fullSlug), "title": helpArticle.title, "sectionType": "" })}`)} </div> </section>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/PopularArticles.astro", void 0);

let count = 0;
const components = {
  types: {
    image: ({ children }) => "",
    youtube: ({ node }) => ""
  },
  marks: {
    // Ex. 1: custom renderer for the em / italics decorator
    em: ({ children }) => "",
    internalLink: ({ value, children }) => {
    },
    // Ex. 2: rendering a custom `link` annotation
    link: ({ value, children }) => {
    }
  },
  block: {
    // Ex. 1: customizing common block types
    h1: ({ children }) => "",
    h2: ({ children }) => /* @__PURE__ */ jsx("a", { href: "#h" + count++, className: "block text-lg tracking-wide font-regular pb-3", children }),
    h3: ({ children }) => "",
    h4: ({ children }) => "",
    h5: ({ children }) => "",
    h6: ({ children }) => "",
    normal: ({ children }) => "",
    blockquote: ({ children }) => "",
    // Ex. 2: rendering custom styles
    customHeading: ({ children }) => ""
  },
  list: {
    // Ex. 1: customizing common list types
    bullet: ({ children }) => "",
    number: ({ children }) => "",
    // Ex. 2: rendering custom lists
    checkmarks: ({ children }) => ""
  },
  listItem: {
    // Ex. 1: customizing common list types
    bullet: ({ children }) => "",
    number: ({ children }) => "",
    // Ex. 2: rendering custom list items
    checkmarks: ({ children }) => ""
  }
};
const content = (post) => {
  let value, postContent = post.post;
  if (postContent.bodyText) {
    value = postContent.bodyText;
  } else {
    value = postContent.contentHtml;
  }
  count = 0;
  return /* @__PURE__ */ jsx("div", { children: /* @__PURE__ */ jsx(PortableText, { value, components }) });
};

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(raw || cooked.slice()) }));
var _a;
const $$Astro$4 = createAstro("https://www.uown.co");
const $$TheHubJSONLD = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$4, $$props, $$slots);
  Astro2.self = $$TheHubJSONLD;
  const { title, summary, publishedAt, author, category } = Astro2.props;
  const schema = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": title,
    "description": summary,
    "image": [category && category.mainImage ? category.mainImage : ""],
    "author": {
      "@type": "Person",
      "name": author
    },
    /* the schema expects Date or DateTime using ISO 8601 format. For Date that is yyyy-MM-dd */
    "datePublished": publishedAt
  };
  return renderTemplate(_a || (_a = __template(['<script type="application/ld+json">', "<\/script>"])), unescapeHTML(JSON.stringify(schema)));
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/StructuredData/TheHubJSONLD.astro", void 0);

const TogetherWeAchieveMore = {"src":"/_astro/together-we-achieve-cta.01b47bb1.png","width":627,"height":499,"format":"png"};

const $$Astro$3 = createAstro("https://www.uown.co");
const $$TogetherWeAchieveCTA = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$3, $$props, $$slots);
  Astro2.self = $$TogetherWeAchieveCTA;
  const { colorClass } = Astro2.props;
  const className = colorClass + " lg:py-40";
  return renderTemplate`${renderComponent($$result, "CTA", $$CallToAction, { "className": className, "text1": "Together we", "text2": "achieve more", "imgsrc": TogetherWeAchieveMore })}`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/TogetherWeAchieveCTA.astro", void 0);

const Causes = {"src":"/_astro/hub-causes.39663272.png","width":614,"height":565,"format":"png"};

const Property = {"src":"/_astro/hub-property.57d20a93.png","width":650,"height":560,"format":"png"};

const Money = {"src":"/_astro/hub-money.13f2284a.png","width":544,"height":545,"format":"png"};

const Hl = {"src":"/_astro/hub-hl.0fe75a49.png","width":650,"height":532,"format":"png"};

const $$Astro$2 = createAstro("https://www.uown.co");
const $$HubCategories = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$2, $$props, $$slots);
  Astro2.self = $$HubCategories;
  const anchorClass = "h-full w-full flex flex-col justify-between p-3 lg:pl-10 lg:pt-12 lg:pb-6";
  const itemContainerClass = "flex shrink-0 w-[174px] h-[135px] lg:w-[389px] lg:h-[400px] mb-6 text-white rounded-cmd";
  const itemTitle = "lg:text-5xl text-2xl tracking-normal font-extrabold";
  return renderTemplate`${maybeRenderHead()}<section class="px-3 py-24 md:pb-12 lg:pt-40"> <p class="lg:text-5xl text-3xl tracking-normal font-extrabold pb-8 md:pb-12 text-center mx-auto">
Take a look at our other articles
</p> <div class="flex flex-wrap justify-center gap-x-4 md:gap-x-6 lg:px-20"> <div${addAttribute(itemContainerClass + " bg-mint-300", "class")}> <a${addAttribute(anchorClass, "class")}${addAttribute(Astro2.url.origin + "/category/property", "href")}> <p${addAttribute(itemTitle, "class")}>Property</p> ${renderComponent($$result, "Image", $$Image, { "class": "relative right-[30px] w-[240px] hidden lg:block", "src": Property, "alt": "" })} <p class="lg:hidden block text-sm tracking-widest font-medium">See articles  →</p> </a> </div> <div${addAttribute(itemContainerClass + " bg-navyblue-300", "class")}> <a${addAttribute(anchorClass, "class")}${addAttribute(Astro2.url.origin + "/category/money", "href")}> <p${addAttribute(itemTitle, "class")}>Money</p> ${renderComponent($$result, "Image", $$Image, { "class": "relative right-[30px] w-[240px] hidden lg:block", "src": Money, "alt": "" })} <p class="lg:hidden block text-sm tracking-widest font-medium">See articles  →</p> </a> </div> <div${addAttribute(itemContainerClass + " bg-yellow-300", "class")}> <a${addAttribute(anchorClass, "class")}${addAttribute(Astro2.url.origin + "/category/home-lifestyle", "href")}> <p${addAttribute(itemTitle, "class")}>Home & Lifestyle</p> ${renderComponent($$result, "Image", $$Image, { "class": "relative right-[30px] w-[240px] hidden lg:block", "src": Hl, "alt": "" })} <p class="lg:hidden block text-sm tracking-widest font-medium">See articles  →</p> </a> </div> <div${addAttribute(itemContainerClass + " bg-salmon-200", "class")}> <a${addAttribute(anchorClass, "class")}${addAttribute(Astro2.url.origin + "/category/causes", "href")}> <p${addAttribute(itemTitle, "class")}>Causes</p> ${renderComponent($$result, "Image", $$Image, { "class": "relative right-[30px] w-[240px] hidden lg:block", "src": Causes, "alt": "" })} <p class="lg:hidden block text-sm tracking-widest font-medium">See articles  →</p> </a> </div> </div> </section>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HubCategories.astro", void 0);

const $$Astro$1 = createAstro("https://www.uown.co");
const $$slug$1 = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$slug$1;
  const { slug } = Astro2.params;
  const hubPost = await getHubPost(slug);
  if (!hubPost) {
    return Astro2.redirect("/404");
  }
  const title = `${hubPost?.title} - UOWN`;
  const heading = hubPost.title;
  const summary = hubPost?.summary || "";
  const author = hubPost?.author.name || "";
  const category = hubPost?.category || "";
  const publishedAt = hubPost?.firstPublishedAt || "";
  const formattedPublishDate = new Date(publishedAt).toLocaleDateString("en-US", {
    year: "2-digit",
    month: "numeric",
    day: "numeric"
  });
  const purl = Astro2.url.origin + "/previews/hub.png";
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": title, "classes": "", "description": "Stay updated with UOWN's investment news and articles. Visit The Hub for the latest insights.", "purl": purl }, { "default": async ($$result2) => renderTemplate`  ${maybeRenderHead()}<main> <section class="flex lg:flex-row flex-col lg:justify-around lg:items-center justify-start text-left lg:px-20 md:px-16 px-10 lg:py-64 md:pt-40 pt-24 lg:gap-x-20"> <div class="max-w-2xl lg:mx-auto"> <p class="lg:text-5xl text-3xl tracking-normal font-extrabold pb-6"> ${heading} </p> <p class="lg:text-2xl text-lg tracking-normal font-regular pb-4"> ${summary} </p> <p class="lg:text-xl text-lg tracking-wide font-bold">
By ${author} <span class="text-gray-100 font-light pl-3"> ${formattedPublishDate}</span> </p> </div> <img class="block lg:py-0 lg:mx-auto lg:max-w-[500px] max-w-[339px] py-12"${addAttribute(category.mainImage, "src")} alt=""> </section> <section class="flex justify-start lg:justify-center lg:mx-20 lg:gap-x-20 lg:px-0 md:px-16 px-10"> <div class="lg:max-w-5xl max-w-3xl"> <!-- {JSON.stringify(hubPost)} --> ${renderComponent($$result2, "Serializer", content$1, { "client:load": true, "post": hubPost, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/BlockSerializer.jsx", "client:component-export": "default" })} </div> <aside class="hidden lg:block"> <div class="flex flex-col shrink-0 w-[389px] sticky top-44 p-6 rounded-cmd border-blk"> <p class="text-2xl tracking-normal font-extrabold pb-6">
Article Contents
</p> <div>${renderComponent($$result2, "H2Serializer", content, { "client:load": true, "post": hubPost, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/H2Serializer.jsx", "client:component-export": "default" })}</div> <div class="flex gap-x-2 py-12"> <!-- <SocialShareIcon client:load  /> --> </div> <a href="https://app.uown.co/signup">${renderComponent($$result2, "Button", $$Button, { "type": "button", "color": "btn-black w-full", "text": "Get Investing" })}</a> </div> </aside> </section> ${renderComponent($$result2, "HubCategories", $$HubCategories, {})} ${renderComponent($$result2, "TogetherWeAchieveCTA", $$TogetherWeAchieveCTA, { "colorClass": "bg-grey-gradient" })} </main>  `, "structuredData": async ($$result2) => renderTemplate`${renderComponent($$result2, "TheHubJSONLD", $$TheHubJSONLD, { "title": title, "summary": summary, "publishedAt": publishedAt, "author": author, "category": category, "slot": "structuredData" })}` })}`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/the-hub/[slug].astro", void 0);

const $$file$1 = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/the-hub/[slug].astro";
const $$url$1 = "/the-hub/[slug]";

const _slug_$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug$1,
  file: $$file$1,
  url: $$url$1
}, Symbol.toStringTag, { value: 'Module' }));

const $$Astro = createAstro("https://www.uown.co");
const $$slug = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$slug;
  const { slug } = Astro2.params;
  const selectedTopic = await getTopicBySlug(slug);
  if (!selectedTopic) {
    return Astro2.redirect("/404");
  }
  const helpArticles = await getHelpArticlesByTopic(selectedTopic._id);
  await getPopularHelpArticles();
  const purl = Astro2.url.origin + "/previews/hub.png";
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Flexible Property Investment and Finance | UOWN", "classes": "", "description": "UOWN is the new way to invest in and finance property, all made possible by our property crowdfunding platform. Click to invest or secure finance now.", "purl": purl }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main> ${renderComponent($$result2, "HeroSection", $$HelpHeroSection, { "isIpadHidden": true })} <section class="bg-gray-pages text-center"> <p class="max-w-4xl lg:text-5xl md:text-4xl text-3xl tracking-normal font-extrabold py-12 md:py-24 mx-auto"> ${selectedTopic.name} </p> <div class="flex md:px-20 lg:px-36 pb-24"> ${renderComponent($$result2, "TopicsBar", $$AllHelpTopicsBar, { "isSticky": false, "hasBorder": false, "selectedTopic": selectedTopic.name })} <div class="flex flex-wrap w-full lg:max-w-[78%] mb-auto lg:justify-start justify-center p-4"> ${helpArticles.map((article) => renderTemplate`${renderComponent($$result2, "ArticleTile", $$ArticleTile, { "readingTime": getReadingTime("article", article.slug.current), "postHref": getFullUrl(Astro2.url.origin, article.fullSlug), "title": article.title, "sectionType": "topic" })}`)} </div> </div> </section> ${renderComponent($$result2, "PopularArticles", $$PopularArticles, { "isHelpCentreMainPage": false })} ${renderComponent($$result2, "MoreQuestions", $$GotMoreQuestions, { "isTopicPage": true })} ${renderComponent($$result2, "StartJourneyCTA", $$StartJourneyCTA, { "colorClass": "hidden lg:block" })} </main> ` })}`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/topic/[slug].astro", void 0);

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/topic/[slug].astro";
const $$url = "/topic/[slug]";

const _slug_ = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

export { $$StartJourneyCTA as $, Causes as C, Hl as H, Money as M, Property as P, _slug_$2 as _, getReadingTime as a, getFullUrl as b, $$Image as c, getHelpArticlesByTopic as d, getAllTopics as e, $$HelpHeroSection as f, getCategoryPosts as g, $$AllHelpTopicsBar as h, imageConfig as i, $$ArticleTile as j, $$PopularArticles as k, $$GotMoreQuestions as l, $$CallToAction as m, getCategoryPostsWithLimit as n, _slug_$1 as o, _slug_ as p, urlForImage as u };

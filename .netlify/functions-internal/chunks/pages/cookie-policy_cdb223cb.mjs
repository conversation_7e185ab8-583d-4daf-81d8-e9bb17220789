import { $ as $$Layout } from './404_1f20d35e.mjs';
import { e as createAstro, f as createComponent, r as renderTemplate, m as maybeRenderHead, i as renderComponent, g as addAttribute, h as renderSlot, _ as __astro_tag_component__, F as Fragment, l as createVNode } from '../astro_ca9e373b.mjs';
import { c as $$Image } from './_slug__d49fd2f5.mjs';
/* empty css                                   */import 'clsx';

const HalfCylinder = {"src":"/_astro/img-half-cylinder.d279f373.png","width":466,"height":260,"format":"png"};

const HalfCircle = {"src":"/_astro/img-half-circle.1f901c7a.png","width":491,"height":411,"format":"png"};

const QuarterCircle = {"src":"/_astro/img-large-quarter-circle-green.49e30dd9.png","width":428,"height":486,"format":"png"};

const Screw = {"src":"/_astro/img-large-screw.835a33e1.png","width":533,"height":411,"format":"png"};

const SphereBlack = {"src":"/_astro/img-large-sphere-black.b510b10b.png","width":592,"height":478,"format":"png"};

const SphereGold = {"src":"/_astro/img-sphere-half-gold.4984d7a9.png","width":589,"height":481,"format":"png"};

const Squircle = {"src":"/_astro/img-large-squircle.6b234248.png","width":759,"height":601,"format":"png"};

const $$Astro$2 = createAstro("https://www.uown.co");
const $$PolicyLayout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$2, $$props, $$slots);
  Astro2.self = $$PolicyLayout;
  const { heading, subheading } = Astro2.props;
  const className = "btn rounded-full bg-black-100 px-4 py-2.5 md:px-6 md:py-4 text-base md:text-lg font-bold text-white tracking-wide mb-7";
  const classSelected = "btn rounded-full bg-mint-300 px-4 py-2.5 md:px-6 md:py-4 text-base md:text-lg font-bold text-black tracking-wide mb-7";
  return renderTemplate`${maybeRenderHead()}<div class="main py-16 pt-40 lg:mt-32 lg:pt-32 lg:pb-40 relative overflow-hidden z-1 lg:px-0 px-8" data-astro-cid-jxsyxwfh> ${renderComponent($$result, "Image", $$Image, { "class": "sgold absolute z-0 lg:w-52 lg:h-44 md:w-28 md:h-28", "width": "99", "height": "82", "src": SphereGold, "'": true, "alt": "", "data-astro-cid-jxsyxwfh": true })} ${renderComponent($$result, "Image", $$Image, { "class": "qcircle absolute z-0 lg:w-52 lg:h-48 md:w-36 md:h-28", "width": "68", "height": "77", "src": QuarterCircle, "alt": "", "data-astro-cid-jxsyxwfh": true })} ${renderComponent($$result, "Image", $$Image, { "class": "squircle absolute z-0", "width": "486", "height": "384", "src": Squircle, "alt": "", "data-astro-cid-jxsyxwfh": true })} ${renderComponent($$result, "Image", $$Image, { "class": "hcircle absolute z-0", "width": "121", "height": "100", "src": HalfCircle, "alt": "", "data-astro-cid-jxsyxwfh": true })} ${renderComponent($$result, "Image", $$Image, { "class": "screw absolute z-0", "width": "354", "height": "273", "src": Screw, "alt": "", "data-astro-cid-jxsyxwfh": true })} ${renderComponent($$result, "Image", $$Image, { "class": "sblack absolute z-0", "width": "103", "height": "83", "src": SphereBlack, "alt": "", "data-astro-cid-jxsyxwfh": true })} ${renderComponent($$result, "Image", $$Image, { "class": "hcylinder absolute z-0", "width": "272", "height": "151", "src": HalfCylinder, "alt": "", "data-astro-cid-jxsyxwfh": true })} <div class="container max-w-5xl mx-auto relative" data-astro-cid-jxsyxwfh> <h1 class="text-4xl md:text-5xl text-center tracking-normal font-extrabold" data-astro-cid-jxsyxwfh>${heading}</h1> <p class="text-lg md:text-xl lg:text-2xl text-center tracking-wide font-regular max-w-lg mx-auto mt-3 md:mt-6 mb-8 md:mb-24" data-astro-cid-jxsyxwfh>${subheading}</p> <div class="max-w-3xl mx-auto flex flex-wrap justify-center justify-items-center gap-x-8 lg:pb-40 pb-24" data-astro-cid-jxsyxwfh> <a href="/terms-and-conditions"${addAttribute(Astro2.url.pathname === "/terms-and-conditions" ? classSelected : className, "class")} data-astro-cid-jxsyxwfh>Terms &amp; Conditions</a> <a href="/privacy-policy"${addAttribute(Astro2.url.pathname === "/privacy-policy" ? classSelected : className, "class")} data-astro-cid-jxsyxwfh>Privacy Policy</a> <a href="/cookie-policy"${addAttribute(Astro2.url.pathname === "/cookie-policy" ? classSelected : className, "class")} data-astro-cid-jxsyxwfh>Cookies Policy</a> <a href="https://mangopay.com/terms/payment-services_EN_2024_02.pdf"${addAttribute(className, "class")} target="_blank" data-astro-cid-jxsyxwfh>MangoPay T&Cs</a> <a href="/risk-statement"${addAttribute(Astro2.url.pathname === "/risk-statement" ? classSelected : className, "class")} data-astro-cid-jxsyxwfh>Risk Statement</a> </div> <div class="content max-w-5xl lg:mx-auto bg-white mx-8 md:mx-20 px-6 py-4 md:px-11 md:py-9 lg:px-16 lg:py-12 z-1 space-y-4" data-astro-cid-jxsyxwfh> ${renderSlot($$result, $$slots["default"])} </div> </div> </div> `;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/PolicyLayout.astro", void 0);

const $$Astro$1 = createAstro("https://www.uown.co");
const $$PoliciesPara = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$PoliciesPara;
  const { text } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<p class="text-sm tracking-widest md:text-base md:tracking-wider lg:text-xl lg:tracking-normal font-regular">${text}</p>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/PoliciesPara.astro", void 0);

const $$Astro = createAstro("https://www.uown.co");
const $$PolicyHeadings = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$PolicyHeadings;
  const { text } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<p class="text-xl lg:text-3xl md:text-2xl tracking-normal font-bold mb-3 pt-12">${text}</p>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/PolicyHeadings.astro", void 0);

const frontmatter = {
  "title": "Cookie Policy",
  "description": "Our site uses cookies. This helps us to provide you with a good experience when you browse and also allows us to improve our site. Click to find out more."
};
function getHeadings() {
  return [];
}
const __usesAstroImage = true;
function _createMdxContent(props) {
  return createVNode($$Layout, {
    title: "Cookie Policy | Data & Privacy | UOWN",
    classes: "bg-gray",
    description: "Understand UOWN's cookie policy and how we protect your data and privacy online.",
    purl: "",
    children: createVNode($$PolicyLayout, {
      heading: "Cookie Policy",
      subheading: "Information about our use of cookies.",
      children: [createVNode($$PoliciesPara, {
        text: "Our Site uses cookies to distinguish you from other users of our Site. This helps us to provide you with a good experience when you browse our Site and also allows us to improve our Site. By continuing to browse the Site, you are agreeing to our use of Cookies."
      }), createVNode($$PoliciesPara, {
        text: "A Cookie is a small file of letters and numbers that we store on your browser or the hard drive of your computer if you agree. Cookies contain information that is transferred to your computer's hard drive."
      }), createVNode($$PoliciesPara, {
        text: "We use the following Cookies:"
      }), createVNode("ul", {
        class: "list-disc pl-5",
        children: [createVNode("li", {
          children: createVNode($$PoliciesPara, {
            text: "Strictly necessary Cookies: These are Cookies that are required for the operation of our Site. They include, for example, Cookies that enable you to log into secure areas of our Site, use a shopping cart or make use of e-billing services."
          })
        }), createVNode("li", {
          children: createVNode($$PoliciesPara, {
            text: "Analytical/performance Cookies: They allow us to recognise and count the number of visitors and to see how visitors move around our Site when they are using it. This helps us to improve the way our Site works, for example, by ensuring that users are finding what they are looking for easily."
          })
        }), createVNode("li", {
          children: createVNode($$PoliciesPara, {
            text: "Functionality Cookies: These are used to recognise you when you return to our Site. This enables us to personalise our content for you, greet you by name and remember your preferences (for example, your choice of language or region)."
          })
        }), createVNode("li", {
          children: createVNode($$PoliciesPara, {
            text: "Targeting Cookies: These cookies record your visit to our Site, the pages you have visited, and the links you have followed. We will use this information to make our Site and the advertising displayed on it more relevant to your interests. We may also share this information with third parties for this purpose."
          })
        }), createVNode("li", {
          children: createVNode("div", {
            children: createVNode($$PoliciesPara, {
              text: "You can block Cookies by activating the setting on your browser that allows you to refuse the setting of all or some Cookies. However, if you use your browser settings to block all Cookies (including essential Cookies) you may not be able to access all or parts of our Site. To find out more about Cookies, including how to see what Cookies have been set and how to manage and delete them, visit allaboutcookies.org."
            })
          })
        })]
      }), createVNode($$PoliciesPara, {
        text: "You can find more information about the individual cookies we use and the purposes for which we use them as follows:"
      }), createVNode($$PolicyHeadings, {
        text: "Session Cookies"
      }), createVNode($$PoliciesPara, {
        text: "We use a session Cookie to remember your login for you and your Membership status. These we deem strictly necessary to the working of our Site. If these are disabled then various functionalities on our Site will be broken."
      }), createVNode($$PolicyHeadings, {
        text: "Google Analytics"
      }), createVNode("div", {
        children: createVNode("p", {
          class: "text-sm tracking-widest md:text-base md:tracking-wider lg:text-xl lg:tracking-normal font-regular",
          children: [createVNode("span", {
            children: "We use this to understand how the website is being used in order to improve the user experience. To opt out of being tracked by Google Analytics across all websites visit"
          }), "\n", createVNode("a", {
            style: "color:#71E5BD;",
            href: "http://tools.google.com/dlpage/gaoptout",
            target: "_blank",
            rel: "noreferrer",
            children: "tools.google.com/dlpage/gaoptout"
          })]
        })
      }), createVNode($$PoliciesPara, {
        text: "Please note that third parties (including, for example, advertising networks and providers of external services like our payment gateway and payment management services) may also use Cookies, over which we have no control. These Cookies are likely to be analytical/performance Cookies or targeting Cookies."
      })]
    })
  });
}
function MDXContent(props = {}) {
  const {
    wrapper: MDXLayout
  } = props.components || {};
  return MDXLayout ? createVNode(MDXLayout, {
    ...props,
    children: createVNode(_createMdxContent, {
      ...props
    })
  }) : _createMdxContent();
}

__astro_tag_component__(getHeadings, "astro:jsx");
__astro_tag_component__(MDXContent, "astro:jsx");
const url = "/cookie-policy";
const file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/cookie-policy.mdx";
const Content = (props = {}) => MDXContent({
											...props,
											components: { Fragment, ...props.components, "astro-image":  props.components?.img ?? $$Image },
										});
Content[Symbol.for('mdx-component')] = true;
Content[Symbol.for('astro.needsHeadRendering')] = !Boolean(frontmatter.layout);
Content.moduleId = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/cookie-policy.mdx";

const cookiePolicy = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  Content,
  __usesAstroImage,
  default: Content,
  file,
  frontmatter,
  getHeadings,
  url
}, Symbol.toStringTag, { value: 'Module' }));

export { $$PolicyLayout as $, $$PolicyHeadings as a, $$PoliciesPara as b, cookiePolicy as c };

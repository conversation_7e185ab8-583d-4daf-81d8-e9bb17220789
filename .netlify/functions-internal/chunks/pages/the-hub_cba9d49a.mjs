import { $ as $$Layout } from './404_1f20d35e.mjs';
import { e as createAstro, f as createComponent, r as renderTemplate, i as renderComponent, m as maybeRenderHead, g as addAttribute } from '../astro_ca9e373b.mjs';
import 'clsx';
import { m as $$CallToAction, n as getCategoryPostsWithLimit, b as getFullUrl, P as Property, a as getReadingTime, u as urlForImage, M as Money, H as Hl, C as Causes } from './_slug__314c3ce5.mjs';
import { $ as $$PostTile } from './causes_298a8449.mjs';
/* empty css                             *//* empty css                            */import '@sanity/client';
import 'react/jsx-runtime';
import 'motion/react';
import 'react';
import '@headlessui/react';
import '@heroicons/react/24/outline';
import '@heroicons/react/20/solid';
/* empty css                            */import 'html-escaper';
import '@astrojs/internal-helpers/path';
/* empty css                            */import '@portabletext/react';
import '../astro-assets-services_967ef4fc.mjs';
/* empty css                                 */import '@sanity/image-url';
import 'groq';
/* empty css                                 *//* empty css                            */
const Unleash = {"src":"/_astro/unleash-the-power-cta.b402b604.png","width":681,"height":553,"format":"png"};

const $$Astro$2 = createAstro("https://www.uown.co");
const $$UnleashThePowerCTA = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$2, $$props, $$slots);
  Astro2.self = $$UnleashThePowerCTA;
  const { colorClass } = Astro2.props;
  const className = colorClass + " py-40";
  return renderTemplate`${renderComponent($$result, "CTA", $$CallToAction, { "className": className, "text1": "Unleash the power", "text2": "of our crowd", "imgsrc": Unleash })}`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/UnleashThePowerCTA.astro", void 0);

const $$Astro$1 = createAstro("https://www.uown.co");
const $$HubCard = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$HubCard;
  const { title, postHref, imgSrc, colorClass } = Astro2.props;
  const cardClass = "z-10 flex shrink-0 w-full md:w-[223px] h-[150px] md:h-[300px] lg:w-[389px] lg:h-[400px] text-white rounded-cmd mb-4 " + colorClass;
  const imgClass = "hidden md:block relative right-[40px] lg:right-[30px] w-[170px] lg:w-[240px]";
  return renderTemplate`${maybeRenderHead()}<div${addAttribute(cardClass, "class")}> <a class="h-full w-full flex flex-col justify-between pl-6 lg:pl-10 pt-4 lg:pt-12 pb-2 lg:pb-6"${addAttribute(postHref, "href")}> <p class="text-3xl md:text-4xl lg:text-5xl tracking-normal font-extrabold">${title}</p> <img${addAttribute(imgClass, "class")}${addAttribute(imgSrc, "src")} alt=""> <p class="md:hidden block text-xl tracking-normal font-medium pb-3">See most popular  ↓</p> </a> </div>`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HubCard.astro", void 0);

const SphereWhite = {"src":"/_astro/img-large-sphere-white.13ea45ba.png","width":383,"height":334,"format":"png"};

const SphereMetal = {"src":"/_astro/img-large-sphere-metal.402e1655.png","width":559,"height":454,"format":"png"};

const CylinderWood = {"src":"/_astro/img-large-cylinder-wood.6a33d348.png","width":477,"height":344,"format":"png"};

const $$Astro = createAstro("https://www.uown.co");
const $$TheHub = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$TheHub;
  const propertyPosts = await getCategoryPostsWithLimit(
    "0a5d1c34-4085-4fab-83e7-19c4884a1251"
  );
  const moneyPosts = await getCategoryPostsWithLimit(
    "7ce6209c-ea5c-4081-95b5-b7b0fa1133e1"
  );
  const hlPosts = await getCategoryPostsWithLimit(
    "faba8071-9de2-408f-8529-e412ce8ed4c2"
  );
  const causesPosts = await getCategoryPostsWithLimit(
    "e17e0246-9f28-4830-b7f3-1b3385557704"
  );
  const itemContainerClass = "hidden md:flex justify-start gap-x-7 overflow-x-scroll hide-scrollbar [&>*:first-child]:ml-7";
  const purl = Astro2.url.origin + "/previews/hub.png";
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Investment News & Articles | UOWN | The Hub", "classes": "", "description": "Stay updated with UOWN's investment news and articles. Visit The Hub for the latest insights.", "purl": purl, "data-astro-cid-b46ygrw4": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main data-astro-cid-b46ygrw4> <section class="relative hero bg-no-repeat bg-cover bg-bottom flex flex-col justify-center items-center text-center overflow-hidden lg:px-0 px-11 py-40 lg:py-64" data-astro-cid-b46ygrw4> <img class="absolute z-0 w-[116px] md:w-[153px] lg:w-[200px] top-[65%] left-[74%]" width="486" height="384"${addAttribute(SphereWhite.src, "src")} alt="" data-astro-cid-b46ygrw4> <img class="absolute z-0 w-[70px] md:w-[93px] lg:w-[119px] top-[10%] left-[62%]" width="121" height="100"${addAttribute(SphereMetal.src, "src")} alt="" data-astro-cid-b46ygrw4> <img class="absolute z-0 w-[128px] md:w-[176px] lg:w-[224px] top-[60%] right-[75%] lg:right-[75%] md:right-[90%] md:top-[45%]" width="354" height="273"${addAttribute(CylinderWood.src, "src")} alt="" data-astro-cid-b46ygrw4> <p class="opacity-0 heading max-w-4xl md:text-5xl text-4xl tracking-normal font-extrabold pb-4" data-astro-cid-b46ygrw4>
The Hub
</p> <p class="opacity-0 subheading max-w-xl md:text-2xl text-xl tracking-normal font-regular pb-12" data-astro-cid-b46ygrw4>
The Hub is our powerful knowledge centre featuring useful and
                informative articles covering everything from money matters, to
                lifestyle choices, to all things property related.
</p> </section> <section class="md:pl-[70px] lg:pl-[140px]" data-astro-cid-b46ygrw4> <div class="flex overflow-hidden mt-6 md:mt-12 px-7 md:px-0 scrolling-section opacity-0" data-astro-cid-b46ygrw4> ${renderComponent($$result2, "HubCard", $$HubCard, { "title": "Property", "postHref": getFullUrl(Astro2.url.origin, "category/property"), ",": true, "imgSrc": Property.src, ",": true, "colorClass": "bg-mint-300", "data-astro-cid-b46ygrw4": true })} <div${addAttribute(itemContainerClass + " scrolling-cards", "class")} data-astro-cid-b46ygrw4> ${propertyPosts.map((post) => renderTemplate`${renderComponent($$result2, "PostTile", $$PostTile, { "readingTime": getReadingTime(
    "post",
    post.slug.current
  ), "postHref": getFullUrl(
    Astro2.url.origin,
    post.fullSlug
  ), "imgSrc": urlForImage(post.thumbnail).width(350).height(200).url(), "title": post.title, "color": "border-mint-300", "data-astro-cid-b46ygrw4": true })}`)} </div> </div> <div class="flex overflow-hidden mt-6 md:mt-12 px-7 md:px-0 scrolling-section opacity-0" data-astro-cid-b46ygrw4> ${renderComponent($$result2, "HubCard", $$HubCard, { "title": "Money", "postHref": getFullUrl(Astro2.url.origin, "category/money"), ",": true, "imgSrc": Money.src, ",": true, "colorClass": "bg-navyblue-300", "data-astro-cid-b46ygrw4": true })} <div${addAttribute(itemContainerClass + " scrolling-cards", "class")} data-astro-cid-b46ygrw4> ${moneyPosts.map((post) => renderTemplate`${renderComponent($$result2, "PostTile", $$PostTile, { "readingTime": getReadingTime(
    "post",
    post.slug.current
  ), "postHref": getFullUrl(
    Astro2.url.origin,
    post.fullSlug
  ), "imgSrc": urlForImage(post.thumbnail).width(350).height(200).url(), "title": post.title, "color": "border-navyblue-300", "data-astro-cid-b46ygrw4": true })}`)} </div> </div> <div class="flex overflow-hidden mt-6 md:mt-12 px-7 md:px-0 scrolling-section opacity-0" data-astro-cid-b46ygrw4> ${renderComponent($$result2, "HubCard", $$HubCard, { "title": "Home & Lifestyle", "postHref": getFullUrl(
    Astro2.url.origin,
    "category/home-lifestyle"
  ), ",": true, "imgSrc": Hl.src, ",": true, "colorClass": "bg-yellow-300", "data-astro-cid-b46ygrw4": true })} <div${addAttribute(itemContainerClass + " scrolling-cards", "class")} data-astro-cid-b46ygrw4> ${hlPosts.map((post) => renderTemplate`${renderComponent($$result2, "PostTile", $$PostTile, { "readingTime": getReadingTime(
    "post",
    post.slug.current
  ), "postHref": getFullUrl(
    Astro2.url.origin,
    post.fullSlug
  ), "imgSrc": urlForImage(post.thumbnail).width(350).height(200).url(), "title": post.title, "color": "border-yellow-300", "data-astro-cid-b46ygrw4": true })}`)} </div> </div> <div class="flex overflow-hidden mt-6 md:mt-12 px-7 md:px-0 scrolling-section opacity-0" data-astro-cid-b46ygrw4> ${renderComponent($$result2, "HubCard", $$HubCard, { "title": "Causes", "postHref": getFullUrl(Astro2.url.origin, "category/causes"), ",": true, "imgSrc": Causes.src, ",": true, "colorClass": "bg-salmon-200", "data-astro-cid-b46ygrw4": true })} <div${addAttribute(itemContainerClass + " scrolling-cards", "class")} data-astro-cid-b46ygrw4> ${causesPosts.map((post) => renderTemplate`${renderComponent($$result2, "PostTile", $$PostTile, { "readingTime": getReadingTime(
    "post",
    post.slug.current
  ), "postHref": getFullUrl(
    Astro2.url.origin,
    post.fullSlug
  ), "imgSrc": urlForImage(post.thumbnail).width(350).height(200).url(), "title": post.title, "color": "border-salmon-300", "data-astro-cid-b46ygrw4": true })}`)} </div> </div> </section> ${renderComponent($$result2, "UnleashThePowerCTA", $$UnleashThePowerCTA, { "colorClass": "bg-grey-gradient", "data-astro-cid-b46ygrw4": true })} </main> ` })}  `;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/the-hub.astro", void 0);

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/the-hub.astro";
const $$url = "/the-hub";

export { $$TheHub as default, $$file as file, $$url as url };

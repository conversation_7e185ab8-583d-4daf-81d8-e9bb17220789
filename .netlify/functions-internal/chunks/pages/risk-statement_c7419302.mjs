import { $ as $$Layout } from './404_1f20d35e.mjs';
import { _ as __astro_tag_component__, F as Fragment, l as createVNode } from '../astro_ca9e373b.mjs';
import { c as $$Image } from './_slug__314c3ce5.mjs';
import { $ as $$PolicyLayout, a as $$PolicyHeadings, b as $$PoliciesPara } from './cookie-policy_5e1db63a.mjs';
import 'clsx';
/* empty css                            */import '@sanity/client';
import 'react/jsx-runtime';
import 'motion/react';
import 'react';
import '@headlessui/react';
import '@heroicons/react/24/outline';
import '@heroicons/react/20/solid';
/* empty css                            */import 'html-escaper';
import '@astrojs/internal-helpers/path';
/* empty css                            */import '@portabletext/react';
import '../astro-assets-services_967ef4fc.mjs';
/* empty css                                 */import '@sanity/image-url';
import 'groq';
/* empty css                                 *//* empty css                                   */
const frontmatter = {
  "title": "Risk Statement",
  "description": "Here you can find all of our terms and conditions of using our platform."
};
function getHeadings() {
  return [];
}
const __usesAstroImage = true;
function _createMdxContent(props) {
  return createVNode($$Layout, {
    title: "Risk Statement | Understanding Investment Risks | UOWN",
    classes: "bg-gray",
    description: "Understand the risks associated with property crowdfunding. Read UOWN's risk statement.",
    purl: "",
    children: createVNode($$PolicyLayout, {
      heading: "Risk Statement",
      subheading: "What are the risks of property crowdfunding?",
      children: [createVNode($$PolicyHeadings, {
        text: "1. Property prices could fall meaning that the value of your shares can decline."
      }), createVNode($$PoliciesPara, {
        text: "The price of your shares are based upon the price of the property at the time of funding. For example, an investment costing \xA3100,000 will have 100,000 shares available to purchase at \xA31 per share."
      }), createVNode($$PoliciesPara, {
        text: "We have the property valued every three months by independent surveyors. We then update the value of your shares based upon this valuation. Based on the example above if the investment is re-valued at \xA390,000, then each of the 100,000 shares will now be worth \xA30.90."
      }), createVNode($$PoliciesPara, {
        text: "It is good financial practice to diversify your portfolio by investing only a portion of your disposable income in a variety of different investments across different platforms in order to mitigate risk."
      }), createVNode("p", {
        class: "text-base md:text-xl lg:text-2xl tracking-normal font-bold",
        children: "What do we do to minimise this risk?"
      }), createVNode($$PoliciesPara, {
        text: "Whilst we cannot control the movement of the market, we vet properties to minimise the risk of the property price falling and maximise the chance of the price rising. We do this by providing houses that have very strong rental demand and which have a history of being rented out year on year."
      }), createVNode($$PolicyHeadings, {
        text: "2. There may be periods when the property is not rented out and you will not receive any rent."
      }), createVNode($$PoliciesPara, {
        text: "If the property is not rented out or some of the rooms are not rented out then the amount you receive in rental income may be less than we state. If this is the case then you may not receive any monthly rental income or the rental income you receive may be less than expected."
      }), createVNode("p", {
        class: "text-base md:text-xl lg:text-2xl tracking-normal font-bold",
        children: "What do we do to minimise this risk?"
      }), createVNode($$PoliciesPara, {
        text: "We work with the best letting agents we can to minimise this risk and only pick properties that have a strong rental history going back many years. Whilst demand may change, we only pick houses in areas with a strong current or expected demand, aiming to minimise the risk that they will be empty for any extended periods."
      }), createVNode($$PolicyHeadings, {
        text: "3. There must be a buyer for your shares when you want to sell."
      }), createVNode($$PoliciesPara, {
        text: "Investments in UOWN are illiquid in nature as they are not listed on a public exchange. This means that you may not be able to easily sell your shares in a given property, or you may not be able to find a buyer at all, before the end of the investment term. When you choose to list your shares for sale on our platform we will market them at the current market value, which is calculated every quarter. There must be someone willing to purchase your shares at the market rate for you to sell them."
      }), createVNode("p", {
        class: "text-base md:text-xl lg:text-2xl tracking-normal font-bold",
        children: "What do we do to minimise this risk?"
      }), createVNode($$PoliciesPara, {
        text: "We will actively market your shares and do our best to facilitate the sale of your shares at the current market value. We aim to find a buyer within 3 weeks. However, we cannot guarantee that a buyer for your shares will be found."
      }), createVNode($$PoliciesPara, {
        text: "Note: You shouldn\u2019t invest money you will need access to immediately. UOWN works best if viewed as a long-term investment.",
        class: "italic"
      }), createVNode($$PolicyHeadings, {
        text: "4. Our forecasts and past performance help with the guesswork but we are only human and can\u2019t predict the future."
      }), createVNode($$PoliciesPara, {
        text: "Past performance of a property is not indicative of future results, and forecasts of future results are not guaranteed."
      }), createVNode("p", {
        class: "text-base md:text-xl lg:text-2xl tracking-normal font-bold",
        children: "What do we do to minimise this risk?"
      }), createVNode($$PoliciesPara, {
        text: "We base our forecasts on long term historical data to ensure that any predictions are resilient to short term changes in prices. We also ensure that all costs, insurance and taxes are accounted for with margins to ensure that a cushion is in place should something untoward happen. (More on this below)"
      }), createVNode($$PoliciesPara, {
        text: "All of these costs are based off the experience of The Parklane Group who have been letting and managing properties in Leeds for 40+ years. The Parklane Group is our primary backer and strategic partner. Despite our best efforts the true returns you receive may be higher or lower than we predict."
      }), createVNode($$PolicyHeadings, {
        text: "5. No coverage under the Financial Services Compensation Scheme (FSCS)"
      }), createVNode($$PoliciesPara, {
        text: "Your investment is not covered by the Financial Services Compensation Scheme."
      }), createVNode($$PolicyHeadings, {
        text: "6. No financial advice"
      }), createVNode($$PoliciesPara, {
        text: "UOWN do not provide financial advice or recommendations. If you are not sure whether or not investing in UOWN is right for you, you should seek the advice of an independent financial advisor who is authorised under the Financial Services and Markets Act 2000."
      }), createVNode($$PoliciesPara, {
        text: "This list of risk factors does not necessarily outline all possible risks involved. If you are unsure about any aspect of the information provided by the company, you should seek advice from an independent financial adviser."
      })]
    })
  });
}
function MDXContent(props = {}) {
  const {
    wrapper: MDXLayout
  } = props.components || {};
  return MDXLayout ? createVNode(MDXLayout, {
    ...props,
    children: createVNode(_createMdxContent, {
      ...props
    })
  }) : _createMdxContent();
}

__astro_tag_component__(getHeadings, "astro:jsx");
__astro_tag_component__(MDXContent, "astro:jsx");
const url = "/risk-statement";
const file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/risk-statement.mdx";
const Content = (props = {}) => MDXContent({
											...props,
											components: { Fragment, ...props.components, "astro-image":  props.components?.img ?? $$Image },
										});
Content[Symbol.for('mdx-component')] = true;
Content[Symbol.for('astro.needsHeadRendering')] = !Boolean(frontmatter.layout);
Content.moduleId = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/risk-statement.mdx";

export { Content, __usesAstroImage, Content as default, file, frontmatter, getHeadings, url };

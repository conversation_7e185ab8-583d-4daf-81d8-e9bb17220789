import { $ as $$Layout } from './404_1f20d35e.mjs';
import { e as createAstro, f as createComponent, r as renderTemplate, i as renderComponent, m as maybeRenderHead } from '../astro_ca9e373b.mjs';
import clsx from 'clsx';
import { jsxs, jsx, Fragment } from 'react/jsx-runtime';
import { motion, AnimatePresence, useMotionValue, animate } from 'motion/react';
import { useId, useState, useRef, useEffect, createContext } from 'react';
import { D as DialogOverlay } from './contact_c73ce573.mjs';
import { createRoot } from 'react-dom/client';
/* empty css                                                  */import PhoneInput, { isPossiblePhoneNumber } from 'react-phone-number-input';
import { P as PostHogNode } from './emailoctopus_03e49484.mjs';
import { T as Trustpilot, I as IN } from './index_e61a1fc3.mjs';
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react';
import { R as Reviews } from './demoPage_fe3d8eb4.mjs';
import useMeasure from 'react-use-measure';
import { I as IH, B as Bk, T as Tele, a as Times, G as GrandDesigns, N as NGI, S, c as BN } from './demoPage2_2cede3ec.mjs';
/* empty css                            */import '@sanity/client';
import '@heroicons/react/24/outline';
import '@heroicons/react/20/solid';
/* empty css                            */import 'html-escaper';
import './_slug__d49fd2f5.mjs';
import '@astrojs/internal-helpers/path';
/* empty css                            */import '@portabletext/react';
import '../astro-assets-services_967ef4fc.mjs';
/* empty css                                 */import '@sanity/image-url';
import 'groq';
/* empty css                                 */import 'react-dom';
/* empty css                             */import 'posthog-node';
/* empty css                           *//* empty css                           */import 'react-stacked-center-carousel';
import 'use-debounce';
import 'next/link.js';
import '@heroicons/react/24/solid';
import '@studio-freight/lenis';
import './invest_01881035.mjs';
/* empty css                            *//* empty css                               */
const ctaImage = {"src":"/_astro/ctaImage.90544db9.png","width":1634,"height":920,"format":"png"};

function CTASection() {
  return /* @__PURE__ */ jsxs(
    motion.div,
    {
      initial: { opacity: 0, y: 50 },
      whileInView: { opacity: 1, y: 0, threshold: 0.99 },
      transition: { duration: 0.5, ease: "easeIn" },
      className: "relative bg-gray-900",
      children: [
        /* @__PURE__ */ jsx("div", { className: "relative mx-auto max-w-7xl py-24 sm:py-32 lg:px-8 lg:py-40", children: /* @__PURE__ */ jsxs("div", { className: "pl-6 pr-6 md:mr-auto mlg:w-1/3 mlg:pr-16 lxl:w-1/2 lxl:pl-0 lxl:pr-24 xl:pr-32", children: [
          /* @__PURE__ */ jsx("p", { className: "mt-2 text-4xl font-semibold tracking-tight text-white sm:text-5xl", children: "Get into the data" }),
          /* @__PURE__ */ jsx("p", { className: "mt-6 text-base/7 text-gray-300", children: " Our guide uses high quality data to breakdown the UK housing market, and predict where prices and rents are going in 2025 and beyond. We look at the key drivers from interest rates to population growth." }),
          /* @__PURE__ */ jsx("div", { className: "mt-8", children: /* @__PURE__ */ jsx("a", { href: "#free-chapters", className: "inline-flex rounded-md bg-white/10 px-3.5 py-2.5 text-sm font-semibold text-white shadow-xs hover:bg-white/20 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white", children: "Get the guide" }) })
        ] }) }),
        /* @__PURE__ */ jsx("div", { className: "relative h-full overflow-hidden bg-white mlg:absolute mlg:top-0 mlg:right-0 mlg:w-2/3 lxl:w-1/2", children: /* @__PURE__ */ jsx("div", { className: "size-full flex justify-center items-center", children: /* @__PURE__ */ jsx("img", { className: "p-6", src: ctaImage.src, alt: "", width: "1920" }) }) })
      ]
    }
  );
}

function GridPattern(props) {
  let patternId = useId();
  return /* @__PURE__ */ jsxs("svg", { "aria-hidden": "true", className: "absolute inset-0 h-full w-full", children: [
    /* @__PURE__ */ jsx("defs", { children: /* @__PURE__ */ jsx(
      "pattern",
      {
        id: patternId,
        width: "128",
        height: "128",
        patternUnits: "userSpaceOnUse",
        ...props,
        children: /* @__PURE__ */ jsx("path", { d: "M0 128V.5H128", fill: "none", stroke: "currentColor" })
      }
    ) }),
    /* @__PURE__ */ jsx("rect", { width: "100%", height: "100%", fill: `url(#${patternId})` })
  ] });
}

function SectionHeading({ number, children, className, ...props }) {
  return /* @__PURE__ */ jsxs(
    "h2",
    {
      className: clsx(
        className,
        "inline-flex items-center rounded-full px-4 py-1 text-mint-500 ring-1 ring-inset ring-mint-500"
      ),
      ...props,
      children: [
        /* @__PURE__ */ jsx("span", { className: "font-mono text-sm", "aria-hidden": "true", children: number.padStart(2, "0") }),
        /* @__PURE__ */ jsx("span", { className: "ml-3 h-3.5 w-px bg-mint-500/20" }),
        /* @__PURE__ */ jsx("span", { className: "ml-3 text-base font-medium tracking-tight", children })
      ]
    }
  );
}

const authorImage = {"src":"/_astro/author2.2e3ee7a7.jpg","width":3259,"height":3678,"format":"jpg"};

function Author() {
  return /* @__PURE__ */ jsxs(
    "section",
    {
      id: "author",
      "aria-labelledby": "author-title",
      className: "relative scroll-mt-14 pb-3 pt-8 sm:scroll-mt-32 sm:pb-16 sm:pt-10 lg:pt-16",
      children: [
        /* @__PURE__ */ jsx("div", { className: "absolute inset-x-0 bottom-0 top-1/2 text-slate-900/10 [mask-image:linear-gradient(transparent,white)]", children: /* @__PURE__ */ jsx(GridPattern, { x: "50%", y: "100%" }) }),
        /* @__PURE__ */ jsx("div", { className: "relative mx-auto max-w-5xl pt-16 sm:px-6", children: /* @__PURE__ */ jsxs("div", { className: "bg-slate-50 pt-px sm:rounded-6xl", children: [
          /* @__PURE__ */ jsx("div", { className: "relative mx-auto -mt-16 h-44 w-44 overflow-hidden rounded-full bg-slate-200 md:float-right md:h-64 md:w-64 md:[shape-outside:circle(40%)] lg:mr-20 lg:h-72 lg:w-72", children: /* @__PURE__ */ jsx(
            motion.img,
            {
              initial: { opacity: 0, y: 50 },
              whileInView: { opacity: 1, y: 0, threshold: 0.99 },
              transition: { duration: 0.5, ease: "easeIn" },
              className: "absolute inset-0 h-full w-full object-cover",
              src: authorImage.src,
              alt: "",
              sizes: "(min-width: 1024px) 18rem, (min-width: 768px) 16rem, 11rem"
            }
          ) }),
          /* @__PURE__ */ jsxs("div", { className: "px-4 py-10 sm:px-10 sm:py-16 md:py-20 lg:px-20 lg:py-32", children: [
            /* @__PURE__ */ jsx(SectionHeading, { number: "4", id: "author-title", children: "Book a Call" }),
            /* @__PURE__ */ jsxs(
              motion.p,
              {
                initial: { opacity: 0, y: 50 },
                whileInView: { opacity: 1, y: 0, threshold: 0.99 },
                transition: { duration: 0.5, ease: "easeIn" },
                className: "mt-8 font-display text-3xl smd:text-5xl font-extrabold tracking-tight text-slate-900 sm:text-3xl",
                children: [
                  /* @__PURE__ */ jsx("span", { className: "block text-mint-500", children: "Haaris Ahmed –" }),
                  " Founder of UOWN"
                ]
              }
            ),
            /* @__PURE__ */ jsx(
              motion.p,
              {
                initial: { opacity: 0, y: 50 },
                whileInView: { opacity: 1, y: 0, threshold: 0.99 },
                transition: { duration: 0.5, ease: "easeIn" },
                className: "mt-4 text-lg tracking-tight text-slate-700",
                children: "Property investment has been a tried-and-tested way for people and businesses to build wealth overthe years. And the UK market still has plenty to offer in 2025."
              }
            ),
            /* @__PURE__ */ jsx(
              motion.p,
              {
                initial: { opacity: 0, y: 50 },
                whileInView: { opacity: 1, y: 0, threshold: 0.99 },
                transition: { duration: 0.5, ease: "easeIn" },
                className: "mt-4 text-lg tracking-tight text-slate-700",
                children: "In this guide, we break down what’s driving the market— from interestrates to population growth—and show you why looking beyond the usual hotspots in the South East could be a smart move."
              }
            ),
            /* @__PURE__ */ jsx(
              motion.p,
              {
                initial: { opacity: 0, y: 50 },
                whileInView: { opacity: 1, y: 0, threshold: 0.99 },
                transition: { duration: 0.5, ease: "easeIn" },
                className: "mt-4 text-lg tracking-tight text-slate-700",
                children: "Buy-to-letinvestors are facing new challenges, like higher stamp duty and stricter regulations, butthat doesn’t mean the opportunities are gone. Investors are increasingly turning to platforms like UOWN to make property investment work forthem in today’s market."
              }
            ),
            /* @__PURE__ */ jsx(
              motion.div,
              {
                initial: { opacity: 0, y: 50 },
                whileInView: { opacity: 1, y: 0, threshold: 0.99 },
                transition: { duration: 0.5, ease: "easeIn" },
                className: "mt-10 flex flex-col justify-center gap-x-6 pb-14",
                children: /* @__PURE__ */ jsx("div", { className: "text-base font-semibold leading-6 text-gray-900", children: /* @__PURE__ */ jsx("a", { href: "https://cal.com/suhayb-uown/uown-guide-2025-invest-with-uown", "data-attr": "bookacall", className: "bg-mint-300 rounded-full px-4 py-3 text-md font-bold text-black-200 shadow-sm transition duration-150 ease-in hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2", children: "Book a call" }) })
              }
            )
          ] })
        ] }) })
      ]
    }
  );
}

const baseStyles = {
  solid: "inline-flex justify-center rounded-full py-3 px-4 text-base font-semibold tracking-tight shadow-sm focus:outline-none",
  outline: "inline-flex justify-center rounded-full border py-3 px-4 text-base font-semibold tracking-tight focus:outline-none"
};
const variantStyles = {
  solid: {
    slate: "bg-slate-900 text-white hover:bg-slate-700 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-slate-900 active:bg-slate-700 active:text-white/80 disabled:opacity-30 disabled:hover:bg-slate-900",
    mint: "bg-mint-500 text-white hover:bg-mint-300 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-mint-500 active:bg-mint-500 active:text-white/80 disabled:opacity-30 disabled:hover:bg-mint-500",
    white: "bg-white text-mint-500 hover:text-mint-500 focus-visible:text-mint-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white active:bg-mint-50 active:text-mint-500/80 disabled:opacity-40 disabled:hover:text-mint-500"
  },
  outline: {
    slate: "border-slate-200 text-slate-900 hover:border-slate-300 hover:bg-slate-50 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-slate-600 active:border-slate-200 active:bg-slate-50 active:text-slate-900/70 disabled:opacity-40 disabled:hover:border-slate-200 disabled:hover:bg-transparent",
    blue: "border-mint-300 text-mint-500 hover:border-mint-200 hover:bg-mint-50 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-mint-500 active:text-mint-500/70 disabled:opacity-40 disabled:hover:border-mint-300 disabled:hover:bg-transparent"
  }
};
function Button({ className, ...props }) {
  props.variant ??= "solid";
  props.color ??= "slate";
  className = clsx(
    baseStyles[props.variant],
    props.variant === "outline" ? variantStyles.outline[props.color] : props.variant === "solid" ? variantStyles.solid[props.color] : void 0,
    className
  );
  return typeof props.href === "undefined" ? /* @__PURE__ */ jsx("button", { className, ...props }) : /* @__PURE__ */ jsx("a", { className, ...props });
}

const styles = {
  xs: "mx-auto px-4 sm:px-6 md:max-w-2xl md:px-4 lg:px-2",
  sm: "mx-auto px-4 sm:px-6 md:max-w-2xl md:px-4 lg:max-w-4xl lg:px-12",
  md: "mx-auto px-4 sm:px-6 md:max-w-2xl md:px-4 lg:max-w-5xl lg:px-8",
  lg: "mx-auto px-4 sm:px-6 md:max-w-2xl md:px-4 lg:max-w-7xl lg:px-8"
};
function Container({ size = "sm", className, ...props }) {
  return /* @__PURE__ */ jsx("div", { className: clsx(styles[size], className), ...props });
}

function Pattern({
  size = 40,
  gapX = 16,
  gapY = 8,
  pattern = [
    [0, 1, 0, 1, 1, 0, 1, 0],
    [1, 0, 1, 1, 0, 0, 0, 1],
    [0, 1, 0, 1, 1, 0, 1, 0],
    [1, 0, 1, 1, 0, 0, 0, 1]
  ],
  ...props
}) {
  let id = useId();
  let width = pattern[0].length * size + (pattern[0].length - 1) * gapX;
  let height = pattern.length * size + (pattern.length - 1) * gapY;
  return /* @__PURE__ */ jsxs("svg", { "aria-hidden": "true", width, height, ...props, children: [
    /* @__PURE__ */ jsxs("defs", { children: [
      /* @__PURE__ */ jsxs("symbol", { id: `${id}-0`, width: size, height: size, children: [
        /* @__PURE__ */ jsx("rect", { className: "fill-mint-300", width: size, height: size }),
        /* @__PURE__ */ jsx(
          "circle",
          {
            className: "fill-mint-500",
            cx: size / 2,
            cy: size / 2,
            r: size * (13 / 40)
          }
        )
      ] }),
      /* @__PURE__ */ jsxs("symbol", { id: `${id}-1`, width: size, height: size, children: [
        /* @__PURE__ */ jsx(
          "circle",
          {
            className: "fill-mint-200",
            cx: size / 2,
            cy: size / 2,
            r: size / 2
          }
        ),
        /* @__PURE__ */ jsx(
          "rect",
          {
            className: "fill-mint-500",
            width: size / 2,
            height: size / 2,
            x: size / 4,
            y: size / 4
          }
        )
      ] })
    ] }),
    pattern.map(
      (row, rowIndex) => row.map((shape, columnIndex) => /* @__PURE__ */ jsx(
        "use",
        {
          href: `#${id}-${shape}`,
          x: columnIndex * size + columnIndex * gapX,
          y: rowIndex * size + rowIndex * gapY
        },
        `${rowIndex}-${columnIndex}`
      ))
    )
  ] });
}

function FreeChapters({ distinctId, variant, fbc }) {
  const [formData, setFormData] = useState({ firstName: "", lastName: "", email: "", phoneNo: "" });
  const delay = (ms) => new Promise(
    (resolve) => setTimeout(resolve, ms)
  );
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevFormData) => ({ ...prevFormData, [name]: value }));
  };
  var allowSubmit = true;
  const submit = async (e) => {
    e.preventDefault();
    grecaptcha.ready(function() {
      grecaptcha.execute("6LcoZIYpAAAAAMyNoRMU7qVWJmRde5eFgoWjh8xd", {
        action: "submit"
      }).then(function(token) {
        fetch("/recaptcha", {
          method: "POST",
          body: JSON.stringify({ recaptcha: token })
        }).then((response) => response.json()).then((gResponse) => {
          console.log(gResponse);
          if (gResponse.success && gResponse.score >= 0.9) {
            let showErrorDialog = function(content) {
              var dialogDiv = document.getElementById("dialog-container");
              if (dialogDiv) {
                const root = createRoot(dialogDiv);
                root.render(/* @__PURE__ */ jsx(
                  DialogOverlay,
                  {
                    heading: "Oops...",
                    content,
                    button: "Close",
                    classes: "bg-salmon-200 text-black hover:bg-salmon-300 focus-visible:outline-salmon-300",
                    type: "error"
                  }
                ));
              }
            };
            if (allowSubmit == false) {
              return false;
            }
            allowSubmit = false;
            const formData2 = new FormData(e.target);
            const number = formData2.get("phoneNo");
            formData2.set("gScore", gResponse.score);
            if (number !== null) {
              if (!isPossiblePhoneNumber(number)) {
                showErrorDialog("The phone number you entered is not valid. Please try again");
              } else {
                const response = fetch("/api/emailoctopus", {
                  method: "POST",
                  body: formData2
                });
                const data = response.json();
                if (data.message === "success") {
                  delay(1e3);
                  window.location.href = "/guide-sent";
                } else if (data.message === "error") {
                  delay(1e3);
                  showErrorDialog("Something went wrong. Please try again.");
                }
              }
            }
          } else {
            const formDataFail = new FormData(e.target);
            const firstname = formDataFail.get("firstName");
            const lastName = formDataFail.get("lastName");
            const email = formDataFail.get("email");
            const distinctid = formDataFail.get("distinctid");
            const userAgent = formDataFail.get("userAgent");
            const variant2 = formDataFail.get("variant");
            const fbc2 = formDataFail.get("fbc");
            var fbclid = "";
            if (fbc2 !== "") {
              const fbcA = fbc2.split(".");
              fbclid = fbcA[3];
            }
            PostHogNode().capture({
              distinctId: distinctid,
              event: "Recaptcha failed",
              properties: {
                em: email,
                first_name: firstname,
                ln: lastName,
                distinct_id: distinctid,
                client_user_agent: userAgent,
                _fbc: fbc2,
                fbclid,
                recaptcha_score: gResponse.score,
                "$feature/uk-investment-guide-landing-page-1": variant2
              }
            });
          }
        });
      });
    });
  };
  function PhoneNumber() {
    const [value, setValue] = useState();
    return /* @__PURE__ */ jsx(
      PhoneInput,
      {
        required: true,
        international: true,
        defaultCountry: "GB",
        name: "phoneNo",
        id: "phone-number",
        value,
        className: "",
        onChange: setValue
      }
    );
  }
  return /* @__PURE__ */ jsx(
    "section",
    {
      id: "free-chapters",
      "aria-label": "Free preview",
      className: "scroll-mt-14 bg-mint-500 sm:scroll-mt-32",
      children: /* @__PURE__ */ jsx("div", { className: "overflow-hidden lg:relative", children: /* @__PURE__ */ jsxs(
        Container,
        {
          size: "md",
          className: "relative grid grid-cols-1 items-end gap-y-12 py-20 lg:static lg:grid-cols-2 lg:py-28 xl:py-32",
          children: [
            /* @__PURE__ */ jsx(Pattern, { className: "absolute -top-32 left-0 w-full sm:-top-5 sm:left-3/4 sm:ml-8 sm:w-auto md:left-2/3 lg:left-auto lg:right-2 lg:ml-0 xl:left-2/3 xl:right-auto" }),
            /* @__PURE__ */ jsxs("div", { children: [
              /* @__PURE__ */ jsx(
                motion.h2,
                {
                  initial: { opacity: 0, y: 50 },
                  whileInView: { opacity: 1, y: 0, threshold: 0.99 },
                  transition: { duration: 0.5, ease: "easeIn" },
                  className: "font-display text-5xl font-extrabold tracking-tight text-white sm:w-3/4 sm:text-6xl md:w-2/3 lg:w-auto",
                  children: "Get the free investment guide"
                }
              ),
              /* @__PURE__ */ jsx(
                motion.p,
                {
                  initial: { opacity: 0, y: 50 },
                  whileInView: { opacity: 1, y: 0, threshold: 0.99 },
                  transition: { duration: 0.5, ease: "easeIn" },
                  className: "mt-4 text-lg tracking-tight text-mint-200",
                  children: "Enter your details and we’ll send you your copy of the guide."
                }
              ),
              /* @__PURE__ */ jsxs(
                motion.form,
                {
                  initial: { opacity: 0, y: 50 },
                  whileInView: { opacity: 1, y: 0, threshold: 0.99 },
                  transition: { duration: 0.5, ease: "easeIn" },
                  className: "pt-6",
                  method: "POST",
                  onSubmit: submit,
                  id: "contact-form",
                  children: [
                    /* @__PURE__ */ jsxs("h3", { className: "text-base font-medium tracking-tight text-white", children: [
                      "Get the guide straight to your inbox",
                      " ",
                      /* @__PURE__ */ jsx("span", { "aria-hidden": "true", children: "→" })
                    ] }),
                    /* @__PURE__ */ jsx("div", { className: "mt-4 sm:relative sm:flex sm:items-center sm:py-0.5 sm:pr-2.5", children: /* @__PURE__ */ jsxs("div", { className: "relative sm:static sm:flex-auto", children: [
                      /* @__PURE__ */ jsx(
                        "input",
                        {
                          onChange: handleChange,
                          value: formData.firstName,
                          type: "text",
                          id: "first-name",
                          name: "firstName",
                          required: true,
                          "aria-label": "First Name",
                          placeholder: "First Name",
                          className: "peer rounded-full border-0 relative z-10 w-full appearance-none bg-transparent ml-1 px-4 py-2 text-base text-white placeholder:text-white/70 focus:ring-transparent focus:outline-none sm:py-3"
                        }
                      ),
                      /* @__PURE__ */ jsx("div", { className: "absolute inset-0 rounded-full border border-white/80 peer-focus:border-mint-300 peer-focus:bg-mint-500 peer-focus:ring-1 peer-focus:ring-mint-300" })
                    ] }) }),
                    /* @__PURE__ */ jsx("div", { className: "mt-4 sm:relative sm:flex sm:items-center sm:py-0.5 sm:pr-2.5", children: /* @__PURE__ */ jsxs("div", { className: "relative sm:static sm:flex-auto", children: [
                      /* @__PURE__ */ jsx(
                        "input",
                        {
                          onChange: handleChange,
                          value: formData.lastName,
                          type: "text",
                          id: "last-name",
                          name: "lastName",
                          required: true,
                          "aria-label": "Last Name",
                          placeholder: "Last Name",
                          className: "peer rounded-full border-0 relative z-10 w-full appearance-none bg-transparent ml-1 px-4 py-2 text-base text-white placeholder:text-white/70 focus:ring-transparent focus:outline-none sm:py-3"
                        }
                      ),
                      /* @__PURE__ */ jsx("div", { className: "absolute inset-0 rounded-full border border-white/80 peer-focus:border-mint-300 peer-focus:bg-mint-500 peer-focus:ring-1 peer-focus:ring-mint-300" })
                    ] }) }),
                    /* @__PURE__ */ jsx("div", { className: "mt-4 sm:relative sm:flex sm:items-center sm:py-0.5 sm:pr-2.5", children: /* @__PURE__ */ jsxs("div", { className: "relative sm:static sm:flex-auto", children: [
                      /* @__PURE__ */ jsx(
                        "input",
                        {
                          onChange: handleChange,
                          value: formData.email,
                          type: "email",
                          id: "email-address",
                          name: "email",
                          required: true,
                          "aria-label": "Email address",
                          placeholder: "Email Address",
                          className: "peer rounded-full border-0 relative z-10 w-full appearance-none bg-transparent ml-1 px-4 py-2 text-base text-white placeholder:text-white/70 focus:ring-transparent focus:outline-none sm:py-3"
                        }
                      ),
                      /* @__PURE__ */ jsx("div", { className: "absolute inset-0 rounded-full border border-white/80 peer-focus:border-mint-300 peer-focus:bg-mint-500 peer-focus:ring-1 peer-focus:ring-mint-300" })
                    ] }) }),
                    /* @__PURE__ */ jsx(
                      "input",
                      {
                        value: distinctId,
                        type: "text",
                        name: "distinctid",
                        className: "hidden",
                        readOnly: true
                      }
                    ),
                    /* @__PURE__ */ jsx(
                      "input",
                      {
                        id: "userAgent",
                        type: "text",
                        name: "userAgent",
                        className: "hidden",
                        readOnly: true
                      }
                    ),
                    /* @__PURE__ */ jsx(
                      "input",
                      {
                        value: variant,
                        type: "text",
                        name: "variant",
                        className: "hidden",
                        readOnly: true
                      }
                    ),
                    /* @__PURE__ */ jsx(
                      "input",
                      {
                        value: fbc !== "" ? fbc.value : "",
                        type: "text",
                        name: "fbc",
                        className: "hidden",
                        readOnly: true
                      }
                    ),
                    /* @__PURE__ */ jsx("div", { className: "mt-4 sm:relative sm:flex sm:items-center sm:py-0.5 sm:pr-2.5", children: /* @__PURE__ */ jsxs("div", { className: "relative sm:static sm:flex-auto", children: [
                      /* @__PURE__ */ jsx("div", { className: "absolute inset-0 rounded-full border border-white/80 peer-focus:border-mint-300 peer-focus:bg-mint-500 peer-focus:ring-1 peer-focus:ring-mint-300" }),
                      PhoneNumber()
                    ] }) }),
                    /* @__PURE__ */ jsx(
                      Button,
                      {
                        type: "submit",
                        color: "white",
                        className: "submit-btn w-full sm:relative sm:z-10 sm:w-auto sm:flex- mt-4 leading-8",
                        "data-attr": "submit",
                        "aria-label": "Send me the guide",
                        children: "Send me the guide"
                      }
                    ),
                    /* @__PURE__ */ jsx("div", { id: "dialog-container" })
                  ]
                }
              )
            ] })
          ]
        }
      ) })
    }
  );
}

function StarIcon(props) {
  return /* @__PURE__ */ jsx("svg", { "aria-hidden": "true", viewBox: "0 0 20 20", ...props, children: /* @__PURE__ */ jsx("path", { d: "M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" }) });
}
function StarRating({ rating = 5 }) {
  return /* @__PURE__ */ jsx("div", { className: "flex gap-1", children: [...Array(rating).keys()].map((index) => /* @__PURE__ */ jsx(StarIcon, { className: "h-5 w-5 fill-current" }, index)) });
}

const coverImage = {"src":"/_astro/coverImage.14ac2aa1.png","width":960,"height":1284,"format":"png"};

function Testimonial$1() {
  return /* @__PURE__ */ jsx("div", { className: "relative mx-auto max-w-md text-center lg:mx-0 lg:text-left", children: /* @__PURE__ */ jsx(
    motion.div,
    {
      initial: { opacity: 0, y: 50 },
      whileInView: { opacity: 1, y: 0, threshold: 0.99 },
      transition: { duration: 0.5, ease: "easeIn" },
      className: "flex justify-center text-mint-500 lg:justify-start",
      children: /* @__PURE__ */ jsxs("div", { className: "flex justify-center items-center", children: [
        /* @__PURE__ */ jsx(
          "span",
          {
            className: "text-2xl tracking-normal font-bold",
            children: "4.9/5"
          }
        ),
        /* @__PURE__ */ jsx(
          "a",
          {
            href: "https://uk.trustpilot.com/review/uown.co",
            target: "_blank",
            children: /* @__PURE__ */ jsx("img", { src: Trustpilot.src, alt: "" })
          }
        )
      ] })
    }
  ) });
}
function Hero() {
  return /* @__PURE__ */ jsx("header", { className: "overflow-hidden bg-slate-100 lg:bg-transparent lg:px-5", children: /* @__PURE__ */ jsxs("div", { className: "mx-auto grid max-w-6xl grid-cols-1 grid-rows-[auto_1fr] gap-y-16 pt-16 md:pt-20 lg:grid-cols-12 lg:gap-y-20 lg:px-3 lg:pb-36 lg:pt-20 xl:py-32", children: [
    /* @__PURE__ */ jsxs("div", { className: "relative flex items-end lg:col-span-5 lg:row-span-2", children: [
      /* @__PURE__ */ jsx("div", { className: "absolute -bottom-12 -top-20 left-0 right-1/2 z-10 rounded-br-6xl bg-mint-500 text-white/10 md:bottom-8 lg:-inset-y-32 lg:left-[-100vw] lg:right-full lg:-mr-40", children: /* @__PURE__ */ jsx(
        GridPattern,
        {
          x: "100%",
          y: "100%",
          patternTransform: "translate(112 64)"
        }
      ) }),
      /* @__PURE__ */ jsx(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.5, ease: "easeIn" },
          className: "relative z-10 mx-auto flex w-64 rounded-xl   md:w-80 lg:w-auto",
          children: /* @__PURE__ */ jsx("img", { className: "w-full", src: coverImage.src, alt: "" })
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "relative px-4 sm:px-6 lg:col-span-7 lg:pb-14 lg:pl-16 lg:pr-0 xl:pl-20", children: [
      /* @__PURE__ */ jsx("div", { className: "hidden lg:absolute lg:-top-32 lg:bottom-0 lg:left-[-100vw] lg:right-[-100vw] lg:block lg:bg-slate-100" }),
      /* @__PURE__ */ jsx(Testimonial$1, {})
    ] }),
    /* @__PURE__ */ jsx("div", { className: "bg-white py-16 lg:col-span-7 lg:bg-transparent lg:pl-16 lg:py-0 xl:pl-20", children: /* @__PURE__ */ jsxs("div", { className: "mx-auto px-4 sm:px-6 md:max-w-2xl md:px-4 lg:px-0", children: [
      /* @__PURE__ */ jsx(
        motion.h1,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.6, ease: "easeIn" },
          className: "font-display text-5xl font-extrabold text-slate-900 sm:text-6xl",
          children: "UOWN 2025 UK Property Investment Guide"
        }
      ),
      /* @__PURE__ */ jsx(
        motion.p,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.7, ease: "easeIn" },
          className: "mt-4 text-3xl text-slate-600",
          children: "Discover the key trends and insights to help you make informed investments in UK property this year."
        }
      ),
      /* @__PURE__ */ jsx(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "easeIn" },
          className: "mt-8 flex gap-4",
          children: /* @__PURE__ */ jsx(Button, { href: "#free-chapters", color: "mint", "aria-label": "Get the guide", children: "Get the guide" })
        }
      )
    ] }) })
  ] }) });
}

function CheckIcon(props) {
  return /* @__PURE__ */ jsx("svg", { "aria-hidden": "true", viewBox: "0 0 32 32", ...props, children: /* @__PURE__ */ jsx("path", { d: "M11.83 15.795a1 1 0 0 0-1.66 1.114l1.66-1.114Zm9.861-4.072a1 1 0 1 0-1.382-1.446l1.382 1.446ZM14.115 21l-.83.557a1 1 0 0 0 1.784-.258L14.115 21Zm.954.3c1.29-4.11 3.539-6.63 6.622-9.577l-1.382-1.446c-3.152 3.013-5.704 5.82-7.148 10.424l1.908.598Zm-4.9-4.391 3.115 4.648 1.661-1.114-3.114-4.648-1.662 1.114Z" }) });
}

function Introduction() {
  return /* @__PURE__ */ jsx(
    "section",
    {
      id: "introduction",
      "aria-label": "Introduction",
      className: "pb-16 pt-20 sm:pb-20 md:pt-36 lg:py-32",
      children: /* @__PURE__ */ jsxs(Container, { className: "text-lg tracking-tight text-slate-700", children: [
        /* @__PURE__ */ jsx(
          motion.p,
          {
            initial: { opacity: 0, y: 50 },
            whileInView: { opacity: 1, y: 0, threshold: 0.99 },
            transition: { duration: 0.5, ease: "easeIn" },
            className: "font-display text-4xl font-bold tracking-tight text-slate-900",
            children: "This investment guide looks at the trends, projections and strategies you can use to make informed investment decisions."
          }
        ),
        /* @__PURE__ */ jsx(
          motion.p,
          {
            initial: { opacity: 0, y: 50 },
            whileInView: { opacity: 1, y: 0, threshold: 0.99 },
            transition: { duration: 0.5, ease: "easeIn" },
            className: "mt-4",
            children: "The UK property market has a strong reputation as a stable and well regulated place to buy property."
          }
        ),
        /* @__PURE__ */ jsx(
          motion.p,
          {
            initial: { opacity: 0, y: 50 },
            whileInView: { opacity: 1, y: 0, threshold: 0.99 },
            transition: { duration: 0.5, ease: "easeIn" },
            className: "mt-4",
            children: "Understanding the data driving the trends and shifts is important to making wise investment decisions, this guide gives you that data alongside expert analysis in an easy to digest format."
          }
        ),
        /* @__PURE__ */ jsx("ul", { role: "list", className: "mt-8 space-y-3", children: [
          "What will happen to house prices in 2025?",
          "Which region will see the biggest growth over the next five years?",
          "How will stamp duty affect the market this year?",
          "Which UK city has the highest rental yield?",
          "Will the government successfully deliver on their house building targets?"
        ].map((feature, index) => /* @__PURE__ */ jsxs(
          motion.li,
          {
            initial: { opacity: 0, y: 50 },
            whileInView: { opacity: 1, y: 0, threshold: 0.99 },
            transition: { duration: 0.5, ease: "easeIn" },
            className: "flex",
            children: [
              /* @__PURE__ */ jsx(CheckIcon, { className: "h-8 w-8 flex-none fill-mint-500" }),
              /* @__PURE__ */ jsx("span", { className: "ml-4", children: feature })
            ]
          },
          feature
        )) }),
        /* @__PURE__ */ jsx(
          motion.p,
          {
            initial: { opacity: 0, y: 50 },
            whileInView: { opacity: 1, y: 0, threshold: 0.99 },
            transition: { duration: 0.6, ease: "easeIn" },
            className: "mt-8",
            children: "By the end of the guide, you’ll have the answers to all these questions and more. This guide will give you the confidence in knowing where to invest and you will have the data to back up your decision."
          }
        ),
        /* @__PURE__ */ jsx("p", { className: "mt-10", children: /* @__PURE__ */ jsxs(
          motion.a,
          {
            initial: { opacity: 0, y: 50 },
            whileInView: { opacity: 1, y: 0, threshold: 0.99 },
            transition: { duration: 0.7, ease: "easeIn" },
            href: "#free-chapters",
            className: "text-base font-medium text-mint-500 hover:text-mint-300",
            children: [
              "Get the guide straight to your inbox",
              " ",
              /* @__PURE__ */ jsx("span", { "aria-hidden": "true", children: "→" })
            ]
          }
        ) })
      ] })
    }
  );
}

const sections = [
  {
    id: "table-of-contents",
    title: /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx("span", { className: "hidden md:inline", children: "Table of contents" }),
      /* @__PURE__ */ jsx("span", { className: "md:hidden", children: "Contents" })
    ] })
  },
  {
    id: "screencasts",
    title: /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx("span", { className: "hidden md:inline", children: "What is Crowdfunding" }),
      /* @__PURE__ */ jsx("span", { className: "md:hidden", children: "Crowdfunding" })
    ] })
  },
  { id: "author", title: "Book a Call" }
];
function MenuIcon({ open, ...props }) {
  return /* @__PURE__ */ jsx(
    "svg",
    {
      "aria-hidden": "true",
      fill: "none",
      strokeWidth: "2",
      strokeLinecap: "round",
      strokeLinejoin: "round",
      viewBox: "0 0 24 24",
      ...props,
      children: /* @__PURE__ */ jsx(
        "path",
        {
          d: open ? "M17 7 7 17M7 7l10 10" : "m15 16-3 3-3-3M15 8l-3-3-3 3"
        }
      )
    }
  );
}
function NavBar() {
  let navBarRef = useRef(null);
  let [activeIndex, setActiveIndex] = useState(null);
  let mobileActiveIndex = activeIndex === null ? 0 : activeIndex;
  useEffect(() => {
    function updateActiveIndex() {
      if (!navBarRef.current) {
        return;
      }
      let newActiveIndex = null;
      let elements = sections.map(({ id }) => document.getElementById(id)).filter((el) => el !== null);
      let bodyRect = document.body.getBoundingClientRect();
      let offset = bodyRect.top + navBarRef.current.offsetHeight + 1;
      if (window.scrollY >= Math.floor(bodyRect.height) - window.innerHeight) {
        setActiveIndex(sections.length - 1);
        return;
      }
      for (let index = 0; index < elements.length; index++) {
        if (window.scrollY >= elements[index].getBoundingClientRect().top - offset) {
          newActiveIndex = index;
        } else {
          break;
        }
      }
      setActiveIndex(newActiveIndex);
    }
    updateActiveIndex();
    window.addEventListener("resize", updateActiveIndex);
    window.addEventListener("scroll", updateActiveIndex, { passive: true });
    return () => {
      window.removeEventListener("resize", updateActiveIndex);
      window.removeEventListener("scroll", updateActiveIndex);
    };
  }, []);
  return /* @__PURE__ */ jsxs(
    motion.div,
    {
      initial: { opacity: 0, y: 50 },
      whileInView: { opacity: 1, y: 0, threshold: 0.99 },
      transition: { duration: 0.5, ease: "easeIn" },
      ref: navBarRef,
      className: "sticky top-[78px] z-[9]",
      children: [
        /* @__PURE__ */ jsx(Popover, { className: "sm:hidden", children: ({ open }) => /* @__PURE__ */ jsxs(Fragment, { children: [
          /* @__PURE__ */ jsxs(
            "div",
            {
              className: clsx(
                "relative flex items-center px-4 py-3",
                !open && "bg-white/95 shadow-sm [@supports(backdrop-filter:blur(0))]:bg-white/80 [@supports(backdrop-filter:blur(0))]:backdrop-blur"
              ),
              children: [
                !open && /* @__PURE__ */ jsxs(Fragment, { children: [
                  /* @__PURE__ */ jsx(
                    "span",
                    {
                      "aria-hidden": "true",
                      className: "font-mono text-sm text-mint-500",
                      children: (mobileActiveIndex + 1).toString().padStart(2, "0")
                    }
                  ),
                  /* @__PURE__ */ jsx("span", { className: "ml-4 text-base font-medium text-slate-900", children: sections[mobileActiveIndex].title })
                ] }),
                /* @__PURE__ */ jsxs(
                  PopoverButton,
                  {
                    className: clsx(
                      "-mr-1 ml-auto flex h-8 w-8 items-center justify-center",
                      open && "relative z-10"
                    ),
                    "aria-label": "Toggle navigation menu",
                    children: [
                      !open && /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsx("span", { className: "absolute inset-0" }) }),
                      /* @__PURE__ */ jsx(MenuIcon, { open, className: "h-6 w-6 stroke-slate-700" })
                    ]
                  }
                )
              ]
            }
          ),
          /* @__PURE__ */ jsx(PopoverPanel, { className: "absolute inset-x-0 top-0 bg-white/95 py-3.5 shadow-sm [@supports(backdrop-filter:blur(0))]:bg-white/80 [@supports(backdrop-filter:blur(0))]:backdrop-blur", children: sections.map((section, sectionIndex) => /* @__PURE__ */ jsxs(
            PopoverButton,
            {
              as: "a",
              href: `#${section.id}`,
              className: "flex items-center px-4 py-1.5",
              children: [
                /* @__PURE__ */ jsx(
                  "span",
                  {
                    "aria-hidden": "true",
                    className: "font-mono text-sm text-mint-500",
                    children: (sectionIndex + 1).toString().padStart(2, "0")
                  }
                ),
                /* @__PURE__ */ jsx("span", { className: "ml-4 text-base font-medium text-slate-900", children: section.title })
              ]
            },
            section.id
          )) }),
          /* @__PURE__ */ jsx("div", { className: "absolute inset-x-0 bottom-full z-10 h-4 bg-white" })
        ] }) }),
        /* @__PURE__ */ jsx("div", { className: "hidden sm:flex sm:h-32 sm:justify-center sm:border-b sm:border-slate-200 sm:bg-white/95 sm:[@supports(backdrop-filter:blur(0))]:bg-white/80 sm:[@supports(backdrop-filter:blur(0))]:backdrop-blur", children: /* @__PURE__ */ jsx(
          "ol",
          {
            role: "list",
            className: "mb-[-2px] grid auto-cols-[minmax(0,15rem)] grid-flow-col text-base font-medium text-slate-900 [counter-reset:section]",
            children: sections.map((section, sectionIndex) => /* @__PURE__ */ jsx("li", { className: "flex [counter-increment:section]", children: /* @__PURE__ */ jsx(
              "a",
              {
                href: `#${section.id}`,
                className: clsx(
                  "text-sm flex w-full flex-col items-center justify-center border-b-2 before:mb-2 before:font-mono before:text-sm before:content-[counter(section,decimal-leading-zero)]",
                  sectionIndex === activeIndex ? "border-mint-500 bg-blue-50 text-mint-500 before:text-mint-500" : "border-transparent before:text-slate-500 hover:bg-mint-50/40 hover:before:text-slate-900"
                ),
                children: section.title
              }
            ) }, section.id))
          }
        ) })
      ]
    }
  );
}

const signImage = {"src":"/_astro/SIGNUP.551ac203.jpg","width":200,"height":112,"format":"jpg"};

const projectsImage = {"src":"/_astro/PICK_PROJECT.0c078b3d.jpg","width":200,"height":111,"format":"jpg"};

const setupImage = {"src":"/_astro/SELECT.cbc9c334.jpg","width":200,"height":121,"format":"jpg"};

const strokesImage = {"src":"/_astro/SIT_BACK.f187a334.jpg","width":200,"height":111,"format":"jpg"};

const videos = [
  {
    title: "Setup your free account",
    description: "It's quick and easy to register. Once you're done you can begin to dip your toe in and make your first investment.",
    image: signImage,
    runtime: { minutes: 16, seconds: 54 }
  },
  {
    title: "Pick your project",
    description: "Our in-house team is always on the hunt for the best deals out there! They sift through hundreds of deals each month and bring you the best ones.",
    image: projectsImage,
    runtime: { minutes: 9, seconds: 12 }
  },
  {
    title: "Choose how much to invest",
    description: "You decide how much you start investing with. With your support, we can bring the project vision to life!",
    image: setupImage,
    runtime: { minutes: 23, seconds: 25 }
  },
  {
    title: "Sit back and let us do the work",
    description: "Our experienced team will oversee the project. We'll keep you informed every step of the way, so you can enjoy the show!",
    image: strokesImage,
    runtime: { minutes: 28, seconds: 44 }
  }
];
function Screencasts() {
  return /* @__PURE__ */ jsxs(
    "section",
    {
      id: "screencasts",
      "aria-labelledby": "screencasts-title",
      className: "scroll-mt-14 py-16 sm:scroll-mt-32 sm:py-20 lg:py-32",
      children: [
        /* @__PURE__ */ jsxs(Container, { children: [
          /* @__PURE__ */ jsx(SectionHeading, { number: "2", id: "screencasts-title", children: "Property Crowdfunding" }),
          /* @__PURE__ */ jsx(
            motion.p,
            {
              initial: { opacity: 0, y: 50 },
              whileInView: { opacity: 1, y: 0, threshold: 0.99 },
              transition: { duration: 0.5, ease: "easeIn" },
              className: "mt-8 font-display text-4xl font-bold tracking-tight text-slate-900",
              children: "Learn how you can begin investing today in minutes with UOWN."
            }
          ),
          /* @__PURE__ */ jsx(
            motion.p,
            {
              initial: { opacity: 0, y: 50 },
              whileInView: { opacity: 1, y: 0, threshold: 0.99 },
              transition: { duration: 0.5, ease: "easeIn" },
              className: "mt-4 text-lg tracking-tight text-slate-700",
              children: "Learn how simple and straightforward property investing can be. You can register and invest in minutes and access projects otherwise unavailable to individuals."
            }
          )
        ] }),
        /* @__PURE__ */ jsx(Container, { size: "lg", className: "mt-16", children: /* @__PURE__ */ jsx(
          "ol",
          {
            role: "list",
            className: "grid grid-cols-1 gap-x-8 gap-y-10 [counter-reset:video] sm:grid-cols-2 lg:grid-cols-4",
            children: videos.map((video) => /* @__PURE__ */ jsxs(
              motion.li,
              {
                initial: { opacity: 0, y: 50 },
                whileInView: { opacity: 1, y: 0, threshold: 0.99 },
                transition: { duration: 0.5, ease: "easeIn" },
                className: "[counter-increment:video]",
                children: [
                  /* @__PURE__ */ jsxs(
                    "div",
                    {
                      className: "relative flex h-44 items-center justify-center rounded-clg px-6 shadow-lg",
                      style: {
                        backgroundImage: "conic-gradient(from -49.8deg at 50% 50%, #7331FF 0deg, #00A3FF 59.07deg, #4E51FF 185.61deg, #39DBFF 284.23deg, #B84FF1 329.41deg, #7331FF 360deg)"
                      },
                      children: [
                        /* @__PURE__ */ jsx("div", { className: "flex overflow-hidden rounded shadow-sm", children: /* @__PURE__ */ jsx("img", { src: video.image.src, alt: "" }) }),
                        /* @__PURE__ */ jsx("div", { className: "absolute bottom-2 left-2 flex items-center rounded-lg bg-black/30 px-1.5 py-0.5 text-sm text-white [..supports(backdrop-filter:blur(0))]:bg-white/10 [..supports(backdrop-filter:blur(0))]:backdrop-blur" })
                      ]
                    }
                  ),
                  /* @__PURE__ */ jsx("h3", { className: "mt-8 text-base font-medium tracking-tight text-slate-900 before:mb-2 before:block before:font-mono before:text-sm before:text-slate-500 before:content-[counter(video,decimal-leading-zero)]", children: video.title }),
                  /* @__PURE__ */ jsx("p", { className: "mt-2 text-sm text-slate-600", children: video.description })
                ]
              },
              video.title
            ))
          }
        ) })
      ]
    }
  );
}

const ExpandableContext = createContext({
  isExpanded: false,
  expand: () => {
  }
});
function Expandable(props) {
  let [isExpanded, setIsExpanded] = useState(false);
  return /* @__PURE__ */ jsx(
    ExpandableContext.Provider,
    {
      value: {
        isExpanded,
        expand: () => {
          setIsExpanded(true);
        }
      },
      children: /* @__PURE__ */ jsx("div", { ...props, "data-expanded": isExpanded ? "" : void 0 })
    }
  );
}

const tableOfContents = {
  "House Prices": {
    "House Prices: A look back over the past 5 years": 3,
    "Interest Rates: Forecasts and most effected areas": 4,
    "House Price Projections: Wages, affordability and regional differences": 7,
    "Investment Strategies: 2025-2030": 7
  },
  "Rental Market": {
    "Rental Performance: Whats happened since covid?": 15,
    "Key Drivers of the rental market moving forward": 17,
    "UK City Rental League Table": 19,
    "Challenges facing landlords": 20,
    "Viable alternatives to buy-to-let": 21
  },
  "Construction Industry": {
    "The state of housebuilding: Where do we stand?": 23,
    "Construction Inflation: What's hapenning to construction costs": 26,
    "House Building Target: UK government's position": 27
  },
  "Conclusions": {
    "Key Conclusions": 30
  }
};
function TableOfContents() {
  return /* @__PURE__ */ jsx(
    "section",
    {
      id: "table-of-contents",
      "aria-labelledby": "table-of-contents-title",
      className: "scroll-mt-14 py-16 sm:scroll-mt-32 sm:py-20 lg:py-32",
      children: /* @__PURE__ */ jsxs(Container, { children: [
        /* @__PURE__ */ jsx(SectionHeading, { number: "1", id: "table-of-contents-title", children: "Table of contents" }),
        /* @__PURE__ */ jsx(
          motion.p,
          {
            initial: { opacity: 0, y: 50 },
            whileInView: { opacity: 1, y: 0, threshold: 0.99 },
            transition: { duration: 0.5, ease: "easeIn" },
            className: "mt-8 font-display text-4xl font-bold tracking-tight text-slate-900",
            children: "Get a look at all of the content covered in the guide. Everything you need to know is inside."
          }
        ),
        /* @__PURE__ */ jsx(
          motion.p,
          {
            initial: { opacity: 0, y: 50 },
            whileInView: { opacity: 1, y: 0, threshold: 0.99 },
            transition: { duration: 0.5, ease: "easeIn" },
            className: "mt-4 text-lg tracking-tight text-slate-700",
            children: "Our UK property investment guide is comprised of 30 tightly edited, highly visual pages designed to teach you everything you need to know about property invertment in the UK."
          }
        ),
        /* @__PURE__ */ jsx(Expandable, { children: /* @__PURE__ */ jsx("ol", { role: "list", className: "mt-16 space-y-10 sm:space-y-16", children: Object.entries(tableOfContents).map(([title, pages]) => /* @__PURE__ */ jsxs(
          motion.li,
          {
            initial: { opacity: 0, y: 50 },
            whileInView: { opacity: 1, y: 0, threshold: 0.99 },
            transition: { duration: 0.5, ease: "easeIn" },
            children: [
              /* @__PURE__ */ jsx("h3", { className: "font-display text-3xl font-bold tracking-tight text-slate-900", children: title }),
              /* @__PURE__ */ jsx(
                "ol",
                {
                  role: "list",
                  className: "mt-8 divide-y divide-slate-300/30 rounded-clg bg-slate-50 px-6 py-3 text-base tracking-tight sm:px-8 sm:py-7",
                  children: Object.entries(pages).map(([title2, pageNumber]) => /* @__PURE__ */ jsxs(
                    motion.li,
                    {
                      initial: { opacity: 0, y: 50 },
                      whileInView: { opacity: 1, y: 0, threshold: 0.99 },
                      transition: { duration: 0.5, ease: "easeIn" },
                      className: "flex justify-between py-3",
                      "aria-label": `${title2} on page ${pageNumber}`,
                      children: [
                        /* @__PURE__ */ jsx(
                          "span",
                          {
                            className: "font-medium text-slate-900",
                            "aria-hidden": "true",
                            children: title2
                          }
                        ),
                        /* @__PURE__ */ jsx(
                          "span",
                          {
                            className: "font-mono text-slate-400",
                            "aria-hidden": "true",
                            children: pageNumber
                          }
                        )
                      ]
                    },
                    title2
                  ))
                }
              )
            ]
          },
          title
        )) }) })
      ] })
    }
  );
}

function Testimonial({ id, author, children }) {
  return /* @__PURE__ */ jsxs(
    "aside",
    {
      id,
      "aria-label": `Testimonial from ${author.name}`,
      className: "relative bg-slate-100 py-16 sm:py-32",
      children: [
        /* @__PURE__ */ jsx("div", { className: "text-slate-900/10", children: /* @__PURE__ */ jsx(GridPattern, { x: "50%", patternTransform: "translate(0 80)" }) }),
        /* @__PURE__ */ jsx(Container, { size: "xs", className: "relative", children: /* @__PURE__ */ jsxs("figure", { children: [
          /* @__PURE__ */ jsx("div", { className: "flex text-slate-900 sm:justify-center", children: /* @__PURE__ */ jsx(StarRating, {}) }),
          /* @__PURE__ */ jsx("blockquote", { className: "mt-10 font-display text-4xl font-medium tracking-tight text-slate-900 sm:text-center", children }),
          /* @__PURE__ */ jsx("figcaption", { className: "mt-10 flex items-center sm:justify-center", children: /* @__PURE__ */ jsxs("div", { className: "ml-4", children: [
            /* @__PURE__ */ jsx("div", { className: "text-base font-medium leading-6 tracking-tight text-slate-900", children: author.name }),
            /* @__PURE__ */ jsx("div", { className: "mt-1 text-sm text-slate-600", children: author.role })
          ] }) })
        ] }) })
      ]
    }
  );
}

const Card = ({ image }) => {
  const [showOverlay, setShowOverlay] = useState(false);
  return /* @__PURE__ */ jsxs(
    motion.div,
    {
      className: "relative overflow-hidden h-[100px] w-[100px] bg-white rounded-clg flex justify-center items-center",
      onHoverStart: () => setShowOverlay(true),
      onHoverEnd: () => setShowOverlay(false),
      children: [
        /* @__PURE__ */ jsx(AnimatePresence, { children: showOverlay && /* @__PURE__ */ jsxs(
          motion.div,
          {
            className: "absolute left-0 top-0 bottom-0 right-0 z-10 flex justify-center items-center",
            initial: { opacity: 0 },
            animate: { opacity: 1 },
            exit: { opacity: 0 },
            children: [
              /* @__PURE__ */ jsx("div", { className: "absolute bg-black-100 pointer-events-none opacity-30 h-full w-full" }),
              /* @__PURE__ */ jsx(
                motion.h1,
                {
                  className: "font-semibold text-[10px] z-10 px-3 py-2 rounded-full flex items-center gap-[0.5ch] hover:opacity-95",
                  initial: { y: 10 },
                  animate: { y: 0 },
                  exit: { y: 10 }
                }
              )
            ]
          }
        ) }),
        /* @__PURE__ */ jsx("img", { loading: "lazy", src: image, alt: image, className: "max-h-[86px] max-w-[86px]" })
      ]
    },
    image
  );
};

function Home() {
  const images = [IH, Bk, Tele, Times, GrandDesigns, NGI, IN, S, BN];
  const FAST_DURATION = 45;
  const SLOW_DURATION = 75;
  const [duration, setDuration] = useState(FAST_DURATION);
  let [ref, { width }] = useMeasure();
  const xTranslation = useMotionValue(0);
  const [mustFinish, setMustFinish] = useState(false);
  const [rerender, setRerender] = useState(false);
  useEffect(() => {
    let controls;
    let finalPosition = -width / 2 - 8;
    if (mustFinish) {
      controls = animate(xTranslation, [xTranslation.get(), finalPosition], {
        ease: "linear",
        duration: duration * (1 - xTranslation.get() / finalPosition),
        onComplete: () => {
          setMustFinish(false);
          setRerender(!rerender);
        }
      });
    } else {
      controls = animate(xTranslation, [0, finalPosition], {
        ease: "linear",
        duration,
        repeat: Infinity,
        repeatType: "loop",
        repeatDelay: 0
      });
    }
    return controls?.stop;
  }, [rerender, xTranslation, duration, width]);
  return /* @__PURE__ */ jsxs("div", { className: "relative py-8 min-h-[300px] overflow-hidden", children: [
    /* @__PURE__ */ jsx(
      motion.h2,
      {
        initial: { opacity: 0, y: 50 },
        whileInView: { opacity: 1, y: 0 },
        transition: { duration: 0.5, ease: "easeIn" },
        className: "text-center text-3xl font-semibold leading-8 text-gray-900 mb-12",
        children: "As seen in.."
      }
    ),
    /* @__PURE__ */ jsx(
      motion.div,
      {
        className: "absolute left-0 flex gap-20",
        style: { x: xTranslation },
        ref,
        onHoverStart: () => {
          setMustFinish(true);
          setDuration(SLOW_DURATION);
        },
        onHoverEnd: () => {
          setMustFinish(true);
          setDuration(FAST_DURATION);
        },
        children: [...images, ...images].map((item, idx) => /* @__PURE__ */ jsx(Card, { image: `${item.src}` }, idx))
      }
    )
  ] });
}

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(raw || cooked.slice()) }));
var _a;
const $$Astro = createAstro("https://www.uown.co");
const $$UkPropertyInvestmentGuide = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$UkPropertyInvestmentGuide;
  let pageVersion = "";
  var distinctId = "";
  const projectAPIKey = "phc_Bw6vfs5tnvJF6RRRpJkZzaPdAmEnuNcxFTc7g1jVzSD";
  const cookie = Astro2.cookies.get(`ph_${projectAPIKey}_posthog`);
  const fbcCookie = Astro2.cookies.get(`_fbc`);
  if (cookie && cookie.json().distinct_id) {
    try {
      distinctId = cookie.json().distinct_id;
      const enabledVariant = await PostHogNode().getFeatureFlag(
        "uk-investment-guide-landing-page-1",
        distinctId
      );
      if (enabledVariant === "test") {
        pageVersion = "test";
      } else if (enabledVariant === "control") {
        pageVersion = "control";
      }
    } catch (error) {
      pageVersion = "error";
    }
  }
  const purl = Astro2.url.origin + "/previews/guides.png";
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "UOWN 2025 UK Property Investment Guide", "classes": "bg-framer", "description": "Discover the key trends and insights to help you make informed investments in UK property this year.", "purl": purl, "data-astro-cid-a6c5jt4w": true }, { "default": async ($$result2) => renderTemplate(_a || (_a = __template([" <script>\n    (function () {\n      userAgent = window.navigator.userAgent;\n      window.onload = function() {\n        document.getElementById('userAgent').value = userAgent;\n    };\n  })();\n  <\/script> ", ""])), pageVersion == "test" ? renderTemplate`${renderComponent($$result2, "Hero", Hero, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Hero.jsx", "client:component-export": "Hero", "data-astro-cid-a6c5jt4w": true })}
  ${renderComponent($$result2, "Introduction", Introduction, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Introduction.jsx", "client:component-export": "Introduction", "data-astro-cid-a6c5jt4w": true })}
  ${renderComponent($$result2, "InfiniteScroll", Home, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/InfiniteScroll.jsx", "client:component-export": "default", "data-astro-cid-a6c5jt4w": true })}
  ${renderComponent($$result2, "NavBar", NavBar, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/NavBar.jsx", "client:component-export": "NavBar", "data-astro-cid-a6c5jt4w": true })}
  ${renderComponent($$result2, "TableOfContents", TableOfContents, { "client:load": true, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/TableOfContents.jsx", "client:component-export": "TableOfContents", "data-astro-cid-a6c5jt4w": true })}
  ${renderComponent($$result2, "Testimonial", Testimonial, { "id": "testimonial-from-tommy-stroman", "author": {
    name: "Francis James",
    role: "UOWN Investor"
  }, "data-astro-cid-a6c5jt4w": true }, { "default": async ($$result3) => renderTemplate` ${maybeRenderHead()}<p data-astro-cid-a6c5jt4w>
“Property investment to everyone is totally possible through using UOWN
      where individuals can buy a stake in property - highly recommended!”
</p> ` })}
  ${renderComponent($$result2, "Screencasts", Screencasts, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Screencasts.jsx", "client:component-export": "Screencasts", "data-astro-cid-a6c5jt4w": true })}
  ${renderComponent($$result2, "FreeChapters", FreeChapters, { "distinctId": cookie ? cookie.json().distinct_id : "", "fbc": fbcCookie ? fbcCookie : "", "variant": "test", "client:load": true, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/FreeChapters.jsx", "client:component-export": "FreeChapters", "data-astro-cid-a6c5jt4w": true })}
  ${renderComponent($$result2, "Reviews", Reviews, { "client:load": true, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Reviews", "client:component-export": "Reviews", "data-astro-cid-a6c5jt4w": true })}
  ${renderComponent($$result2, "Author", Author, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Author.jsx", "client:component-export": "Author", "data-astro-cid-a6c5jt4w": true })}` : renderTemplate`${renderComponent($$result2, "Hero", Hero, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Hero.jsx", "client:component-export": "Hero", "data-astro-cid-a6c5jt4w": true })}
  ${renderComponent($$result2, "CTASection", CTASection, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/CtaSection.jsx", "client:component-export": "default", "data-astro-cid-a6c5jt4w": true })}
  ${renderComponent($$result2, "Introduction", Introduction, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Introduction.jsx", "client:component-export": "Introduction", "data-astro-cid-a6c5jt4w": true })}
  ${renderComponent($$result2, "InfiniteScroll", Home, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/InfiniteScroll.jsx", "client:component-export": "default", "data-astro-cid-a6c5jt4w": true })}
  ${renderComponent($$result2, "FreeChapters", FreeChapters, { "distinctId": cookie ? cookie.json().distinct_id : "", "fbc": fbcCookie ? fbcCookie : "", "variant": "control", "client:load": true, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/FreeChapters.jsx", "client:component-export": "FreeChapters", "data-astro-cid-a6c5jt4w": true })}
  ${renderComponent($$result2, "Reviews", Reviews, { "client:load": true, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Reviews", "client:component-export": "Reviews", "data-astro-cid-a6c5jt4w": true })}`) })} `;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/guides/uk-property-investment-guide.astro", void 0);

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/guides/uk-property-investment-guide.astro";
const $$url = "/guides/uk-property-investment-guide";

export { $$UkPropertyInvestmentGuide as default, $$file as file, $$url as url };

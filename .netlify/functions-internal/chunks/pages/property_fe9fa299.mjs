import { $ as $$Layout } from './404_1f20d35e.mjs';
import { e as createAstro, f as createComponent, r as renderTemplate, i as renderComponent, m as maybeRenderHead } from '../astro_ca9e373b.mjs';
import 'clsx';
import { g as getCategoryPosts, a as getReadingTime, b as getFullUrl, u as urlForImage, $ as $$StartJourneyCTA } from './_slug__314c3ce5.mjs';
import { $ as $$PostTile } from './causes_298a8449.mjs';
/* empty css                              *//* empty css                            */import '@sanity/client';
import 'react/jsx-runtime';
import 'motion/react';
import 'react';
import '@headlessui/react';
import '@heroicons/react/24/outline';
import '@heroicons/react/20/solid';
/* empty css                            */import 'html-escaper';
import '@astrojs/internal-helpers/path';
/* empty css                            */import '@portabletext/react';
import '../astro-assets-services_967ef4fc.mjs';
/* empty css                                 */import '@sanity/image-url';
import 'groq';
/* empty css                                 *//* empty css                            */
const $$Astro = createAstro("https://www.uown.co");
const $$Property = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Property;
  const categoryPosts = await getCategoryPosts("0a5d1c34-4085-4fab-83e7-19c4884a1251");
  const purl = Astro2.url.origin + "/previews/property.png";
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Property Insights | The Hub | UOWN", "classes": "", "description": "Explore property insights and investment opportunities with UOWN. Start your property crowdfunding journey today.", "purl": purl, "data-astro-cid-xfilifzt": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main data-astro-cid-xfilifzt> <section class="bg-mint-300 hero bg-no-repeat bg-cover flex flex-col justify-center items-center text-center lg:px-0 px-11 text-white" data-astro-cid-xfilifzt> <p class="max-w-4xl lg:text-7xl md:text-6xl text-5xl tracking-normal font-extrabold pb-4" data-astro-cid-xfilifzt>
The Property Hub
</p> <p class="max-w-2xl md:text-2xl text-xl tracking-normal font-regular pb-12" data-astro-cid-xfilifzt>
The Hub is our knowledge center featuring useful and
				inspirational articles.
</p> </section> <section class="flex flex-wrap gap-x-7 justify-center md:pt-12 lg:pt-24 lg:px-32 md:px-16 px-8 pb-24 pt-8" data-astro-cid-xfilifzt> ${categoryPosts.map((post) => renderTemplate`${renderComponent($$result2, "PostTile", $$PostTile, { "readingTime": getReadingTime("post", post.slug.current), "postHref": getFullUrl(Astro2.url.origin, post.fullSlug), "imgSrc": urlForImage(post.thumbnail).width(350).height(200).url(), "title": post.title, "color": "border-mint-300", "data-astro-cid-xfilifzt": true })}`)} </section> ${renderComponent($$result2, "StartJourneyCTA", $$StartJourneyCTA, { "colorClass": "bg-mint-gradient", "data-astro-cid-xfilifzt": true })} </main> ` })} `;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/category/property.astro", void 0);

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/category/property.astro";
const $$url = "/category/property";

export { $$Property as default, $$file as file, $$url as url };

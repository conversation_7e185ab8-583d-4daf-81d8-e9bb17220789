import { $ as $$Layout } from './404_1f20d35e.mjs';
import { f as createComponent, r as renderTemplate, i as renderComponent } from '../astro_ca9e373b.mjs';
import 'clsx';
import { jsxs, jsx } from 'react/jsx-runtime';
import { motion } from 'motion/react';
import { ArrowUpRightIcon, ArrowRightIcon, FireIcon, BanknotesIcon, SparklesIcon, UserGroupIcon } from '@heroicons/react/24/outline';
import { C as Cardiff } from './demoPage2_2cede3ec.mjs';
/* empty css                            */import '@sanity/client';
import 'react';
import '@headlessui/react';
import '@heroicons/react/20/solid';
/* empty css                            */import 'html-escaper';
import './demoPage_fe3d8eb4.mjs';
import 'use-debounce';
import 'next/link.js';
import 'react-use-measure';
import '@heroicons/react/24/solid';
import '@studio-freight/lenis';
import './invest_01881035.mjs';
import './_slug__d49fd2f5.mjs';
import '@astrojs/internal-helpers/path';
/* empty css                            */import '@portabletext/react';
import '../astro-assets-services_967ef4fc.mjs';
/* empty css                                 */import '@sanity/image-url';
import 'groq';
/* empty css                                 *//* empty css                            */import './index_e61a1fc3.mjs';
/* empty css                           *//* empty css                           */import 'react-stacked-center-carousel';
/* empty css                               */
function HeroSection$2({ title, subtitle, btnTxt, imgTxt, isHero }) {
  return /* @__PURE__ */ jsxs("section", { className: "bg-white flex mlg:flex-row flex-col gap-x-3 gap-y-4 py-12 md:py-24 px-6 md:px-12", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-8", children: [
      /* @__PURE__ */ jsx(
        motion.h1,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.5, ease: "easeIn" },
          className: "text-5xl " + (isHero == "true" ? "font-semibold lg:text-7xl lxl:text-8xl" : "lxl:text-6xl"),
          children: title
        }
      ),
      /* @__PURE__ */ jsx(
        motion.p,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.6, ease: "easeIn" },
          className: "lxl:text-2xl text-lg max-w-xl lxl:max-w-4xl",
          children: subtitle
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.a,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.7, ease: "easeIn" },
          className: "flex items-center",
          children: [
            /* @__PURE__ */ jsx("span", { className: "py-3 px-6 bg-mint-300 rounded-full text-xl lxl:py-5 lxl:px-10 lxl:text-3xl", children: btnTxt }),
            /* @__PURE__ */ jsx("span", { className: "bg-mint-300 rounded-full p-4 lxl:p-7", children: /* @__PURE__ */ jsx(ArrowUpRightIcon, { className: "h-4 w-4 text-black-200", "aria-hidden": "true" }) })
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "easeIn" },
          className: "flex lg:grow items-end justify-between",
          children: [
            /* @__PURE__ */ jsxs("p", { className: "flex  text-lg lg:text-2xl gap-x-5 items-center", children: [
              "Your Path Starts Here",
              /* @__PURE__ */ jsx(ArrowRightIcon, { className: "h-8 w-8 text-black-200 font-bold", "aria-hidden": "true" })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "relative lg:block hidden", children: [
              /* @__PURE__ */ jsx("img", { src: Cardiff.src, alt: "", className: "h-[290px] max-w-[300px] rounded-cxxl object-cover" }),
              /* @__PURE__ */ jsxs("div", { className: "absolute text-xl top-4 right-4 p-3 rounded-csm w-[192px]", children: [
                /* @__PURE__ */ jsx("mark", { className: "bg-white p-2 mb-2 rounded-t-csm rounded-br-csm leading-10", children: "Unlock the true" }),
                /* @__PURE__ */ jsx("mark", { className: "bg-white p-2 mb-2 rounded-br-csm leading-10", children: " potential with " }),
                /* @__PURE__ */ jsx("mark", { className: "bg-white p-2 mb-2 rounded-b-csm leading-10", children: "our app" })
              ] })
            ] })
          ]
        }
      )
    ] }),
    /* @__PURE__ */ jsxs(
      motion.div,
      {
        initial: { opacity: 0, y: 50 },
        whileInView: { opacity: 1, y: 0, threshold: 0.99 },
        transition: { duration: 1, ease: "easeIn" },
        className: "relative",
        children: [
          /* @__PURE__ */ jsx("img", { src: Cardiff.src, alt: "", className: "mlg:h-full rounded-cxxl object-cover w-full md:min-w-[400px] mlg:max-w-[500px] lxl:min-w-[500px] lxl:max-w-[700px]" }),
          /* @__PURE__ */ jsxs("div", { className: "absolute flex top-4 right-4 md:top-6 md:right-6 p-2 md:p-4 gap-x-4 bg-mint-300 rounded-full text-base md:text-xl ", children: [
            imgTxt,
            " ",
            /* @__PURE__ */ jsx(ArrowUpRightIcon, { className: "h-6 w-6", "aria-hidden": "true" })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "absolute" })
        ]
      }
    )
  ] });
}

function HeroSection$1() {
  return /* @__PURE__ */ jsx("section", { className: "bg-white py-12 md:py-24 px-6 md:px-12", children: /* @__PURE__ */ jsxs("div", { className: "bg-gray-20 rounded-clg py-12 md:py-24 px-6 md:px-12", children: [
    /* @__PURE__ */ jsx(
      motion.h1,
      {
        initial: { opacity: 0, y: 50 },
        whileInView: { opacity: 1, y: 0, threshold: 0.99 },
        transition: { duration: 0.3, ease: "easeIn" },
        className: "text-3xl md:text-5xl lg:text-6xl max-w-3xl text-center mx-auto mb-24",
        children: "Real Estate Management Services"
      }
    ),
    /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 mlg:grid-cols-2 lg:grid-cols-3 gap-6", children: [
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.5, ease: "linear" },
          className: "bg-white rounded-clg col-span-1 mlg:col-span-2 p-6 text-left flex flex-col justify-between h-[280px]",
          children: [
            /* @__PURE__ */ jsxs("div", { className: "text-3xl flex justify-between", children: [
              "Property Listings",
              /* @__PURE__ */ jsx(FireIcon, { className: "h-16 w-16 text-black-200 bg-mint-50 rounded-full p-4", "aria-hidden": "true" })
            ] }),
            /* @__PURE__ */ jsx("div", { className: "text-lg  max-w-lg", children: "Easily browse and manage all your property listings with our user-friendly platform." })
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.6, ease: "linear" },
          className: "bg-white rounded-clg col-span-1 p-6 text-left flex flex-col justify-between h-[280px]",
          children: [
            /* @__PURE__ */ jsxs("div", { className: "text-3xl flex justify-between", children: [
              /* @__PURE__ */ jsx("span", { className: "max-w-28", children: "Financial Reporting" }),
              /* @__PURE__ */ jsx(BanknotesIcon, { className: "h-16 w-16 text-black-200 bg-mint-50 rounded-full p-4", "aria-hidden": "true" })
            ] }),
            /* @__PURE__ */ jsx("div", { className: "text-lg  max-w-lg", children: "Generate detailed financial statements and performance reports to stay on top of your property's financial health." })
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.7, ease: "linear" },
          className: "bg-white rounded-clg col-span-1 p-6 text-left flex flex-col justify-between h-[280px]",
          children: [
            /* @__PURE__ */ jsxs("div", { className: "text-3xl flex justify-between", children: [
              /* @__PURE__ */ jsx("span", { className: "max-w-28", children: "Service Requests" }),
              /* @__PURE__ */ jsx(SparklesIcon, { className: "h-16 w-16 text-black-200 bg-mint-50 rounded-full p-4", "aria-hidden": "true" })
            ] }),
            /* @__PURE__ */ jsx("div", { className: "text-lg max-w-lg", children: "Efficiently handle maintenance requests and work orders through our app." })
          ]
        }
      ),
      /* @__PURE__ */ jsxs(
        motion.div,
        {
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.8, ease: "linear" },
          className: "bg-white rounded-clg col-span-1 mlg:col-span-2 p-6 text-left flex flex-col justify-between h-[280px]",
          children: [
            /* @__PURE__ */ jsxs("div", { className: "text-3xl flex justify-between", children: [
              "Tenant Management",
              /* @__PURE__ */ jsx(UserGroupIcon, { className: "h-16 w-16 text-black-200 bg-mint-50 rounded-full p-4", "aria-hidden": "true" })
            ] }),
            /* @__PURE__ */ jsx("div", { className: "text-lg max-w-lg", children: "Keep track of tenant information and lease agreements without hassle. Our app allows you to store and manage all necessary details." })
          ]
        }
      )
    ] })
  ] }) });
}

function HeroSection() {
  return /* @__PURE__ */ jsxs("section", { className: "flex flex-col bg-white py-12 md:py-24 px-6 md:px-12 gap-y-6", children: [
    /* @__PURE__ */ jsx(
      motion.h1,
      {
        initial: { opacity: 0, y: 50 },
        whileInView: { opacity: 1, y: 0, threshold: 0.99 },
        transition: { duration: 0.3, ease: "easeIn" },
        className: "text-3xl md:text-5xl lg:text-6xl max-w-3xl text-center mx-auto mb-12",
        children: "Insights and Updates"
      }
    ),
    /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 mlg:grid-cols-2 lg:grid-cols-3 gap-4 mx-auto", children: [
      /* @__PURE__ */ jsxs("div", { className: "w-full mlg:w-[350px] h-[420px] flex flex-col bg-gray-20 rounded-clg p-6 max-w-2xl gap-y-4", children: [
        /* @__PURE__ */ jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsx("img", { src: Cardiff.src, alt: "Cardiff", className: "w-full h-[200px] object-cover rounded-cmd" }),
          /* @__PURE__ */ jsx("div", { className: "absolute rounded-full text-sm bg-mint-300 px-4 py-2 top-2 right-2", children: "Marketing" })
        ] }),
        /* @__PURE__ */ jsx("p", { className: "text-gray-200 text-sm", children: "Jan 12, 2022" }),
        /* @__PURE__ */ jsx("p", { className: "text-2xl", children: "Maximizing Your Rental Property's Potential" })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "w-full mlg:w-[350px] h-[420px] flex flex-col bg-gray-20 rounded-clg p-6 max-w-2xl gap-y-4", children: [
        /* @__PURE__ */ jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsx("img", { src: Cardiff.src, alt: "Cardiff", className: "w-full h-[200px] object-cover rounded-cmd" }),
          /* @__PURE__ */ jsx("div", { className: "absolute rounded-full text-sm bg-mint-300 px-4 py-2 top-2 right-2", children: "Marketing" })
        ] }),
        /* @__PURE__ */ jsx("p", { className: "text-gray-200 text-sm", children: "Jan 12, 2022" }),
        /* @__PURE__ */ jsx("p", { className: "text-2xl", children: "Maximizing Your Rental Property's Potential" })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "w-full mlg:w-[350px] h-[420px] flex flex-col bg-gray-20 rounded-clg p-6 max-w-2xl gap-y-4", children: [
        /* @__PURE__ */ jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsx("img", { src: Cardiff.src, alt: "Cardiff", className: "w-full h-[200px] object-cover rounded-cmd" }),
          /* @__PURE__ */ jsx("div", { className: "absolute rounded-full text-sm bg-mint-300 px-4 py-2 top-2 right-2", children: "Marketing" })
        ] }),
        /* @__PURE__ */ jsx("p", { className: "text-gray-200 text-sm", children: "Jan 12, 2022" }),
        /* @__PURE__ */ jsx("p", { className: "text-2xl", children: "Maximizing Your Rental Property's Potential" })
      ] })
    ] }),
    /* @__PURE__ */ jsxs(
      motion.a,
      {
        initial: { opacity: 0, y: 50 },
        whileInView: { opacity: 1, y: 0, threshold: 0.99 },
        transition: { duration: 0.7, ease: "easeIn" },
        className: "flex items-center justify-center",
        children: [
          /* @__PURE__ */ jsx("span", { className: "py-3 px-6 bg-mint-300 rounded-full text-xl lxl:py-5 lxl:px-10 lxl:text-3xl", children: "Read More" }),
          /* @__PURE__ */ jsx("span", { className: "bg-mint-300 rounded-full p-4 lxl:p-7", children: /* @__PURE__ */ jsx(ArrowUpRightIcon, { className: "h-4 w-4 text-black-200", "aria-hidden": "true" }) })
        ]
      }
    )
  ] });
}

const $$DemoPage3 = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "", "classes": "bg-framer", "description": "", "purl": "" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "HeroSection", HeroSection$2, { "title": "THE PREMIER CHOICE IN REAL ESTATE SERVICES.", "subtitle": "Managing. Leasing. Selling. Our comprehensive real estate management app is designed to meet all your property needs.", "btnTxt": "Get Started", "imgTxt": "+3k Listings", "isHero": "true", "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HeroSection2.jsx", "client:component-export": "default" })} ${renderComponent($$result2, "TilesSection", HeroSection$1, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/TilesSection.jsx", "client:component-export": "default" })} ${renderComponent($$result2, "InsightsSection", HeroSection, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/InsightsSection.jsx", "client:component-export": "default" })} ${renderComponent($$result2, "HeroSection", HeroSection$2, { "title": "Join Our Community of Successful Property Managers", "subtitle": "Experience the future of real estate management. Whether you're a homeowner, property manager, or real estate investor, we offer the tools you need.", "btnTxt": "Book Appointment", "imgTxt": "Manage with Ease", "isHero": "false", "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HeroSection2.jsx", "client:component-export": "default" })} ` })}`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/demoPage3.astro", void 0);

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/demoPage3.astro";
const $$url = "/demoPage3";

export { $$DemoPage3 as default, $$file as file, $$url as url };

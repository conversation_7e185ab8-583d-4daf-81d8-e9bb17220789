import { a as $$Button, $ as $$Layout } from './404_1f20d35e.mjs';
import { f as createComponent, r as renderTemplate, i as renderComponent, m as maybeRenderHead } from '../astro_ca9e373b.mjs';
import 'clsx';
/* empty css                            */import '@sanity/client';
import 'react/jsx-runtime';
import 'motion/react';
import 'react';
import '@headlessui/react';
import '@heroicons/react/24/outline';
import '@heroicons/react/20/solid';
/* empty css                            */import 'html-escaper';

const $$ThankYou = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Thank You - Your Submission Was Received | UOWN", "classes": "", "description": "Thank you for contacting UOWN. We have received your submission and will respond shortly.", "purl": "" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="relative isolate flex items-center min-h-full h-[88vh] bg-mint-300"> <div class="flex flex-col justify-center items-center mx-auto max-w-7xl px-6 py-32 text-center sm:py-40 lg:px-8"> <h1 class="mt-4 m-auto font-extrabold text-7xl lg:text-9xl tracking-normal">Thank You</h1> <div class="mt-24 flex justify-center"> <a href="/">${renderComponent($$result2, "Button", $$Button, { "type": "button", "color": "btn-black btn-homepage", "text": "Home Page" })}</a> </div> </div> </main> ` })}`;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/thank-you.astro", void 0);

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/thank-you.astro";
const $$url = "/thank-you";

export { $$ThankYou as default, $$file as file, $$url as url };

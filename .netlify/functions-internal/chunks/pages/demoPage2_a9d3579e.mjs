import { $ as $$Layout } from './404_1f20d35e.mjs';
import { f as createComponent, r as renderTemplate, i as renderComponent } from '../astro_ca9e373b.mjs';
import 'clsx';
import { jsxs, jsx } from 'react/jsx-runtime';
import { P as PhoneFrame } from './demoPage_fe3d8eb4.mjs';
import { motion, useMotionValue, animate, useScroll, useTransform, AnimatePresence } from 'motion/react';
import { useState, useEffect, useRef } from 'react';
import useMeasure from 'react-use-measure';
import { CheckCircleIcon } from '@heroicons/react/24/solid';
import Lenis from '@studio-freight/lenis';
import { StarIcon } from '@heroicons/react/20/solid';
import './invest_0a0774cb.mjs';
/* empty css                               */
const Times = {"src":"/_astro/logo_times.f83f47aa.png","width":600,"height":71,"format":"png"};

const S = {"src":"/_astro/logo_scotsman.e14217ab.png","width":501,"height":101,"format":"png"};

const Tele = {"src":"/_astro/logo_telegraph.8a61ad9a.png","width":558,"height":90,"format":"png"};

const NGI = {"src":"/_astro/logo_ngi.010e31c4.png","width":685,"height":295,"format":"png"};

const Bk = {"src":"/_astro/logo_booking.1a6ceada.png","width":299,"height":169,"format":"png"};

const Cardiff = {"src":"/_astro/cardiff-main.35e2e882.webp","width":1900,"height":1267,"format":"webp"};

const Table = {"src":"/_astro/Setting-Dinner-Table.d9299642.webp","width":1900,"height":1267,"format":"webp"};

const Card$2 = ({ image }) => {
  return /* @__PURE__ */ jsxs(
    motion.div,
    {
      className: "relative overflow-hidden h-[200px] w-[200px] flex justify-center items-center",
      children: [
        /* @__PURE__ */ jsx(
          motion.div,
          {
            className: "absolute left-0 top-0 bottom-0 right-0 z-10 flex justify-center items-center",
            initial: { opacity: 0 },
            animate: { opacity: 1 },
            exit: { opacity: 0 },
            children: /* @__PURE__ */ jsx(
              motion.h1,
              {
                className: "font-semibold text-[10px] z-10 px-3 py-2 rounded-full flex items-center gap-[0.5ch]",
                initial: { y: 10 },
                animate: { y: 0 },
                exit: { y: 10 }
              }
            )
          }
        ),
        /* @__PURE__ */ jsx("img", { loading: "lazy", src: image, alt: image, className: "max-h-[150px]" })
      ]
    },
    image
  );
};

const BN = {"src":"/_astro/logo_business_north.0d2479be.png","width":391,"height":129,"format":"png"};

const GrandDesigns = {"src":"/_astro/grand_designs.cddd3df9.webp","width":500,"height":500,"format":"webp"};

const IH = {"src":"/_astro/ideal-home.e1b01a4a.svg","width":257,"height":50,"format":"svg"};

function Home$2({ bgClass, title }) {
  const images = [Bk, Tele, Times, GrandDesigns, IH, NGI, S, BN];
  const FAST_DURATION = 45;
  const SLOW_DURATION = 75;
  const [duration, setDuration] = useState(FAST_DURATION);
  let [ref, { width }] = useMeasure();
  const xTranslation = useMotionValue(0);
  const [mustFinish, setMustFinish] = useState(false);
  const [rerender, setRerender] = useState(false);
  useEffect(() => {
    let controls;
    let finalPosition = -width / 2 - 8;
    if (mustFinish) {
      controls = animate(xTranslation, [xTranslation.get(), finalPosition], {
        ease: "linear",
        duration: duration * (1 - xTranslation.get() / finalPosition),
        onComplete: () => {
          setMustFinish(false);
          setRerender(!rerender);
        }
      });
    } else {
      controls = animate(xTranslation, [0, finalPosition], {
        ease: "linear",
        duration,
        repeat: Infinity,
        repeatType: "loop",
        repeatDelay: 0
      });
    }
    return controls?.stop;
  }, [rerender, xTranslation, duration, width]);
  return /* @__PURE__ */ jsx("section", { className: "max-w-7xl mx-auto", children: /* @__PURE__ */ jsx("div", { className: bgClass + " rounded-clg m-6 md:m-12 py-6", children: /* @__PURE__ */ jsxs("div", { className: "relative py-12 min-h-[300px] overflow-hidden mx-3 mlg:mx-12", style: { maskImage: "linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgb(0, 0, 0) 30%, rgb(0, 0, 0) 70%, rgba(0, 0, 0, 0) 100%)" }, children: [
    /* @__PURE__ */ jsx(
      motion.h2,
      {
        initial: { opacity: 0, y: 50 },
        whileInView: { opacity: 1, y: 0 },
        transition: { duration: 0.5, ease: "easeIn" },
        className: "text-center text-lg md:text-2xl font-semibold leading-8 text-gray-900 mb-3 md:mb-12",
        children: title
      }
    ),
    /* @__PURE__ */ jsx(
      motion.div,
      {
        className: "absolute left-0 flex gap-x-16",
        style: { x: xTranslation },
        ref,
        onHoverStart: () => {
          setMustFinish(true);
          setDuration(SLOW_DURATION);
        },
        onHoverEnd: () => {
          setMustFinish(true);
          setDuration(FAST_DURATION);
        },
        children: [...images, ...images].map((item, idx) => /* @__PURE__ */ jsx(Card$2, { image: `${item.src}` }, idx))
      }
    )
  ] }) }) });
}

function Example() {
  return /* @__PURE__ */ jsx("div", { className: "bg-hero-section", children: /* @__PURE__ */ jsx("div", { className: "relative isolate pt-14", children: /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:flex lg:items-center lg:gap-x-10 lg:px-8 lg:py-40", children: [
    /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-2xl lg:mx-0 lg:flex-auto", children: [
      /* @__PURE__ */ jsx(
        motion.div,
        {
          className: "flex",
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.5, ease: "easeIn" },
          children: /* @__PURE__ */ jsx(
            "div",
            {
              className: "relative flex items-center gap-x-4 rounded-full bg-white px-4 py-1 text-sm/6 text-black-50 ring-1 ring-gray-900/10 hover:ring-gray-900/20",
              children: /* @__PURE__ */ jsx("a", { href: "#", className: "flex items-center gap-x-1 tracking-wide", children: "UOWN - A Home For Your Money" })
            }
          )
        }
      ),
      /* @__PURE__ */ jsx(
        motion.h1,
        {
          className: "mt-10 text-pretty text-5xl font-light !leading-tight tracking-normal text-gray-900 sm:text-7xl",
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.6, ease: "easeIn" },
          children: "Invest in the UK and earn up to 12% p.a."
        }
      ),
      /* @__PURE__ */ jsx(
        motion.div,
        {
          className: "mt-10 flex items-center gap-x-6",
          initial: { opacity: 0, y: 50 },
          whileInView: { opacity: 1, y: 0, threshold: 0.99 },
          transition: { duration: 0.7, ease: "easeIn" },
          children: /* @__PURE__ */ jsx(
            "a",
            {
              href: "#",
              className: "rounded-full bg-black-100 px-5 py-4 text-base font-light text-white shadow-sm hover:bg-black-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-black-100",
              children: "Get a free account"
            }
          )
        }
      ),
      /* @__PURE__ */ jsx("div", { className: "divider" })
    ] }),
    /* @__PURE__ */ jsx(
      motion.div,
      {
        className: "mt-16 sm:mt-24 lg:mt-0 lg:shrink-0 lg:grow",
        initial: { opacity: 0, y: 50 },
        whileInView: { opacity: 1, y: 0, threshold: 0.99 },
        transition: { duration: 1, ease: "easeIn" },
        children: /* @__PURE__ */ jsx(PhoneFrame, { className: "z-10 mx-auto w-full max-w-[366px]", children: /* @__PURE__ */ jsx("div", { className: "absolute left-[calc(23/366*100%)] top-[calc(23/729*100%)] grid h-[calc(686/729*100%)] w-[calc(318/366*100%)] transform grid-cols-1 overflow-hidden bg-white pt-[calc(23/318*100%)]", children: /* @__PURE__ */ jsx("div", { className: "col-start-1 row-start-1 flex focus:outline-offset-[32px] ui-not-focus-visible:outline-none", children: /* @__PURE__ */ jsxs("div", { className: "flex flex-col bg-white w-full", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex justify-between px-4 pt-4", children: [
            /* @__PURE__ */ jsxs("svg", { width: "48", height: "24", viewBox: "0 0 48 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", className: "h-6 flex-none", children: [
              /* @__PURE__ */ jsx("path", { d: "M20.3391 14.2694C20.3391 16.8492 19.3167 19.3234 17.4967 21.1476C15.6768 22.9718 13.2084 23.9966 10.6346 23.9966C8.06085 23.9966 5.59249 22.9718 3.77255 21.1476C1.95261 19.3234 0.930176 16.8492 0.930176 14.2694L0.930176 0.845967C0.945786 0.626796 1.03971 0.420614 1.19472 0.265242C1.34973 0.10987 1.55543 0.0157264 1.77409 7.91782e-05H5.57114C5.68239 -0.00145697 5.79282 0.0193733 5.8959 0.0613392C5.99898 0.103305 6.09262 0.165555 6.1713 0.244411C6.24997 0.323267 6.31207 0.417129 6.35394 0.520451C6.39581 0.623773 6.41659 0.734458 6.41506 0.845967V14.2694C6.43023 15.3875 6.88541 16.4542 7.6815 17.2375C8.07335 17.6163 8.53586 17.9141 9.04258 18.1137C9.54929 18.3133 10.0903 18.4108 10.6346 18.4007C11.179 18.4108 11.72 18.3133 12.2267 18.1137C12.7334 17.9141 13.1959 17.6163 13.5878 17.2375C14.3839 16.4542 14.839 15.3875 14.8542 14.2694V0.845967C14.8527 0.734556 14.8734 0.623965 14.9152 0.52072C14.957 0.417475 15.019 0.323665 15.0976 0.244825C15.1761 0.165984 15.2696 0.10371 15.3726 0.0616755C15.4755 0.0196407 15.5858 -0.00130318 15.697 7.91782e-05H19.494C19.6053 -0.00145697 19.7157 0.0193733 19.8188 0.0613392C19.9219 0.103305 20.0155 0.165555 20.0942 0.244411C20.1729 0.323267 20.235 0.417129 20.2768 0.520451C20.3187 0.623773 20.3395 0.734458 20.338 0.845967L20.3391 14.2694Z", fill: "#71E5BD" }),
              /* @__PURE__ */ jsx("path", { d: "M38.3759 17.9737C37.3749 18.5285 36.2499 18.8195 35.1061 18.8195C33.9624 18.8195 32.8373 18.5285 31.8364 17.9737C30.7783 17.392 29.8965 16.5349 29.2839 15.4928C28.6713 14.4506 28.3507 13.262 28.3559 12.0525C28.3559 10.2299 29.0783 8.48193 30.364 7.19315C31.6498 5.90438 33.3937 5.18035 35.212 5.18035C37.0304 5.18035 38.7743 5.90438 40.06 7.19315C41.3458 8.48193 42.0681 10.2299 42.0681 12.0525C42.0103 13.2738 41.6403 14.4598 40.9938 15.4966C40.3473 16.5334 39.4458 17.3864 38.3759 17.9737ZM35.1061 6.61337e-06C32.7487 6.61337e-06 30.4441 0.700712 28.484 2.01351C26.5238 3.32632 24.996 5.19225 24.0939 7.37536C23.1917 9.55847 22.9556 11.9607 23.4156 14.2783C23.8755 16.5959 25.0107 18.7247 26.6777 20.3956C28.3447 22.0665 30.4686 23.2043 32.7807 23.6653C35.0929 24.1263 37.4895 23.8897 39.6676 22.9855C41.8456 22.0812 43.7072 20.5498 45.0169 18.5851C46.3267 16.6204 47.0257 14.3104 47.0257 11.9475C47.0274 10.378 46.7202 8.82369 46.1218 7.37341C45.5233 5.92313 44.6454 4.6054 43.5382 3.49565C42.4311 2.3859 41.1164 1.50592 39.6695 0.906087C38.2226 0.306257 36.6719 -0.00164549 35.1061 6.61337e-06Z", fill: "#101010" })
            ] }),
            /* @__PURE__ */ jsx("svg", { viewBox: "0 0 24 24", fill: "none", "aria-hidden": "true", className: "h-6 w-6 flex-none", children: /* @__PURE__ */ jsx("path", { d: "M5 6h14M5 18h14M5 12h14", stroke: "#000", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round" }) })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "mt-6 px-4 text-white bg-white", style: { opacity: 1, willChange: "transform" }, children: [
            /* @__PURE__ */ jsx("div", { className: "text-2xl text-black-50 font-bold", children: "Sign Up" }),
            /* @__PURE__ */ jsx("div", { className: "text-sm text-gray-500", children: "Sign up to join the UOWN team." })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "mt-6 flex-auto rounded-t-2xl bg-white", style: { opacity: 1, zIndex: 1073741824, filter: `blur(${0})`, willChange: "transform", transform: `none` }, children: /* @__PURE__ */ jsxs("div", { className: "px-4 py-6", children: [
            /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("div", { className: "text-sm text-gray-500", children: "Full name" }),
                /* @__PURE__ */ jsx("div", { className: "mt-2 border-b border-gray-200 pb-2 text-sm text-gray-900", children: "Albert H. Wiggin" })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("div", { className: "text-sm text-gray-500", children: "Email address" }),
                /* @__PURE__ */ jsx("div", { className: "mt-2 border-b border-gray-200 pb-2 text-sm text-gray-900", children: "<EMAIL>" })
              ] })
            ] }),
            /* @__PURE__ */ jsx("div", { className: "mt-6 rounded-full bg-mint-300 px-3 py-2 text-center text-sm font-semibold text-black-50", children: "Sign Up" })
          ] }) })
        ] }) }) }) })
      }
    )
  ] }) }) });
}

const Card$1 = ({ i, title, description, src, bullets, bulletColor, checkMarkColor, color, progress, range, targetScale }) => {
  const container = useRef(null);
  const { scrollYProgress } = useScroll({
    target: container,
    offset: ["start end", "start start"]
  });
  const imageScale = useTransform(scrollYProgress, [0, 1], [2, 1]);
  const scale = useTransform(progress, range, [1, targetScale]);
  return /* @__PURE__ */ jsx("div", { ref: container, className: "h-screen flex items-normal center justify-center sticky top-[150px]", children: /* @__PURE__ */ jsx(
    motion.div,
    {
      style: { backgroundColor: color, scale, top: `calc(-5vh + ${i * 25}px)` },
      className: "flex flex-col relative top-[-25%] h-[620px] smd:h-[500px] lg:h-[600px] max-w-[1000px] rounded-[25px] pt-0 md:p-[50px] p-[20px] origin-top",
      children: /* @__PURE__ */ jsxs("div", { className: "flex h-full mt-[50px] gap-[50px]", children: [
        /* @__PURE__ */ jsxs("div", { className: "lg:w-[50%] relative flex flex-col", children: [
          /* @__PURE__ */ jsx("h3", { className: "text-left m-0 text-2xl md:text-[28px] mb-4", children: title }),
          /* @__PURE__ */ jsx("p", { className: "text-sm md:text-base text-gray-700", children: description }),
          /* @__PURE__ */ jsxs("div", { className: "pt-4 flex flex-col gap-y-2 text-black-50", children: [
            /* @__PURE__ */ jsxs("div", { className: "md:w-max md:text-sm text-xs flex gap-x-2 rounded-full p-2 pr-4 items-center", style: { backgroundColor: bulletColor }, children: [
              /* @__PURE__ */ jsx("span", { children: /* @__PURE__ */ jsx(CheckCircleIcon, { className: "h-6 w-6", style: { color: checkMarkColor }, "aria-hidden": "true" }) }),
              bullets[0]
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "md:w-max md:text-sm text-xs flex gap-x-2 rounded-full p-2 pr-4 items-center", style: { backgroundColor: bulletColor }, children: [
              /* @__PURE__ */ jsx("span", { children: /* @__PURE__ */ jsx(CheckCircleIcon, { className: "h-6 w-6", style: { color: checkMarkColor }, "aria-hidden": "true" }) }),
              bullets[1]
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "md:w-max md:text-sm text-xs flex gap-x-2 rounded-full p-2 pr-4 items-center", style: { backgroundColor: bulletColor }, children: [
              /* @__PURE__ */ jsx("span", { children: /* @__PURE__ */ jsx(CheckCircleIcon, { className: "h-6 w-6", style: { color: checkMarkColor }, "aria-hidden": "true" }) }),
              bullets[2]
            ] })
          ] }),
          /* @__PURE__ */ jsxs("span", { className: "flex items-center gap-[10px] mt-10 text-black-50", children: [
            /* @__PURE__ */ jsx("a", { className: "cursor-pointer text-sm font-semibold ", href: "", target: "_blank", children: "View Projects" }),
            /* @__PURE__ */ jsx("svg", { width: "22", height: "12", viewBox: "0 0 22 12", fill: "none", xmlns: "http://www.w3.org/2000/svg", children: /* @__PURE__ */ jsx("path", { d: "M21.5303 6.53033C21.8232 6.23744 21.8232 5.76256 21.5303 5.46967L16.7574 0.696699C16.4645 0.403806 15.9896 0.403806 15.6967 0.696699C15.4038 0.989592 15.4038 1.46447 15.6967 1.75736L19.9393 6L15.6967 10.2426C15.4038 10.5355 15.4038 11.0104 15.6967 11.3033C15.9896 11.5962 16.4645 11.5962 16.7574 11.3033L21.5303 6.53033ZM0 6.75L21 6.75V5.25L0 5.25L0 6.75Z", fill: "black" }) })
          ] })
        ] }),
        /* @__PURE__ */ jsx("div", { className: "relative w-[50%] h-full rounded-[25px] overflow-hidden lg:block hidden", children: /* @__PURE__ */ jsx(
          motion.div,
          {
            className: "w-full h-full",
            style: { scale: imageScale },
            children: /* @__PURE__ */ jsx(
              "img",
              {
                src: src.src,
                alt: "image",
                className: "object-cover"
              }
            )
          }
        ) })
      ] })
    }
  ) });
};

const projects = [
  {
    title: "Invest with the UK's leading property investment platform",
    description: "UOWN was created by a leading property group to open the doors to exclusive property deals. Using innovative technology, we make property investment accessible to everyday investors like you.",
    src: Cardiff,
    bullets: [
      "Over five decades of property experience.",
      "Access to deals normally unavailable to ordinary investors.",
      "One of the UK's first property crowdfudning platforms"
    ],
    bulletColor: "#EDEDED",
    checkMarkColor: "#827C6A",
    color: "#F4F2EF"
  },
  {
    title: "Invest your way—online or on the go.",
    description: "Invest with ease through our web platform or on the go with our mobile app. Get started today by connecting with your dedicated investment manager!",
    src: Table,
    bullets: [
      "Access to your own persoanl investment manager.",
      "Invest online or on the go in minutes using our app.",
      "Select the deals that suit your investment needs."
    ],
    bulletColor: "#F7E79F",
    checkMarkColor: "#FFD008",
    color: "#FFFBEE"
  },
  {
    title: "Suitable for everyone",
    description: "No matter where you are in life, UOWN is here for you. We’ve made property investment accessible to everyone—whether you’re saving for the future or enjoying retirement after years of hard work.",
    src: Cardiff,
    bullets: [
      "Saving for retirement",
      "Saving for a big life event",
      "Just saving for a rainy day"
    ],
    bulletColor: "#A7F9D8",
    checkMarkColor: "#13B688",
    color: "#EEFFF8"
  },
  {
    title: "Invest your way—online or on the go.",
    description: "Invest with ease through our web platform or on the go with our mobile app. Get started today by connecting with your dedicated investment manager!",
    src: Table,
    bullets: [
      "Access to your own persoanl investment manager.",
      "Invest online or on the go in minutes using our app.",
      "Select the deals that suit your investment needs."
    ],
    bulletColor: "#EEF7FF",
    checkMarkColor: "#5A6ECC",
    color: "#DCE4EA"
  }
];
function Home$1() {
  const container = useRef(null);
  const { scrollYProgress } = useScroll({
    target: container,
    offset: ["start start", "end end"]
  });
  useEffect(() => {
    const lenis = new Lenis();
    function raf(time) {
      lenis.raf(time);
      requestAnimationFrame(raf);
    }
    requestAnimationFrame(raf);
  });
  return /* @__PURE__ */ jsx("div", { className: "lg:px-28 smd:px-6 px-3", children: /* @__PURE__ */ jsxs("div", { className: "px-3 smd:px-6 slideContainer rounded-b-clg pb-16", children: [
    /* @__PURE__ */ jsxs(motion.div, { className: "flex flex-col items-center justify-center text-center mt-36", children: [
      /* @__PURE__ */ jsx("h2", { className: "text-3xl lg:text-4xl tracking- font-bold pb-4", children: "A platform with experience" }),
      /* @__PURE__ */ jsx("p", { className: "max-w-lg text-lg tracking-normal pb-12", children: "Our platform has been built by a team with over five decades of experience in the UK property market.  " })
    ] }),
    /* @__PURE__ */ jsx("div", { ref: container, className: "relative mt-12 lg:max-w-4xl mx-4 md:mx-10 lg:mx-auto", children: projects.map((project, i) => {
      const targetScale = 1 - (projects.length - i) * 0.05;
      return /* @__PURE__ */ jsx(Card$1, { i, ...project, progress: scrollYProgress, range: [i * 0.25, 1], targetScale }, `p_${i}`);
    }) }),
    /* @__PURE__ */ jsx("div", { className: "mt-24 smd:mt-0 lg:mt-8 mx-auto flex justify-center items-center", children: /* @__PURE__ */ jsx("a", { href: "#", className: "rounded-full bg-black-100 px-5 py-4 text-lg font-light text-white shadow-sm hover:bg-black-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-black-100", children: "View All Case Studies" }) })
  ] }) });
}

const slides = [
  {
    id: 1,
    title: "Identities",
    description: "We create motion identities that transform your brand.",
    src: Cardiff
  },
  {
    id: 2,
    title: "Systems",
    description: "We build motion systems that scale your identity.",
    src: Table
  },
  {
    id: 3,
    title: "Applications",
    description: "We activate your brand across a range of global touchpoints.",
    src: Cardiff
  }
];
function Home() {
  const container = useRef(null);
  const { scrollYProgress } = useScroll({
    target: container,
    offset: ["start start", "end end"]
  });
  useEffect(() => {
    const lenis = new Lenis();
    function raf(time) {
      lenis.raf(time);
      requestAnimationFrame(raf);
    }
    requestAnimationFrame(raf);
  });
  return /* @__PURE__ */ jsx("div", { className: "bg-black-100 lg:px-28 smd:px-6 px-3", children: /* @__PURE__ */ jsx("div", { className: "pb-16", children: /* @__PURE__ */ jsx("div", { ref: container, className: "relative lg:max-w-4xl mx-auto", children: slides.map((slide, i) => {
    const targetScale = 1 - (slides.length - i) * 0.05;
    return /* @__PURE__ */ jsx(Card, { i, ...slide, progress: scrollYProgress, range: [i * 0.25, 1], targetScale }, `p_${i}`);
  }) }) }) });
}
const Card = ({ i, id, title, description, src, progress, range, targetScale }) => {
  const container = useRef(null);
  const { scrollYProgress } = useScroll({
    target: container,
    offset: ["start end", "start start"]
  });
  useTransform(scrollYProgress, [0, 1], [2, 1]);
  const scale = useTransform(progress, range, [1, targetScale]);
  return /* @__PURE__ */ jsx("div", { ref: container, className: "bg-black-100 h-screen flex items-normal center justify-center sticky top-[100px]", children: /* @__PURE__ */ jsxs(
    motion.div,
    {
      style: { scale, top: `calc(0vh + ${i * 25}px)` },
      className: "bg-black-100 flex lg:flex-row flex-col justify-between gap-x-24 relative top-[0%] h-[620px] smd:h-[500px] lg:h-[600px] max-w-[1000px] pt-0 px-3 md:px-0 origin-top",
      children: [
        /* @__PURE__ */ jsxs("div", { className: "flex gap-x-12 smd:gap-x-24 lg:gap-x-12 items-start", children: [
          /* @__PURE__ */ jsx("div", { className: "text-[120px] lg:text-[240px] font-bold text-white", children: id }),
          /* @__PURE__ */ jsxs("div", { className: "text-2xl lg:text-lg text-white mt-[45px] lg:mt-[100px]", children: [
            "(",
            title,
            ")"
          ] })
        ] }),
        /* @__PURE__ */ jsx("div", { className: "flex h-full lg:mt-[100px]", children: /* @__PURE__ */ jsxs("div", { className: "relative flex flex-col text-white", children: [
          /* @__PURE__ */ jsx("h3", { className: "text-left m-0 text-2xl md:text-[28px] mb-4", children: description }),
          /* @__PURE__ */ jsx("div", { className: "max-w-[300px] smd:max-w-[400px] mt-[20px]", children: /* @__PURE__ */ jsx(
            motion.div,
            {
              className: "w-full h-full",
              children: /* @__PURE__ */ jsx(
                "img",
                {
                  src: src.src,
                  alt: "image",
                  className: "object-cover  rounded-cmd"
                }
              )
            }
          ) })
        ] }) })
      ]
    }
  ) });
};

const ReviewContainer = ({ review }) => {
  return /* @__PURE__ */ jsx("div", { className: "reviewItem", children: /* @__PURE__ */ jsxs("div", { className: "reviewContainer flex flex-col items-start justify-between bg-[#F4F2EF] rounded-cmd p-6 w-[384px] h-[420px]", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex gap-x-0.5 w-[116px] h-[20px]", children: [
      /* @__PURE__ */ jsx(StarIcon, { className: "text-yellow-400" }),
      /* @__PURE__ */ jsx(StarIcon, { className: "text-yellow-400" }),
      /* @__PURE__ */ jsx(StarIcon, { className: "text-yellow-400" }),
      /* @__PURE__ */ jsx(StarIcon, { className: "text-yellow-400" }),
      /* @__PURE__ */ jsx(StarIcon, { className: "text-yellow-400" })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "text-[15px] text-black-50 opacity-80 tracking-normal", children: [
      /* @__PURE__ */ jsx("p", { children: review.reviewLine1 }),
      /* @__PURE__ */ jsx("br", {}),
      /* @__PURE__ */ jsx("p", { children: review.reviewLine2 })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "bg-[#dce4ea] relative overflow-hidden w-full !h-[2px]" }),
    /* @__PURE__ */ jsxs("div", { children: [
      /* @__PURE__ */ jsx("div", { className: "font-semibold text-xl", children: review.author }),
      /* @__PURE__ */ jsx("div", { className: "text-[15px] text-gray-200", children: review.reviewTitle })
    ] })
  ] }, review.id) });
};

const reviews = [
  {
    id: 1,
    reviewTitle: "UOWN has delivered so far.",
    reviewLine1: "I have trial investments with seven property crowdfunding platforms. UOWN is the only one that has delivered on time and on budget. All the others, in addition to using COVID as an excuse, have had delays and dubious excuses some bordering on the slimy.",
    reviewLine2: "When UOWN comes up with another development project if it looks interesting I'll be in (unlike four of the other platforms that I won't touch again.)",
    author: "Dr. Michael Prior"
  },
  {
    id: 2,
    reviewTitle: "UOWN allows me to invest in property through an expert team.",
    reviewLine1: "I have been investing with UOWN for several years now and they have never let me down. I have had good returns on my investments in line with what was projected. It is easy to withdraw cash my wallet when needed.",
    reviewLine2: "If there are delays which is uncommon in projects I am remunerated fairly for this. I think communication is good and the website simple and intuitive.",
    author: "Paul"
  },
  {
    id: 3,
    reviewTitle: "Used Uown for years, great experience.",
    reviewLine1: "I used uown for 3-4 years, and earned a very solid return. When it came time to withdraw basically all my portfolio, I was able to do so very quickly, with no hassle.",
    reviewLine2: "The regular rental returns were rock-solid, exactly as advertised, and were topped off nicely by capital appreciation when selling shares (i.e. withdrawing) or when the property is sold.",
    author: "Jasim"
  },
  {
    id: 4,
    reviewTitle: "Invested an inheritance and I'm happy…",
    reviewLine1: "Invested an inheritance and I'm happy with my investment so far. They deliver the returns as listed. However don't expect to be able to sell out of your investment quickly at the moment.",
    reviewLine2: "This is a long term investment looked after by honest people that will help you build and grow your wealth over the long term in my opinion.",
    author: "Jonathan"
  },
  {
    id: 5,
    reviewTitle: "Excellent Service",
    reviewLine1: "Excellent service. The right platform to invest your savings and get value back. You don't have to be an investment expert to get started and that's what I love",
    reviewLine2: "Especially good customer service in terms of getting in touch regarding any concerns and helping out. Must try.",
    author: "Hugo Shepard"
  },
  {
    id: 6,
    reviewTitle: "I'm a big fan of UOWN",
    reviewLine1: "I am a big fan of UOwn - I have been investing with them for more than 6 months and not once have they been late with dividend payments or updating investors with matters that concern them.",
    reviewLine2: "I personally would like to stay with UOwn for a long time coming simply because of their consistency and timely manner.",
    author: "Sharose"
  },
  {
    id: 7,
    reviewTitle: "Very pleased and will continue to invest with UOWN!",
    reviewLine1: "I’ve invested with UOWN for some years now, and have always been very pleased with both the investments and the service from UOWN.",
    reviewLine2: "Property crowdfunding has become more difficult recently due to new regulations but UOWN have managed to quickly make new arrangements to deal with this and offer new projects still.",
    author: "Joe Dooley"
  }
];
const HorizontalScroll = () => {
  const targetRef = useRef(null);
  const { scrollYProgress } = useScroll({ target: targetRef });
  const x = useTransform(scrollYProgress, [0, 1], ["0%", "-50%"]);
  return /* @__PURE__ */ jsxs("div", { className: "carouselContainer bg-[#F9F7F6] py-24 mt-28", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col justify-center items-center text-center gap-y-3 max-w-4xl mx-auto px-6 text-black-50", children: [
      /* @__PURE__ */ jsx("h1", { className: "text-5xl tracking-tight", children: "Trusted by Thousands of Investors" }),
      /* @__PURE__ */ jsx("p", { className: "text-xl", children: "Established in 2016, our platform has been the premier investment choice for thousands of people looking to invest in UK property. See what they have to say about us." })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "carousel h-[500vh]", ref: targetRef, children: /* @__PURE__ */ jsx("div", { className: "contentContainer h-[100vh] sticky top-[50px] flex items-center justify-start overflow-hidden", children: /* @__PURE__ */ jsx(motion.div, { className: "reviews flex gap-x-8 px-10", style: { x }, children: reviews.map((review) => {
      return /* @__PURE__ */ jsx(ReviewContainer, { review }, review.id);
    }) }) }) })
  ] });
};

const Projects = {"src":"/_astro/pyp.abacee33.webp","width":1726,"height":1118,"format":"webp"};

const Investment = {"src":"/_astro/inl.ed0a1bb6.webp","width":1803,"height":1251,"format":"webp"};

const Dashboard = {"src":"/_astro/db.4975dbc6.webp","width":1444,"height":1064,"format":"webp"};

const Instructions = () => {
  return /* @__PURE__ */ jsx("div", { className: "bg-white", children: /* @__PURE__ */ jsxs("div", { className: "relative isolate overflow-hidden bg-gradient-to-b from-indigo-100/20", children: [
    /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-7xl pb-24 pt-10 sm:pb-32 flex lg:gap-x-8 lg:px-8 lg:py-40", children: [
      /* @__PURE__ */ jsx("div", { className: "block px-6 lg:px-0 lg:pt-4 w-full max-w-2xl", children: /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-2xl", children: [
        /* @__PURE__ */ jsx(
          "img",
          {
            className: "h-11",
            src: "https://tailwindui.com/plus/img/logos/mark.svg?color=indigo&shade=600",
            alt: "Your Company"
          }
        ),
        /* @__PURE__ */ jsxs("div", { className: "max-w-lg w-full flex flex-col justify-between", children: [
          /* @__PURE__ */ jsx("h1", { className: "mt-10 text-pretty text-3xl tracking-tight text-gray-900 sm:text-5xl", children: "Pick your Projects" }),
          /* @__PURE__ */ jsx("p", { className: "mt-6 text-lg/8 text-black-50", children: "Portfolio Management" }),
          /* @__PURE__ */ jsx("p", { className: "mt-6 text-lg/8 text-gray-600", children: "Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo." })
        ] })
      ] }) }),
      /* @__PURE__ */ jsx(
        "img",
        {
          src: Projects.src,
          alt: "image",
          className: "object-cover rounded-clg flex"
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-7xl pb-24 pt-10 sm:pb-32 flex lg:gap-x-8 lg:px-8 lg:py-40", children: [
      /* @__PURE__ */ jsx("div", { className: "px-6 lg:px-0 lg:pt-4", children: /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-2xl", children: [
        /* @__PURE__ */ jsx(
          "img",
          {
            className: "h-11",
            src: "https://tailwindui.com/plus/img/logos/mark.svg?color=indigo&shade=600",
            alt: "Your Company"
          }
        ),
        /* @__PURE__ */ jsxs("div", { className: "max-w-lg flex flex-col justify-between", children: [
          /* @__PURE__ */ jsx("h1", { className: "mt-10 text-pretty text-3xl tracking-tight text-gray-900 sm:text-5xl", children: "Set your Investment Level" }),
          /* @__PURE__ */ jsx("p", { className: "mt-6 text-lg/8 text-black-50", children: "Portfolio Management" }),
          /* @__PURE__ */ jsx("p", { className: "mt-6 text-lg/8 text-gray-600", children: "Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo." })
        ] })
      ] }) }),
      /* @__PURE__ */ jsx(
        "img",
        {
          src: Investment.src,
          alt: "image",
          className: "object-cover rounded-clg flex"
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-7xl pb-24 pt-10 sm:pb-32 flex lg:gap-x-8 lg:px-8 lg:py-40", children: [
      /* @__PURE__ */ jsx("div", { className: "flex px-6 lg:px-0 lg:pt-4", children: /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-2xl", children: [
        /* @__PURE__ */ jsx(
          "img",
          {
            className: "h-11",
            src: "https://tailwindui.com/plus/img/logos/mark.svg?color=indigo&shade=600",
            alt: "Your Company"
          }
        ),
        /* @__PURE__ */ jsxs("div", { className: "max-w-lg flex flex-col justify-between", children: [
          /* @__PURE__ */ jsx("h1", { className: "mt-10 text-pretty text-3xl tracking-tight text-gray-900 sm:text-5xl", children: "Monitor your Portfolio in your Dashboard" }),
          /* @__PURE__ */ jsx("p", { className: "mt-6 text-lg/8 text-black-50", children: "Portfolio Management" }),
          /* @__PURE__ */ jsx("p", { className: "mt-6 text-lg/8 text-gray-600", children: "Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo." })
        ] })
      ] }) }),
      /* @__PURE__ */ jsx(
        "img",
        {
          src: Dashboard.src,
          alt: "image",
          className: "object-cover rounded-clg flex"
        }
      )
    ] })
  ] }) });
};

const TodsonHouse = {"src":"/_astro/todson-house.e6cc1ee3.jpg","width":600,"height":400,"format":"jpg"};

const Bakery = {"src":"/_astro/the-bakery.5954fdfc.jpg","width":600,"height":398,"format":"jpg"};

const VillageStreet = {"src":"/_astro/village-street.e9845688.jpg","width":600,"height":398,"format":"jpg"};

const WickerIsland = {"src":"/_astro/wicker-island.6ec18f03.jpg","width":600,"height":400,"format":"jpg"};

const tabs = [
  {
    id: 1,
    name: "Todson House",
    title: "Extension of Luxury Student Living",
    description: "Helped to fund the construction of an additional 41 student apartments for a luxury student living brand. The project raised over £1m and paid an average return of 24% over 26 months.",
    imgurl: TodsonHouse,
    pl: "12 months",
    ar: "£400,000",
    noi: "234",
    mr: "£450,000"
  },
  {
    id: 2,
    name: "The Bakery",
    title: "Twelve city centre apartments in the heart of Sheffield",
    description: "We brought four derelict floors of a historic building back into use with the development of twelve new apartments. Even thought the project was delayed due to covid our investors made our forecasted 15% return on the project.",
    imgurl: Bakery,
    pl: "12 months",
    ar: "£400,000",
    noi: "234",
    mr: "£450,000"
  },
  {
    id: 3,
    name: "Village Street",
    title: "Leadership for easy community access",
    description: "Helped to fund the construction of an additional 41 student apartments for a luxury student living brand. The project raised over £1m and paid an average return of 24% over 26 months.",
    imgurl: VillageStreet,
    pl: "12 months",
    ar: "£400,000",
    noi: "234",
    mr: "£450,000"
  },
  {
    id: 4,
    name: "Wicker Island",
    title: "Design System for easy community access",
    description: "Helped to fund the construction of an additional 41 student apartments for a luxury student living brand. The project raised over £1m and paid an average return of 24% over 26 months.",
    imgurl: WickerIsland,
    pl: "12 months",
    ar: "£400,000",
    noi: "234",
    mr: "£450,000"
  }
];
function Tabs() {
  const [selectedTab, setSelectedTab] = useState(tabs[0]);
  return /* @__PURE__ */ jsxs("div", { className: "w-full rounded-csm py-24 px-6 smd:px-12 lxl:px-36 bg-white", children: [
    /* @__PURE__ */ jsxs("nav", { className: "flex justify-between", children: [
      /* @__PURE__ */ jsx("div", { className: "rounded-full border border-gray-100 px-6 py-2 text-base hidden lg:block ", children: "Project Specifics" }),
      /* @__PURE__ */ jsx("ul", { className: "flex gap-x-3 md:gap-x-8", children: tabs.map((item) => /* @__PURE__ */ jsxs("li", { className: item === selectedTab ? "text-black-50 cursor-pointer text-center" : "text-gray-100 cursor-pointer text-center", onClick: () => setSelectedTab(item), children: [
        `${item.name}`,
        item === selectedTab ? /* @__PURE__ */ jsx(motion.div, { className: "mt-2 h-[2px] bg-black-50 rounded-full", layoutId: "underline" }) : /* @__PURE__ */ jsx(motion.div, { className: "mt-2 h-[2px] bg-gray-100 rounded-full", layoutId: "underline" })
      ] }, `i_` + item.id)) })
    ] }),
    /* @__PURE__ */ jsx("main", { children: /* @__PURE__ */ jsx(AnimatePresence, { mode: "wait", children: /* @__PURE__ */ jsxs(
      motion.div,
      {
        initial: { y: 10, opacity: 0 },
        animate: { y: 0, opacity: 1 },
        exit: { y: -10, opacity: 0 },
        transition: { duration: 0.2 },
        children: [
          /* @__PURE__ */ jsxs("div", { className: "flex flex-col lg:flex-row w-full py-12 smd:py-24 gap-x-6 lxl:gap-x-12 justify-center items-center", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-12 justify-center max-w-xl lg:text-left text-center", children: [
              /* @__PURE__ */ jsx("div", { className: "text-4xl md:text-5xl lxl:text-6xl", children: selectedTab ? selectedTab.title : "empty" }),
              /* @__PURE__ */ jsx("div", { className: "text-xl lxl:text-2xl", children: selectedTab ? selectedTab.description : "😋" })
            ] }),
            /* @__PURE__ */ jsx("div", {}),
            /* @__PURE__ */ jsx(
              "img",
              {
                src: selectedTab ? selectedTab.imgurl.src : "",
                alt: "image",
                className: "object-cover rounded-clg hidden lg:block"
              }
            )
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "flex flex-col smd:flex-row gap-x-6 w-full", children: [
            /* @__PURE__ */ jsx(
              "img",
              {
                src: selectedTab ? selectedTab.imgurl.src : "",
                alt: "image",
                className: "object-cover rounded-clg flex lg:hidden max-h-[550px] smd:w-[50%] mlg:w-full"
              }
            ),
            /* @__PURE__ */ jsxs("div", { className: "w-full flex flex-col lg:flex-row justify-between gap-x-4 gap-y-3 text-lg mlg:text-2xl smd:pt-0 pt-12", children: [
              /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-1 smd:gap-y-2", children: [
                /* @__PURE__ */ jsx("p", { className: "text-gray-100", children: "Project Length" }),
                /* @__PURE__ */ jsx("p", { className: "text-black-50", children: selectedTab ? selectedTab.pl : "0 months" })
              ] }),
              /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-1 smd:gap-y-2", children: [
                /* @__PURE__ */ jsx("p", { className: "text-gray-100", children: "Amount Raised" }),
                /* @__PURE__ */ jsx("p", { className: "text-black-50", children: selectedTab ? selectedTab.ar : "0" })
              ] }),
              /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-1 smd:gap-y-2", children: [
                /* @__PURE__ */ jsx("p", { className: "text-gray-100", children: "Number of Investers" }),
                /* @__PURE__ */ jsx("p", { className: "text-black-50", children: selectedTab ? selectedTab.noi : "0" })
              ] }),
              /* @__PURE__ */ jsxs("div", { className: "flex flex-col gap-y-1 smd:gap-y-2", children: [
                /* @__PURE__ */ jsx("p", { className: "text-gray-100", children: "Money Returned" }),
                /* @__PURE__ */ jsx("p", { className: "text-black-50", children: selectedTab ? selectedTab.mr : "0" })
              ] })
            ] })
          ] })
        ]
      },
      selectedTab ? selectedTab.title : "empty"
    ) }) })
  ] });
}

const $$DemoPage2 = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "", "classes": "bg-framer", "description": "", "purl": "", "data-astro-cid-tv6iajjp": true }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "HeroSection", Example, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HeroSection.jsx", "client:component-export": "default", "data-astro-cid-tv6iajjp": true })} ${renderComponent($$result2, "InfiniteScroll", Home$2, { "bgClass": "bg-framer", "title": "As seen in", "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/InfiniteScrollSlice", "client:component-export": "default", "data-astro-cid-tv6iajjp": true })} ${renderComponent($$result2, "SlideContainer", Home$1, { "data-astro-cid-tv6iajjp": true })} ${renderComponent($$result2, "HorizontalScroll", HorizontalScroll, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/HorizontalScroll.jsx", "client:component-export": "default", "data-astro-cid-tv6iajjp": true })} ${renderComponent($$result2, "Tabs", Tabs, { "client:load": true, "client:component-hydration": "load", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Tabs.jsx", "client:component-export": "default", "data-astro-cid-tv6iajjp": true })} ${renderComponent($$result2, "Instructions", Instructions, { "client:visible": true, "client:component-hydration": "visible", "client:component-path": "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/components/Instructions.jsx", "client:component-export": "default", "data-astro-cid-tv6iajjp": true })} ${renderComponent($$result2, "SlideSection", Home, { "data-astro-cid-tv6iajjp": true })} ` })} `;
}, "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/demoPage2.astro", void 0);

const $$file = "/Volumes/NVMe_Drive/Documents/Website/astro/uown-revamp/src/pages/demoPage2.astro";
const $$url = "/demoPage2";

const demoPage2 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$DemoPage2,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

export { Bk as B, Cardiff as C, GrandDesigns as G, Home$2 as H, IH as I, NGI as N, S, Tele as T, Times as a, Table as b, BN as c, demoPage2 as d };

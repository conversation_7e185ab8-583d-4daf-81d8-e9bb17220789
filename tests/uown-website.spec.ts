import { test } from '@playwright/test';

// Test suite for UOWN website functionality
// Generated based on manual exploration of the site

test('UOWN Homepage - Basic functionality test', async ({ page }) => {
  // Navigate to the homepage
  await page.goto('/');
  await page.waitForLoadState('networkidle');

  // Log basic page information
  const title = await page.title();
  console.log(`Page title: ${title}`);

  const url = page.url();
  console.log(`Page URL: ${url}`);

  // Test navigation to "How it works?" page
  await page.getByRole('link', { name: 'How it works?' }).click();
  await page.waitForLoadState('networkidle');

  const investPageTitle = await page.title();
  console.log(`Invest page title: ${investPageTitle}`);

  // Navigate back to homepage
  await page.getByRole('link', { name: 'UOWN' }).click();
  await page.waitForLoadState('networkidle');

  console.log('Navigation test completed successfully');
});

test('UOWN Homepage - Responsive design test', async ({ page }) => {
  await page.goto('/');
  await page.waitForLoadState('networkidle');

  // Test desktop viewport
  await page.setViewportSize({ width: 1440, height: 900 });
  console.log('Testing desktop viewport (1440x900)');

  // Test mobile viewport
  await page.setViewportSize({ width: 375, height: 667 });
  console.log('Testing mobile viewport (375x667)');

  // Check if mobile menu button appears
  const mobileMenuButton = page.getByRole('button', { name: 'Open main menu' });
  const isMobileMenuVisible = await mobileMenuButton.isVisible();
  console.log(`Mobile menu button visible: ${isMobileMenuVisible}`);

  // Test tablet viewport
  await page.setViewportSize({ width: 768, height: 1024 });
  console.log('Testing tablet viewport (768x1024)');

  console.log('Responsive design test completed');
});

test('UOWN Homepage - Content verification test', async ({ page }) => {
  await page.goto('/');
  await page.waitForLoadState('networkidle');

  // Check for key statistics
  const statistics = ['£70m+', '17.7%', '11,000+'];
  for (const stat of statistics) {
    const element = page.getByRole('heading', { name: stat });
    const isVisible = await element.isVisible();
    console.log(`Statistic "${stat}" visible: ${isVisible}`);
  }

  // Check for key content sections
  const contentSections = ['Strength in numbers', 'As safe as houses', 'Don\'t just take our word for it'];
  for (const section of contentSections) {
    const element = page.getByText(section);
    const isVisible = await element.isVisible();
    console.log(`Content section "${section}" visible: ${isVisible}`);
  }

  // Check for call-to-action buttons
  const buttons = ['View Projects', 'Get Started'];
  for (const buttonText of buttons) {
    const button = page.getByRole('button', { name: buttonText }).first();
    const isVisible = await button.isVisible();
    console.log(`Button "${buttonText}" visible: ${isVisible}`);
  }

  console.log('Content verification test completed');
});

test('UOWN Homepage - Footer and links test', async ({ page }) => {
  await page.goto('/');
  await page.waitForLoadState('networkidle');

  // Scroll to footer
  await page.locator('footer').scrollIntoViewIfNeeded();

  // Check for legal links
  const legalLinks = ['Terms & Conditions', 'Privacy Policy', 'Cookie Policy', 'Risk Statement'];
  for (const linkText of legalLinks) {
    const link = page.getByRole('link', { name: linkText });
    const isVisible = await link.isVisible();
    console.log(`Legal link "${linkText}" visible: ${isVisible}`);
  }

  // Check for social media links
  const socialLinks = ['Facebook', 'Instagram', 'Linkedin'];
  for (const socialLink of socialLinks) {
    const link = page.getByRole('link', { name: socialLink });
    const isVisible = await link.isVisible();
    console.log(`Social link "${socialLink}" visible: ${isVisible}`);
  }

  // Check for risk disclaimer
  const riskText = 'House prices can fall as well as rise';
  const riskElement = page.getByText(riskText);
  const isRiskVisible = await riskElement.isVisible();
  console.log(`Risk disclaimer visible: ${isRiskVisible}`);

  console.log('Footer and links test completed');
});



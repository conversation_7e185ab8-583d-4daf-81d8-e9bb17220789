import { test, expect } from '@playwright/test';

// Test suite for UOWN website local development version
// Based on exploration of the live site functionality

test('UOWN Homepage - Basic page load and structure', async ({ page }) => {
  // Navigate to the homepage (baseURL is set to localhost:4321 in config)
  await page.goto('/');

  // Wait for page to load completely
  await page.waitForLoadState('networkidle');

  // Verify page loads successfully
  await expect(page).toHaveURL('/');

  // Verify essential page structure elements are present
  await expect(page.locator('html')).toBeVisible();
  await expect(page.locator('body')).toBeVisible();

  // Check for main content areas
  const hasMain = await page.locator('main').count();
  const hasContent = await page.locator('[role="main"], .main-content, #main').count();

  // Should have either a main element or main content area
  expect(hasMain + hasContent).toBeGreaterThan(0);
});

test('UOWN Homepage - Navigation elements', async ({ page }) => {
  await page.goto('/');
  await page.waitForLoadState('networkidle');

  // Check for navigation elements
  const navElements = await page.locator('nav, [role="navigation"], .navigation, .nav').count();
  expect(navElements).toBeGreaterThan(0);

  // Check for links in navigation
  const navLinks = page.locator('nav a, [role="navigation"] a, .navigation a, .nav a');
  const linkCount = await navLinks.count();

  if (linkCount > 0) {
    // Test that at least one navigation link is visible and functional
    const firstLink = navLinks.first();
    await expect(firstLink).toBeVisible();

    // Check if link has href attribute
    const href = await firstLink.getAttribute('href');
    expect(href).toBeTruthy();
  }
});

test('UOWN Homepage - Content and headings', async ({ page }) => {
  await page.goto('/');
  await page.waitForLoadState('networkidle');

  // Verify page has a title
  const title = await page.title();
  expect(title.length).toBeGreaterThan(0);

  // Check for heading structure
  const headings = page.locator('h1, h2, h3, h4, h5, h6');
  const headingCount = await headings.count();

  // Should have at least one heading
  expect(headingCount).toBeGreaterThan(0);

  // Check for main heading (h1)
  const h1Count = await page.locator('h1').count();
  expect(h1Count).toBeGreaterThanOrEqual(1);
});

test('UOWN Homepage - Interactive elements', async ({ page }) => {
  await page.goto('/');
  await page.waitForLoadState('networkidle');

  // Check for buttons
  const buttons = page.locator('button');
  const buttonCount = await buttons.count();

  if (buttonCount > 0) {
    // Test that buttons are visible and enabled
    const firstButton = buttons.first();
    await expect(firstButton).toBeVisible();
    await expect(firstButton).toBeEnabled();
  }

  // Check for form elements if any
  const inputs = page.locator('input');
  const inputCount = await inputs.count();

  if (inputCount > 0) {
    const firstInput = inputs.first();
    await expect(firstInput).toBeVisible();
  }

  // Check for clickable links
  const links = page.locator('a[href]');
  const linkCount = await links.count();
  expect(linkCount).toBeGreaterThan(0);
});

test('UOWN Homepage - Responsive design', async ({ page }) => {
  await page.goto('/');
  await page.waitForLoadState('networkidle');

  // Test mobile viewport (375x667 - iPhone SE)
  await page.setViewportSize({ width: 375, height: 667 });
  await expect(page.locator('body')).toBeVisible();

  // Test tablet viewport (768x1024 - iPad)
  await page.setViewportSize({ width: 768, height: 1024 });
  await expect(page.locator('body')).toBeVisible();

  // Test desktop viewport (1440x900)
  await page.setViewportSize({ width: 1440, height: 900 });
  await expect(page.locator('body')).toBeVisible();

  // Verify content is still accessible at different viewport sizes
  const headings = page.locator('h1, h2, h3, h4, h5, h6');
  const headingCount = await headings.count();
  expect(headingCount).toBeGreaterThan(0);
});

test('UOWN Homepage - Page performance', async ({ page }) => {
  const startTime = Date.now();

  await page.goto('/');
  await page.waitForLoadState('networkidle');

  const loadTime = Date.now() - startTime;

  // Page should load within 10 seconds (generous for local dev)
  expect(loadTime).toBeLessThan(10000);

  // Verify essential elements loaded
  await expect(page.locator('html')).toBeVisible();
  await expect(page.locator('body')).toBeVisible();
});

test('UOWN Homepage - Basic accessibility', async ({ page }) => {
  await page.goto('/');
  await page.waitForLoadState('networkidle');

  // Check page has a title
  const title = await page.title();
  expect(title.length).toBeGreaterThan(0);
  expect(title).not.toBe('');

  // Check for lang attribute on html element
  const htmlLang = await page.locator('html').getAttribute('lang');
  expect(htmlLang).toBeTruthy();

  // Check for main landmark or equivalent
  const mainElements = await page.locator('main, [role="main"]').count();
  expect(mainElements).toBeGreaterThanOrEqual(1);

  // Check heading hierarchy starts with h1
  const h1Elements = await page.locator('h1').count();
  expect(h1Elements).toBeGreaterThanOrEqual(1);
});

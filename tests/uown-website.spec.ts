import { test, expect } from '@playwright/test';

test.describe('UOWN Website - Property Investment Platform', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the UOWN homepage before each test
    await page.goto('https://www.uown.co');
  });

  test('should load homepage with correct title and key elements', async ({ page }) => {
    // Verify page title
    await expect(page).toHaveTitle('Property Crowdfunding & Investment Platform | UOWN');
    
    // Verify main navigation elements are present
    await expect(page.getByRole('link', { name: 'UOWN' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'How it works?' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Invest' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Login' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Sign Up' })).toBeVisible();
    
    // Verify key statistics are displayed
    await expect(page.getByRole('heading', { name: '£70m+' })).toBeVisible();
    await expect(page.getByRole('heading', { name: '17.7%' })).toBeVisible();
    await expect(page.getByRole('heading', { name: '11,000+' })).toBeVisible();
    
    // Verify main call-to-action buttons
    await expect(page.getByRole('button', { name: 'View Projects' }).first()).toBeVisible();
    await expect(page.getByRole('button', { name: 'Get Started' }).first()).toBeVisible();
  });

  test('should navigate to properties page when clicking View Projects', async ({ page }) => {
    // Click on the first "View Projects" button
    await page.getByRole('button', { name: 'View Projects' }).first().click();
    
    // Verify navigation to properties page
    await expect(page).toHaveURL('https://app.uown.co/properties');
    await expect(page).toHaveTitle('Our Investment Properties | Invest Online | UOWN');
    
    // Verify properties page elements
    await expect(page.getByText('Lets get this show on the road')).toBeVisible();
    await expect(page.getByText('To invest in a project you must have an account.')).toBeVisible();
    
    // Verify project filter buttons are present
    await expect(page.getByRole('button', { name: 'Live Projects' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Funded Projects' })).toBeVisible();
  });

  test('should display live projects by default on properties page', async ({ page }) => {
    // Navigate to properties page
    await page.getByRole('button', { name: 'View Projects' }).first().click();
    
    // Verify we're on the live projects view (default)
    await expect(page).toHaveURL('https://app.uown.co/properties');
    
    // Verify at least one live project is displayed
    await expect(page.getByText('Live')).toBeVisible();
    
    // Verify project details are shown (using Roomzzz Glasgow as example)
    await expect(page.getByText('Roomzzz Glasgow')).toBeVisible();
    await expect(page.getByText('8 Cochrane Street, Glasgow')).toBeVisible();
    await expect(page.getByText('£750,000')).toBeVisible();
    await expect(page.getByText('25.00%')).toBeVisible();
    await expect(page.getByText('12/2026')).toBeVisible();
  });

  test('should filter to funded projects when clicking Funded Projects button', async ({ page }) => {
    // Navigate to properties page
    await page.getByRole('button', { name: 'View Projects' }).first().click();
    
    // Click on Funded Projects filter
    await page.getByRole('button', { name: 'Funded Projects' }).click();
    
    // Verify URL changes to include completed filter
    await expect(page).toHaveURL('https://app.uown.co/properties?type=completed');
    
    // Verify completed projects are displayed
    await expect(page.getByText('Completed').first()).toBeVisible();
    
    // Verify some specific completed projects are shown
    await expect(page.getByText('Village Street')).toBeVisible();
    await expect(page.getByText('The Bakery')).toBeVisible();
    await expect(page.getByText('Wicker Island BTR')).toBeVisible();
    
    // Verify project details include completion status
    await expect(page.getByText('21.20%')).toBeVisible(); // Village Street return
    await expect(page.getByText('34.00%')).toBeVisible(); // Wicker Island BTR return
  });

  test('should switch back to live projects from funded projects', async ({ page }) => {
    // Navigate to properties page and go to funded projects
    await page.getByRole('button', { name: 'View Projects' }).first().click();
    await page.getByRole('button', { name: 'Funded Projects' }).click();
    
    // Verify we're on funded projects
    await expect(page).toHaveURL('https://app.uown.co/properties?type=completed');
    
    // Switch back to live projects
    await page.getByRole('button', { name: 'Live Projects' }).click();
    
    // Verify we're back on live projects
    await expect(page).toHaveURL('https://app.uown.co/properties?type=live');
    await expect(page.getByText('Live')).toBeVisible();
    await expect(page.getByText('Roomzzz Glasgow')).toBeVisible();
  });

  test('should display trust indicators and social proof', async ({ page }) => {
    // Verify "As seen in" section with media logos
    await expect(page.getByRole('heading', { name: 'As seen in' })).toBeVisible();
    await expect(page.getByRole('img', { name: 'Times' })).toBeVisible();
    await expect(page.getByRole('img', { name: 'I News' })).toBeVisible();
    
    // Verify Trustpilot rating
    await expect(page.getByText('4.9/5')).toBeVisible();
    await expect(page.getByRole('img', { name: 'Trustpilot' })).toBeVisible();
    
    // Verify customer testimonials are present
    await expect(page.getByText('Fraser')).toBeVisible();
    await expect(page.getByText('Rebecca')).toBeVisible();
    await expect(page.getByText('Zak')).toBeVisible();
  });

  test('should have proper footer with legal links', async ({ page }) => {
    // Scroll to footer
    await page.locator('footer').scrollIntoViewIfNeeded();
    
    // Verify company information
    await expect(page.getByText('UOWN is a platform offering anyone the ability to invest')).toBeVisible();
    
    // Verify legal links are present
    await expect(page.getByRole('link', { name: 'Terms & Conditions' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Privacy Policy' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Cookie Policy' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Risk Statement' })).toBeVisible();
    
    // Verify help and resources links
    await expect(page.getByRole('link', { name: 'Contact Us' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Help Centre' })).toBeVisible();
    
    // Verify risk disclaimer
    await expect(page.getByText('House prices can fall as well as rise')).toBeVisible();
    await expect(page.getByText('Investments are not protected under the Financial Services Compensation Scheme')).toBeVisible();
  });

  test('should have responsive navigation and mobile-friendly elements', async ({ page }) => {
    // Test on mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Verify page still loads correctly on mobile
    await expect(page.getByRole('link', { name: 'UOWN' })).toBeVisible();
    await expect(page.getByRole('heading', { name: '£70m+' })).toBeVisible();
    
    // Verify buttons are still clickable on mobile
    await expect(page.getByRole('button', { name: 'View Projects' }).first()).toBeVisible();
    await expect(page.getByRole('button', { name: 'Get Started' }).first()).toBeVisible();
  });
});

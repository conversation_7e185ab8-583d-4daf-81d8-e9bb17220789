import { test } from '@playwright/test';

test('Button Components - All States', async ({ page }) => {
  // Create a test page with all button variants
  await page.setContent(`
      <html>
        <head>
          <link rel="stylesheet" href="/src/styles/global.css">
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="p-8 space-y-4">
          <h1>Button Component States</h1>
          
          <!-- Button variants -->
          <div class="space-x-4">
            <button class="btn-black">Black Button</button>
            <button class="btn-mint">Mint Button</button>
            <button class="btn-white">White Button</button>
          </div>
          
          <!-- Button sizes -->
          <div class="space-x-4">
            <button class="btn-black text-sm px-3 py-1">Small</button>
            <button class="btn-black">Medium</button>
            <button class="btn-black text-lg px-6 py-3">Large</button>
          </div>
          
          <!-- But<PERSON> states -->
          <div class="space-x-4">
            <button class="btn-black">Normal</button>
            <button class="btn-black hover:opacity-80">Hover</button>
            <button class="btn-black opacity-50" disabled>Disabled</button>
          </div>
        </body>
      </html>
    `);

  if (process.env.PERCY_TOKEN) {
    const percySnapshot = (await import('@percy/playwright')).default;
    await percySnapshot(page, 'Button Components - All States');
  } else {
    console.log('Skipping Percy snapshot for Button Components - PERCY_TOKEN not set');
  }
});

test('Navigation Component States', async ({ page }) => {
  await page.goto('/');

  if (process.env.PERCY_TOKEN) {
    const percySnapshot = (await import('@percy/playwright')).default;

    // Desktop navigation
    await percySnapshot(page, 'Navigation - Desktop');

    // Mobile navigation (if applicable)
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForLoadState('networkidle');

    await percySnapshot(page, 'Navigation - Mobile');
  } else {
    console.log('Skipping Percy snapshot for Navigation Components - PERCY_TOKEN not set');
  }
});

import { test } from '@playwright/test';

const pages = [
    { name: 'Homepage', url: '/' },
    { name: 'Contact', url: '/contact' },
    { name: 'Invest', url: '/invest' },
    { name: 'Help Centre', url: '/help-centre' },
    { name: 'The Hub', url: '/the-hub' },
    { name: 'Track Record', url: '/track-record' },
    { name: 'Roomzzz Glasgow', url: '/roomzzz-glasgow' },
    { name: 'Wicker Island 2', url: '/wicker-island-2' },
    { name: 'Fantasy Football', url: '/fantasy-football' },
    { name: 'Thank You', url: '/thank-you' },
    { name: 'Guide Sent', url: '/guide-sent' },
    { name: 'Demo Page 1', url: '/demoPage' },
    { name: 'Demo Page 2', url: '/demoPage2' },
    { name: 'Demo Page 3', url: '/demoPage3' },
    { name: 'Demo Page 4', url: '/demoPage4' },
    { name: '<PERSON>ake Demo Page', url: '/stakeDemoPage' },
    { name: '404 Page', url: '/non-existent-page' },
];

// CMS Content Pages (2-3 representative samples)
const cmsPages = [
    { name: 'Help Article Sample 1', url: '/help-centre/getting-started' },
    { name: 'Help Article Sample 2', url: '/help-centre/account-setup' },
    { name: 'Hub Article Sample 1', url: '/the-hub/investment-basics' },
    { name: 'Hub Article Sample 2', url: '/the-hub/market-insights' },
];

// Main pages
for (const page of pages) {
    test(`${page.name} - Visual Test`, async ({ page: playwrightPage }) => {
        await playwrightPage.goto(page.url);

        // Wait for page to be fully loaded
        await playwrightPage.waitForLoadState('networkidle');

        // Handle any loading states
        await playwrightPage.waitForTimeout(1000);

        // Take Percy snapshot (only if PERCY_TOKEN is available)
        if (process.env.PERCY_TOKEN) {
            const percySnapshot = (await import('@percy/playwright')).default;
            await percySnapshot(playwrightPage, `Page: ${page.name}`, {
                percyCSS: `
          /* Stabilize dynamic elements */
          .loading, .spinner, [data-loading="true"] {
            visibility: hidden !important;
          }
          /* Hide timestamps and dynamic content */
          [data-testid*="time"], .timestamp {
            visibility: hidden !important;
          }
        `
            });
        } else {
            console.log(`Skipping Percy snapshot for ${page.name} - PERCY_TOKEN not set`);
        }
    });
}

// CMS pages (limited for Percy free account)
for (const page of cmsPages) {
    test(`${page.name} - CMS Visual Test`, async ({ page: playwrightPage }) => {
        await playwrightPage.goto(page.url);
        await playwrightPage.waitForLoadState('networkidle');
        await playwrightPage.waitForTimeout(1000);

        if (process.env.PERCY_TOKEN) {
            const percySnapshot = (await import('@percy/playwright')).default;
            await percySnapshot(playwrightPage, `CMS - ${page.name}`);
        } else {
            console.log(`Skipping Percy snapshot for ${page.name} - PERCY_TOKEN not set`);
        }
    });
}

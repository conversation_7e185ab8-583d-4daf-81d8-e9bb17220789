image: node:20

definitions:
  services:
    docker:
      memory: 2048

pipelines:
  default:
    - step:
        name: Visual Regression Tests
        size: 2x
        max-time: 45
        services:
          - docker
        caches:
          - node
        script:
          - apt-get update && apt-get install -y git-lfs
          - git lfs pull
          - npm ci
          - npx playwright install --with-deps
          - npm run build
          - timeout 600 npx percy exec -- npx playwright test tests/visual-regression.spec.ts
        artifacts:
          - playwright-report/**
        after-script:
          - if [ -d "playwright-report" ]; then echo "Test report available in artifacts"; fi

  branches:
    demo-pages:
      - step:
          name: Visual Regression Tests - Demo Pages Branch
          size: 2x
          services:
            - docker
          caches:
            - node
          script:
            - apt-get update && apt-get install -y git-lfs
            - git lfs pull
            - npm ci
            - npx playwright install --with-deps
            - npm run build
            - npx percy exec -- npx playwright test tests/visual-regression.spec.ts
          artifacts:
            - playwright-report/**

    improvements/*:
      - step:
          name: Visual Regression Tests - Improvements
          size: 2x
          services:
            - docker
          caches:
            - node
          script:
            - apt-get update && apt-get install -y git-lfs
            - git lfs pull
            - npm ci
            - npx playwright install --with-deps
            - npm run build
            - npx percy exec -- npx playwright test tests/visual-regression.spec.ts
          artifacts:
            - playwright-report/**

  pull-requests:
    '**':
      - step:
          name: Visual Regression Tests - PR
          size: 2x
          services:
            - docker
          caches:
            - node
          script:
            - apt-get update && apt-get install -y git-lfs
            - git lfs pull
            - npm ci
            - npx playwright install --with-deps
            - npm run build
            - npx percy exec -- npx playwright test tests/visual-regression.spec.ts
          artifacts:
            - playwright-report/**

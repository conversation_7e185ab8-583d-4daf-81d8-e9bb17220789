{"name": "uown-revamp", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/mdx": "^1.1.0", "@astrojs/netlify": "^3.0.1", "@astrojs/partytown": "^2.1.4", "@astrojs/react": "^3.0.4", "@astrojs/sitemap": "^3.1.1", "@astrojs/tailwind": "^5.0.0", "@headlessui/react": "^2.1.9", "@heroicons/react": "^2.0.18", "@portabletext/react": "^3.0.11", "@sanity/astro": "^1.3.0", "@sanity/image-url": "^1.0.2", "@studio-freight/lenis": "^1.0.42", "@szhsin/react-accordion": "^1.4.0", "@types/node": "^22.7.5", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "astro": "^3.4.0", "astro-portabletext": "^0.9.6", "clsx": "^2.1.1", "groq": "^3.18.1", "motion": "^12.7.4", "next": "^14.2.15", "posthog-node": "^4.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-intersection-observer": "^9.15.1", "react-phone-number-input": "^3.3.9", "react-player": "^2.13.0", "react-share-social": "^0.1.55", "react-stacked-center-carousel": "^1.0.12", "react-use-measure": "^2.1.1", "sass": "^1.67.0", "tailwindcss": "^3.4.13", "typescript": "^5.6.3", "use-debounce": "^10.0.3"}, "devDependencies": {"@percy/cli": "^1.31.0", "@percy/playwright": "^1.0.8", "@playwright/test": "^1.53.0", "playwright": "^1.53.0"}}
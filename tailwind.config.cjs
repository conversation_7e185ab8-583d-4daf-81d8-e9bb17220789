/** @type {import('tailwindcss').Config} */
module.exports = {
	content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
	theme: {
		boxShadow: {
			sm: '0 2px 6px rgb(15 23 42 / 0.08)',
			md: '0 8px 8px rgb(15 23 42 / 0.05), 0 3px 6px rgb(15 23 42 / 0.05)',
			lg: '0 8px 15px rgb(15 23 42 / 0.08), 0 3px 6px rgb(15 23 42 / 0.08)',
			xl: '2px 11px 16px rgb(15 23 42 / 0.17), 0 1px 6px rgb(15 23 42 / 0.17), 3px 23px 24px rgb(15 23 42 / 0.17)',
			stake: 'rgba(0, 0, 0, 0.25) 0px 3px 20px -4px',
			nav: '0px 7px 14px 0px #0000000A',
			articletile: '0px 8px 14px 4px #1D1D1B0D',
			tile: '4px 8px 14px 14px #06060603',
		},
		borderRadius: {
			'none': '0px',
			'cxs': '8px',
			'csm': '10px',
			'cmd': '20px',
			'clg': '30px',
			'cxl': '40px',
			'cxxl': '55px',
			'4xl': '2rem',
			'5xl': '3rem',
			'6xl': '5rem',
			'full': '99999px',
		},
		extend: {
			animation: {
				shake: "shake 0.8s cubic-bezier(0.455, 0.030, 0.515, 0.955) both",
				'fade-in': 'fade-in 0.5s linear forwards',
				marquee: 'marquee var(--marquee-duration) linear infinite',
				'spin-slow': 'spin 4s linear infinite',
				'spin-slower': 'spin 6s linear infinite',
				'spin-reverse': 'spin-reverse 1s linear infinite',
				'spin-reverse-slow': 'spin-reverse 4s linear infinite',
				'spin-reverse-slower': 'spin-reverse 6s linear infinite',
			},
			keyframes: {
				shake: {
					"0%, 100%": {
						transform: 'translateX(0)',
					},
					"10%, 30%, 50%, 70%": {
						transform: "translateX(-10px)",
					},
					"20%, 40%, 60%": {
						transform: "translateX(10px)",
					},
					"80%": {
						transform: "translateX(8px)",
					},
					"90%": {
						transform: "translateX(-8px)",
					},
				},
				'fade-in': {
					from: {
						opacity: '0',
					},
					to: {
						opacity: '1',
					},
				},
				marquee: {
					'100%': {
						transform: 'translateY(-50%)',
					},
				},
				'spin-reverse': {
					to: {
						transform: 'rotate(-360deg)',
					},
				},
			},
			colors: {
				'rose': {
					'50': 'hsl(51, 58%, 95%)',
					'100': 'hsl(53, 57%, 88%)',
					'200': 'hsl(51, 59%, 77%)',
					'300': 'hsl(49, 59%, 64%)',
					'400': 'hsl(46, 58%, 54%)',
					'500': 'hsl(44, 56%, 47%)',
					'600': 'hsl(39, 58%, 40%)',
					'700': 'hsl(34, 55%, 33%)',
					'800': 'hsl(30, 48%, 29%)',
					'900': 'hsl(27, 44%, 26%)',
					'950': 'hsl(25, 50%, 14%)',
				},

				'mint': {
					'50': '#EEFFF8',
					'100': '#A7F9D8',
					'200': '#92E8C3',
					'300': '#71E5BD',
					'500': '#13b688'
				},

				'black': {
					'50': '#1D1D1B',
					'100': '#101010',
					'200': '#010101',
					'300':'#000000',
				},

				'gray': {
					'10': '#fafafa',
					'20': '#f5f5f5',
					'50': '#DFDFDF',
					'100': '#D0D0D0',
					'200': '#767676',
					'pages': '#f7f7f7',
				},

				'salmon': {
					'50': '#FFEEEE',
					'100': '#FECEBF',
					'200': '#FE8075',
					'300': '#FC4C4C'
				},

				'yellow': {
					'10': '#fffdf5',
					'50': '#FFFBEE',
					'100': '#F7E79F',
					'200': '#FADC62',
					'300': '#FFD008',
				},

				'navyblue': {
					'50': '#EEF7FF',
					'100': '#AEC5E4',
					'200': '#7883C2',
					'300': '#5A6ECC',
				},
			},
		},
		fontFamily: {
			sans: ['Plus Jakarta Sans', 'sans-serif']
		},
		screens: {
			'sm': '280px',
			// => @media (min-width: 640px) { ... }

			'smd': '400px',
			'md': '640px',
			// => @media (min-width: 768px) { ... }
			'mlg': '850px',
			'lg': '1140px',
			// => @media (min-width: 1024px) { ... }
			'lxl': '1300px',
			'xl': '1920px',
			// => @media (min-width: 1280px) { ... }

			'xxl': '2560px',
			// => @media (min-width: 1536px) { ... }
		}
	},
	plugins: [],
}

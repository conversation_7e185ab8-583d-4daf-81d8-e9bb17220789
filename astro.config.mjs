import sanity from "@sanity/astro";
import { defineConfig } from 'astro/config';
import tailwind from "@astrojs/tailwind";
import netlify from "@astrojs/netlify";
import react from "@astrojs/react";
import sitemap from '@astrojs/sitemap';
import mdx from "@astrojs/mdx";

import partytown from "@astrojs/partytown";

// https://astro.build/config
export default defineConfig({
  experimental: {},
  site: 'https://www.uown.co',
  integrations: [sitemap(), tailwind(), react({
    experimentalReactChildren: true
  }), mdx(), sanity({
    projectId: "hyj5exjm",
    dataset: "production",
    // Set useCdn to false if you're building statically.
    useCdn: true
  }), partytown({
    config: {
      debug: false,
      logCalls: false,
      logGetters: false,
      logSetters: false,
      logImageRequests: false,
      logScriptExecution: false,
      logStackTraces: false,
      forward: [
        ["dataLayer.push"],
      ],
      resolveUrl: (url) => {
        const siteUrl = "https://your-proxy.url/";
        const proxyUrl = new URL(siteUrl);
        if (
          url.hostname === "googleads.g.doubleclick.net" ||
          url.hostname === "www.googleadservices.com" ||
          url.hostname === "googletagmanager.com" ||
          url.hostname === "www.googletagmanager.com" ||
          url.hostname === "region1.google-analytics.com" ||
          url.hostname === "google.com"
        ) {
          proxyUrl.searchParams.append("apiurl", url.href);
          return proxyUrl;
        }
        return url;
      },
    },
  }),
  ],
  output: "server",
  server: {
    headers: {
      "Access-Control-Allow-Origin": "*"
    }
  },
  adapter: netlify()
});
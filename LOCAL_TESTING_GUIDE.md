# Local Percy Testing Guide

## Prerequisites
1. You need a Percy account and token from percy.io
2. Add the PERCY_TOKEN to your Bitbucket repository secrets

## Local Testing Options

### Option 1: Export Token in Terminal
```bash
export PERCY_TOKEN=your_percy_token_here
npm run build
npx percy exec -- npx playwright test tests/visual-regression.spec.ts
```

### Option 2: Run with Token Inline
```bash
npm run build
PERCY_TOKEN=your_percy_token_here npx percy exec -- npx playwright test tests/visual-regression.spec.ts
```

### Option 3: Test Without Percy (Basic Playwright)
```bash
npm run build
npx playwright test tests/visual-regression.spec.ts
```
*Note: This will skip Percy snapshots but test page loading*

## What Happens
- **With PERCY_TOKEN**: Creates visual snapshots and uploads to Percy dashboard
- **Without PERCY_TOKEN**: Tests run but skip visual snapshots (logs message)

## Current Test Coverage
**Main Pages (17 pages):**
- Homepage, Contact, Invest, Help Centre, The Hub
- Track Record, Roomzzz Glasgow, Wicker Island 2
- Fantasy Football, Thank You, <PERSON>, 404 Page
- Demo Page 1, Demo Page 2, Demo Page 3, Demo Page 4
- Stake Demo Page

**CMS Sample Pages (4 pages):**
- Help Centre articles (2 samples)
- Hub articles (2 samples)

**Total: 21 pages under visual regression protection**

## Pipeline Testing
The Bitbucket Pipeline will automatically run when you push to:
- `demo-pages` branch
- `improvements/*` branches
- Pull requests

## Troubleshooting
- If tests fail locally due to Playwright issues, the CI pipeline should still work
- Check Percy dashboard at percy.io for visual diff results
- Pipeline artifacts will contain test reports if tests fail
- Pipeline now includes `git lfs pull` to properly download large image assets

## Current Status
- **AVIF files**: Temporarily committed as regular Git files (bypassing LFS tracking issue)
- **TODO**: Migrate AVIF files back to proper Git LFS tracking later today
- **Pipeline**: Should now work with all 21 pages including demo/stake pages
- **Build**: Confirmed working locally with all image assets

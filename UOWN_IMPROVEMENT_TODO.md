# UOWN Codebase Improvement TODO List

## 🚨 CRITICAL SECURITY FIXES (Priority 1)

### TODO-SEC-001: Remove Exposed API Keys
**Issue**: Sensitive API keys are exposed in `.env` file and committed to repository
**Plan**:
1. Remove `.env` file from repository immediately
2. Add `.env` to `.gitignore` if not already present
3. Create `.env.example` template file with placeholder values
4. Update deployment environment to use proper environment variables
5. Rotate all exposed API keys:
   - `WHATSAPP_AUTH_BEARER`
   - `SENTRY_AUTH_TOKEN`
   - `EMAIL_OCTOPUS_AUTH`
**Files affected**: `.env`, `.gitignore`, deployment config

### TODO-SEC-002: Fix CORS Security
**Issue**: Overly permissive CORS policy allowing all origins
**Plan**:
1. Replace `"Access-Control-Allow-Origin": "*"` with specific allowed domains
2. Add environment-based CORS configuration
3. Implement proper CORS middleware for API routes
**Files affected**: `astro.config.mjs`

### TODO-SEC-003: Sanitize API Inputs
**Issue**: No input validation or sanitization in API routes
**Plan**:
1. Install validation library (e.g., `zod` or `joi`)
2. Create validation schemas for all API endpoints
3. Add input sanitization for all user inputs
4. Implement rate limiting for API endpoints
**Files affected**: `src/pages/api/ArrangeCall.ts`, `src/pages/api/emailoctopus.ts`, `src/pages/api/sms.ts`, `src/pages/api/feedback.ts`

### TODO-SEC-004: Fix GROQ Query Injection
**Issue**: String concatenation in GROQ queries creates injection vulnerability
**Plan**:
1. Replace string concatenation with parameterized queries
2. Use Sanity's built-in query parameter system
3. Add input validation before query execution
**Files affected**: `src/lib/sanity-client.ts`

## 🏗️ CODE ORGANIZATION & STRUCTURE (Priority 2)

### TODO-ORG-001: Standardize File Extensions
**Issue**: Inconsistent use of .jsx, .tsx, and .astro files
**Plan**:
1. Convert all React components to TypeScript (.tsx)
2. Keep .astro files for Astro components only
3. Update all import statements accordingly
4. Add TypeScript interfaces for all component props
**Files affected**: All components in `src/components/`

### TODO-ORG-002: Break Down Large Components
**Issue**: Some components are too large and have multiple responsibilities
**Plan**:
1. **Nav.jsx**: Split into separate components:
   - `Navigation.tsx` (main nav)
   - `MobileMenu.tsx` (mobile navigation)
   - `NavigationItem.tsx` (individual nav items)
2. **Footer.astro**: Extract sections into smaller components
3. **Hero.jsx**: Separate testimonial and main hero sections
**Files affected**: `src/components/Nav.jsx`, `src/components/Footer.astro`, `src/components/Hero.jsx`

### TODO-ORG-003: Create Consistent Component Structure
**Issue**: Components lack consistent patterns and interfaces
**Plan**:
1. Create TypeScript interfaces for all component props
2. Implement consistent error handling patterns
3. Add JSDoc comments for all components
4. Create component documentation template
**Files affected**: All components

### TODO-ORG-004: Implement Proper Error Boundaries
**Issue**: No error boundaries for React components
**Plan**:
1. Create `ErrorBoundary.tsx` component
2. Implement error logging integration
3. Add fallback UI components
4. Wrap main application sections with error boundaries
**Files affected**: New files + layout updates

## 🔧 API IMPROVEMENTS (Priority 2)

### TODO-API-001: Refactor API Route Structure
**Issue**: Inconsistent API patterns and poor error handling
**Plan**:
1. Create shared API utilities:
   - `src/lib/api-utils.ts` (common functions)
   - `src/lib/validation.ts` (validation schemas)
   - `src/lib/errors.ts` (error handling)
2. Implement consistent response format
3. Add proper HTTP status codes
4. Add request logging
**Files affected**: All API routes + new utility files

### TODO-API-002: Replace Hardcoded XML Generation
**Issue**: XML strings are hardcoded in ArrangeCall.ts
**Plan**:
1. Create XML builder utility function
2. Use template system or XML library
3. Add proper escaping for user inputs
4. Create reusable lead generation service
**Files affected**: `src/pages/api/ArrangeCall.ts`

### TODO-API-003: Add API Rate Limiting
**Issue**: No protection against API abuse
**Plan**:
1. Implement rate limiting middleware
2. Add IP-based throttling
3. Create API key system for internal use
4. Add monitoring and alerting
**Files affected**: All API routes + new middleware

## 🎨 STYLING & UI CONSISTENCY (Priority 3)

### TODO-UI-001: Consolidate Styling Approaches
**Issue**: Mix of SCSS files and Tailwind utilities
**Plan**:
1. Audit all SCSS files for necessity
2. Convert reusable styles to Tailwind utilities
3. Keep only component-specific SCSS
4. Create design system documentation
**Files affected**: `src/styles/` directory

### TODO-UI-002: Implement Design System
**Issue**: Inconsistent spacing, colors, and typography
**Plan**:
1. Document all design tokens in Tailwind config
2. Create component library documentation
3. Standardize button variants and sizes
4. Create consistent spacing scale
**Files affected**: `tailwind.config.cjs`, component files

### TODO-UI-003: Optimize Button Components
**Issue**: Multiple button components with different patterns
**Plan**:
1. Merge `Button.jsx`, `Button.astro`, and `ButtonA.jsx`
2. Create single, flexible button component
3. Add proper TypeScript interfaces
4. Implement consistent styling variants
**Files affected**: Button components

### TODO-STYLE-001: Fix Color System Inconsistencies (CRITICAL)
**Issue**: SCSS color variables conflict with Tailwind colors, causing maintenance issues and potential visual bugs
**Plan**:
1. **Phase 1 - Audit & Document (1 day)**:
   - Create comprehensive color usage audit across all components
   - Document exact hex values currently in use
   - Create visual regression test baseline screenshots
   - Map SCSS variables to Tailwind equivalents

2. **Phase 2 - Tailwind Config Update (0.5 days)**:
   - Update `tailwind.config.cjs` with exact SCSS color values
   - Ensure 1:1 color mapping to prevent visual changes
   - Add missing color variants (50, 100, 200, etc.)

3. **Phase 3 - Component Migration (2-3 days)**:
   - Replace SCSS color classes with Tailwind utilities
   - Update components one by one with visual verification
   - Run visual regression tests after each component

4. **Phase 4 - Cleanup (0.5 days)**:
   - Remove unused SCSS color variables
   - Update imports and dependencies
   - Final visual verification

**Files affected**: `tailwind.config.cjs`, `src/styles/colors.scss`, `src/styles/globalStyles.scss`, all components using color classes
**Risk Level**: LOW (1:1 color mapping ensures no visual changes)
**Performance Impact**: POSITIVE (smaller CSS bundle, better tree-shaking)

### TODO-STYLE-002: Consolidate Button Components (MEDIUM PRIORITY)
**Issue**: Three different button implementations causing inconsistent UX and maintenance overhead
**Plan**:
1. **Phase 1 - Analysis & Mapping (1 day)**:
   - Audit all button usages across the codebase
   - Document current button variants and their exact styling
   - Create compatibility matrix between button components
   - Take screenshots of all button states for regression testing

2. **Phase 2 - Enhanced ButtonA Component (1 day)**:
   - Extend `ButtonA.jsx` to support all current button variants
   - Add exact styling matches for `btn-black`, `btn-mint`, `btn-white` classes
   - Implement size variants (large, med, small) with exact pixel dimensions
   - Add proper TypeScript interfaces with strict typing

3. **Phase 3 - Gradual Migration (2-3 days)**:
   - Replace button components page by page
   - Verify visual consistency after each replacement
   - Test all interactive states (hover, focus, active, disabled)
   - Maintain exact same DOM structure for CSS selectors

4. **Phase 4 - Cleanup (0.5 days)**:
   - Remove old button components and SCSS
   - Update all imports
   - Final regression testing

**Files affected**: `src/components/Button.jsx`, `src/components/Button.astro`, `src/components/ButtonA.jsx`, `src/styles/button.scss`, all components using buttons
**Risk Level**: MEDIUM (requires careful state matching)
**Performance Impact**: POSITIVE (reduced CSS, better component reuse)

### TODO-STYLE-003: Eliminate SCSS/Tailwind Hybrid Patterns (LOW PRIORITY)
**Issue**: Mixed styling approaches create maintenance complexity and larger bundle sizes
**Plan**:
1. **Phase 1 - SCSS Audit (1 day)**:
   - Inventory all SCSS files and their usage
   - Identify styles that can be converted to Tailwind utilities
   - Document styles that must remain as custom CSS
   - Calculate potential bundle size reduction

2. **Phase 2 - Gradient System Migration (1 day)**:
   - Add all SCSS gradients to Tailwind config `backgroundImage`
   - Create utility classes for gradient backgrounds
   - Replace `.bg-*-gradient` classes with Tailwind utilities
   - Verify exact visual matching with original gradients

3. **Phase 3 - Custom Class Conversion (2 days)**:
   - Convert `.anchor-text`, `.nav-opt-border`, etc. to Tailwind utilities
   - Use `@apply` directive for complex repeated patterns
   - Maintain exact visual appearance
   - Test responsive behavior

4. **Phase 4 - Font Loading Optimization (0.5 days)**:
   - Fix font format declarations (`format('truetype')` instead of `format('ttf')`)
   - Optimize font loading strategy
   - Add font-display: swap for better performance

5. **Phase 5 - File Cleanup (0.5 days)**:
   - Remove unused SCSS files
   - Update imports and build process
   - Verify bundle size reduction

**Files affected**: `src/styles/globalStyles.scss`, `src/styles/carousel.scss`, `src/styles/stackedCarousel.scss`, `tailwind.config.cjs`
**Risk Level**: LOW (careful visual matching ensures no changes)
**Performance Impact**: POSITIVE (smaller CSS bundle, better caching)

### TODO-STYLE-004: Optimize Long Tailwind Class Strings (ENHANCEMENT)
**Issue**: Components have extremely long className strings (100+ characters) affecting readability
**Plan**:
1. **Phase 1 - Pattern Analysis (0.5 days)**:
   - Identify most common class combinations
   - Find repeated patterns across components
   - Document class strings longer than 80 characters

2. **Phase 2 - Create Utility Components (1 day)**:
   - Create Tailwind component classes using `@apply`
   - Focus on form inputs, cards, and layout patterns
   - Maintain exact same styling output

3. **Phase 3 - Component Refactoring (1-2 days)**:
   - Replace long class strings with utility components
   - Use `clsx` for conditional classes
   - Improve component readability

**Files affected**: Components with long className strings
**Risk Level**: VERY LOW (no visual changes, only code organization)
**Performance Impact**: NEUTRAL (same CSS output, better maintainability)

### TODO-STYLE-005: Implement Visual Regression Testing (INFRASTRUCTURE)
**Issue**: Need automated way to verify styling changes don't affect visual appearance
**Plan**:
1. **Phase 1 - Setup Testing Infrastructure (1 day)**:
   - Install and configure visual regression testing tool (Playwright + Percy or Chromatic)
   - Create baseline screenshots for all pages
   - Set up CI/CD integration

2. **Phase 2 - Test Coverage (1 day)**:
   - Screenshot all button states and variants
   - Capture responsive breakpoints
   - Test dark/light mode if applicable

3. **Phase 3 - Integration (0.5 days)**:
   - Add visual tests to PR workflow
   - Create approval process for visual changes
   - Document testing procedures

**Files affected**: New test files, CI/CD configuration
**Risk Level**: NONE (testing infrastructure only)
**Performance Impact**: NONE (testing only)

## ⚡ PERFORMANCE OPTIMIZATIONS (Priority 3)

### TODO-PERF-001: Implement Image Optimization
**Issue**: No systematic image optimization strategy
**Plan**:
1. Audit all image usage
2. Implement responsive image components
3. Add lazy loading for below-fold images
4. Optimize image formats (WebP, AVIF)
**Files affected**: Image components and assets

### TODO-PERF-002: Add Loading States
**Issue**: No loading indicators for dynamic content
**Plan**:
1. Create reusable loading components
2. Add skeleton screens for content areas
3. Implement progressive loading for heavy sections
4. Add error states for failed loads
**Files affected**: Dynamic content components

### TODO-PERF-003: Implement Caching Strategy
**Issue**: No caching for API responses or static content
**Plan**:
1. Add browser caching headers
2. Implement service worker for offline support
3. Add Sanity response caching
4. Create cache invalidation strategy
**Files affected**: API routes, service worker

## 🧪 TESTING & QUALITY ASSURANCE (Priority 3)

### TODO-TEST-001: Add Testing Framework
**Issue**: No testing infrastructure
**Plan**:
1. Install and configure Vitest
2. Add React Testing Library
3. Create test utilities and helpers
4. Add component testing examples
**Files affected**: New test files + config

### TODO-TEST-002: Add Type Safety Improvements
**Issue**: Missing TypeScript interfaces and proper typing
**Plan**:
1. Add strict TypeScript configuration
2. Create interfaces for all data structures
3. Add proper typing for Sanity responses
4. Implement runtime type checking where needed
**Files affected**: TypeScript config + all components

### TODO-TEST-003: Add Code Quality Tools
**Issue**: No linting or formatting standards
**Plan**:
1. Configure ESLint with React and TypeScript rules
2. Add Prettier for code formatting
3. Set up pre-commit hooks
4. Add code quality CI/CD checks
**Files affected**: Config files + CI/CD

## 📚 DOCUMENTATION & MAINTENANCE (Priority 4)

### TODO-DOC-001: Create Proper README
**Issue**: README contains placeholder content
**Plan**:
1. Document project setup and installation
2. Add development workflow instructions
3. Document deployment process
4. Add troubleshooting guide
**Files affected**: `README.md`

### TODO-DOC-002: Add Component Documentation
**Issue**: No documentation for component usage
**Plan**:
1. Add Storybook for component documentation
2. Create usage examples for all components
3. Document props and interfaces
4. Add design system documentation
**Files affected**: New documentation files

### TODO-DOC-003: Environment Setup Documentation
**Issue**: No clear environment setup instructions
**Plan**:
1. Document all required environment variables
2. Create setup scripts for development
3. Add Docker configuration for consistent environments
4. Document external service integrations
**Files affected**: Documentation + config files

## 🔄 MIGRATION & REFACTORING (Priority 4)

### TODO-MIG-001: Upgrade Dependencies
**Issue**: Some dependencies may be outdated
**Plan**:
1. Audit all dependencies for security vulnerabilities
2. Update to latest stable versions
3. Test for breaking changes
4. Update code for new API changes
**Files affected**: `package.json` + affected code

### TODO-MIG-002: Implement Proper State Management
**Issue**: No centralized state management for complex interactions
**Plan**:
1. Evaluate need for state management (Zustand/Redux)
2. Implement for user authentication state
3. Add for form state management
4. Create consistent patterns
**Files affected**: Interactive components

---

## Implementation Priority Order:

1. **Week 1**: Complete all Priority 1 (Security) items
2. **Week 2-3**: Address Priority 2 (Code Organization & API)
3. **Week 4-5**: Tackle Priority 3 (UI & Performance)
4. **Week 6+**: Complete Priority 4 (Documentation & Migration)

Each TODO item should be implemented as a separate branch/PR for proper code review and testing.

---

## 🎬 MOTION & ANIMATION IMPROVEMENTS (Priority 4)

### TODO-MOTION-001: Create Reusable Motion Component Library
**Issue**: Duplicate fade-in animations used 15+ times across components with inconsistent implementation
**Plan**:
1. **Phase 1 - Extract Common Patterns (1 day)**:
   - Create FadeInUp component to replace most common pattern: `initial={{ opacity: 0, y: 50 }}`
   - Build StaggeredContainer/StaggeredItem components for sequential animations
   - Implement PageTransition component for consistent page transitions
   - Create ScrollParallax component for reusable parallax effects

2. **Phase 2 - Motion Utilities (0.5 days)**:
   - Extract motion variants into `utils/motionVariants.js`
   - Create custom hooks (`useParallax`, `useScrollAnimation`) for common patterns
   - Add TypeScript interfaces for motion props

**Files affected**: New motion component library, existing components using motion
**Reference**: See motion analysis for detailed implementation guidance

### TODO-MOTION-002: Add Motion to Static Pages (HIGH IMPACT)
**Issue**: Key pages completely lack motion animations, reducing engagement and conversion potential
**Plan**:
1. **Phase 1 - Contact Page (0.5 days)**:
   - Add hero section fade-in animations
   - Implement form field stagger animations
   - Add parallax effects for background shapes

2. **Phase 2 - Investment Pages (1-2 days)**:
   - Enhance `roomzzz-glasgow.astro` with hero, features, testimonials, stats animations
   - Add motion to `wicker-island-2.astro` (hero, form, images, stats)
   - Implement step-by-step animations on `invest.astro`

**Files affected**: `src/pages/contact.astro`, `src/pages/roomzzz-glasgow.astro`, `src/pages/wicker-island-2.astro`, `src/pages/invest.astro`
**Business Impact**: HIGH (improved conversion rates on key pages)

### TODO-MOTION-003: Enhance Content Pages with Motion
**Issue**: Content and guide pages lack engaging animations
**Plan**:
1. **Phase 1 - Help Centre Improvements (1 day)**:
   - Replace script-based animations in `help-centre.astro` with React motion components
   - Add search bar animation and staggered article cards
   - Implement category hover effects

2. **Phase 2 - Content Page Enhancements (1-2 days)**:
   - Add staggered animations to article cards across content pages
   - Implement motion on guide pages (`/pages/guides/`) for better engagement
   - Add hover effects and micro-interactions on category pages

**Files affected**: `src/pages/help-centre.astro`, content pages, guide pages
**User Experience Impact**: MEDIUM (improved engagement and polish)

### TODO-MOTION-004: Performance & Accessibility Optimization
**Issue**: Current motion implementation lacks accessibility considerations and performance optimization
**Plan**:
1. **Phase 1 - Accessibility (0.5 days)**:
   - Add `prefers-reduced-motion` support across all motion components
   - Implement proper motion disable functionality
   - Test with screen readers and accessibility tools

2. **Phase 2 - Performance Optimization (1 day)**:
   - Optimize animation thresholds (currently using 0.99 which may be too high)
   - Implement proper cleanup for infinite scroll animations
   - Audit motion performance on lower-end devices
   - Consider lazy loading motion components to reduce initial bundle size

**Files affected**: All motion components, motion utilities
**Performance Impact**: POSITIVE (better performance, accessibility compliance)

## Progress Tracking:

### Completed Items:
- [ ] None yet

### In Progress:
- [ ] None yet

### Next Up:
- [ ] TODO-SEC-001: Remove Exposed API Keys

---

*Last Updated: [Current Date]*
*Total Items: 24*
*Completed: 0*
*Remaining: 24*
# UOWN Visual Regression Testing & Systematic Improvements Implementation Guide

## 📋 Executive Summary

This document provides a comprehensive roadmap for implementing visual regression testing as the foundation for systematic improvements to the UOWN codebase. The strategy prioritizes visual safety through automated testing before making any styling or structural changes.

### 🎯 Strategic Approach
- **Foundation First**: Establish visual regression testing before any improvements
- **Mobile-First**: Prioritize mobile viewport testing (highest user impact)
- **Risk Mitigation**: Use visual testing to prevent regressions during improvements
- **Systematic Implementation**: Phase-based approach with clear success criteria

### 📅 Timeline Overview
- **Week 1**: Visual regression testing foundation
- **Week 2**: Color system consolidation (low risk, high impact)
- **Week 3**: Component unification & TypeScript migration
- **Week 4**: Security improvements (excluding .env per user request)

---

## 🔧 Technical Specifications

### **Core Technologies**
- **Testing Framework**: Playwright (Latest - supports Node.js 18, 20, 22)
- **Visual Testing**: Percy (Free account - 5,000 screenshots/month)
- **Browser Coverage**: Safari, Chrome, Firefox
- **Responsive Strategy**: Mobile-first (375px), Desktop (1440px), Tablet (768px)

### **Package Versions (Latest)**
```json
{
  "@playwright/test": "latest",
  "@percy/playwright": "latest",
  "@percy/cli": "latest"
}
```

---

## 🌳 Branch Strategy

### **Base Branch**: `demo-pages`
All improvement branches will be created from the current `demo-pages` branch which contains the latest development work.

### **Branch Structure**
```
demo-pages (base)
├── improvements/visual-regression-setup-demo
├── improvements/color-system-consolidation  
├── improvements/button-component-unification
├── improvements/typescript-migration
├── improvements/security-api-hardening
└── improvements/performance-optimization
```

### **Workflow**
1. Create each improvement branch from `demo-pages`
2. Implement changes with visual regression verification
3. Merge back to `demo-pages` after approval
4. Eventually merge `demo-pages` to `main`

---

## 🎯 Phase 1: Visual Regression Testing Setup

### **Objective**
Establish comprehensive visual regression testing infrastructure to safely implement all subsequent improvements.

### **Duration**: 5 days

### **Day 1-2: Installation & Configuration** ✅ COMPLETED

#### **Step 1: Install Dependencies** ✅ COMPLETED
```bash
# Install Playwright
npm install --save-dev @playwright/test

# Install Percy integration
npm install --save-dev @percy/playwright @percy/cli
```

#### **Step 2: Playwright Configuration** ✅ COMPLETED
Create `playwright.config.ts`:

```typescript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:4321',
    trace: 'on-first-retry',
  },

  projects: [
    {
      name: 'Mobile Safari',
      use: { 
        ...devices['iPhone 12'],
        viewport: { width: 375, height: 667 }
      },
    },
    {
      name: 'Desktop Chrome',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1440, height: 900 }
      },
    },
    {
      name: 'Desktop Firefox',
      use: { 
        ...devices['Desktop Firefox'],
        viewport: { width: 1440, height: 900 }
      },
    },
    {
      name: 'Desktop Safari',
      use: { 
        ...devices['Desktop Safari'],
        viewport: { width: 1440, height: 900 }
      },
    },
    {
      name: 'Tablet',
      use: { 
        ...devices['iPad Pro'],
        viewport: { width: 768, height: 1024 }
      },
    },
  ],

  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:4321',
    reuseExistingServer: !process.env.CI,
  },
});
```

#### **Step 3: Percy Configuration**
Create `.percy.yml`:

```yaml
version: 2
snapshot:
  widths: [375, 768, 1440]
  min-height: 1024
  percy-css: |
    /* Hide dynamic elements */
    [data-testid="timestamp"],
    .loading-spinner,
    .animation-element {
      visibility: hidden !important;
    }
discovery:
  allowed-hostnames:
    - localhost
  network-idle-timeout: 750
```

### **Day 3-4: Test Suite Development**

#### **Core Visual Tests**
Create `tests/visual-regression.spec.ts`:

```typescript
import { test } from '@playwright/test';
import { percySnapshot } from '@percy/playwright';

const pages = [
  { name: 'Homepage', url: '/' },
  { name: 'Contact', url: '/contact' },
  { name: 'Invest', url: '/invest' },
  { name: 'Help Centre', url: '/help-centre' },
  { name: 'The Hub', url: '/the-hub' },
  { name: 'Track Record', url: '/track-record' },
  { name: 'Roomzzz Glasgow', url: '/roomzzz-glasgow' },
  { name: 'Wicker Island 2', url: '/wicker-island-2' },
  { name: 'Fantasy Football', url: '/fantasy-football' },
  { name: 'Thank You', url: '/thank-you' },
  { name: 'Guide Sent', url: '/guide-sent' },
  { name: '404 Page', url: '/non-existent-page' },
];

// CMS Content Pages (2-3 representative samples)
const cmsPages = [
  { name: 'Help Article Sample 1', url: '/help-centre/getting-started' },
  { name: 'Help Article Sample 2', url: '/help-centre/account-setup' },
  { name: 'Hub Article Sample 1', url: '/the-hub/investment-basics' },
  { name: 'Hub Article Sample 2', url: '/the-hub/market-insights' },
];

test.describe('Visual Regression Tests', () => {
  // Main pages
  for (const page of pages) {
    test(`${page.name} - Visual Test`, async ({ page: playwrightPage }) => {
      await playwrightPage.goto(page.url);
      
      // Wait for page to be fully loaded
      await playwrightPage.waitForLoadState('networkidle');
      
      // Handle any loading states
      await playwrightPage.waitForTimeout(1000);
      
      // Take Percy snapshot
      await percySnapshot(playwrightPage, `${page.name}`, {
        fullPage: true,
        percyCSS: `
          /* Stabilize dynamic elements */
          .loading, .spinner, [data-loading="true"] {
            visibility: hidden !important;
          }
          /* Hide timestamps and dynamic content */
          [data-testid*="time"], .timestamp {
            visibility: hidden !important;
          }
        `
      });
    });
  }

  // CMS pages (limited for Percy free account)
  for (const page of cmsPages) {
    test(`${page.name} - CMS Visual Test`, async ({ page: playwrightPage }) => {
      await playwrightPage.goto(page.url);
      await playwrightPage.waitForLoadState('networkidle');
      await playwrightPage.waitForTimeout(1000);
      
      await percySnapshot(playwrightPage, `CMS - ${page.name}`, {
        fullPage: true
      });
    });
  }
});
```

#### **Component-Level Visual Tests**
Create `tests/components-visual.spec.ts`:

```typescript
import { test } from '@playwright/test';
import { percySnapshot } from '@percy/playwright';

test.describe('Component Visual Tests', () => {
  test('Button Components - All States', async ({ page }) => {
    // Create a test page with all button variants
    await page.setContent(`
      <html>
        <head>
          <link rel="stylesheet" href="/src/styles/global.css">
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="p-8 space-y-4">
          <h1>Button Component States</h1>
          
          <!-- Button variants -->
          <div class="space-x-4">
            <button class="btn-black">Black Button</button>
            <button class="btn-mint">Mint Button</button>
            <button class="btn-white">White Button</button>
          </div>
          
          <!-- Button sizes -->
          <div class="space-x-4">
            <button class="btn-black text-sm px-3 py-1">Small</button>
            <button class="btn-black">Medium</button>
            <button class="btn-black text-lg px-6 py-3">Large</button>
          </div>
          
          <!-- Button states -->
          <div class="space-x-4">
            <button class="btn-black">Normal</button>
            <button class="btn-black hover:opacity-80">Hover</button>
            <button class="btn-black opacity-50" disabled>Disabled</button>
          </div>
        </body>
      </html>
    `);
    
    await percySnapshot(page, 'Button Components - All States');
  });

  test('Navigation Component States', async ({ page }) => {
    await page.goto('/');
    
    // Desktop navigation
    await percySnapshot(page, 'Navigation - Desktop');
    
    // Mobile navigation (if applicable)
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    await percySnapshot(page, 'Navigation - Mobile');
  });
});
```

### **Day 5: CI/CD Integration & Testing**

#### **GitHub Actions Workflow**
Create `.github/workflows/visual-tests.yml`:

```yaml
name: Visual Regression Tests

on:
  push:
    branches: [ feature-stake-page, improvements/* ]
  pull_request:
    branches: [ feature-stake-page, main ]

jobs:
  visual-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install Playwright browsers
      run: npx playwright install --with-deps
    
    - name: Build project
      run: npm run build
    
    - name: Run visual tests
      env:
        PERCY_TOKEN: ${{ secrets.PERCY_TOKEN }}
      run: npx percy exec -- npx playwright test tests/visual-regression.spec.ts
    
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: playwright-report
        path: playwright-report/
```

#### **Percy Free Account Optimization**

**Screenshot Budget Management:**
- **Total Monthly Limit**: 5,000 screenshots
- **Per Test Run**: ~150 screenshots (3 browsers × 3 viewports × ~17 pages)
- **Available Runs**: ~33 per month
- **Strategy**: Run on all commits initially, optimize later if needed

**Optimization Techniques:**
```typescript
// Conditional testing based on branch
const isMainBranch = process.env.GITHUB_REF === 'refs/heads/main';
const isImprovementBranch = process.env.GITHUB_REF?.includes('improvements/');

// Run full suite on main and improvement branches
// Run limited suite on feature branches
const testSuite = isMainBranch || isImprovementBranch ? 'full' : 'limited';
```

---

## 🎨 Phase 2: Color System Consolidation

### **Objective**
Consolidate SCSS color variables into Tailwind configuration for better maintainability and smaller bundle size.

### **Duration**: 5 days
### **Risk Level**: LOW (1:1 color mapping ensures no visual changes)

### **Day 1: Color Audit & Documentation**

#### **Step 1: Extract Current Colors**
Create `color-audit.js` script:

```javascript
const fs = require('fs');
const path = require('path');

// Read SCSS color file
const scssColors = fs.readFileSync('src/styles/colors.scss', 'utf8');

// Extract color variables
const colorMatches = scssColors.match(/\$[\w-]+:\s*#[0-9a-fA-F]{6};/g);
const colors = {};

colorMatches?.forEach(match => {
  const [variable, value] = match.split(':');
  const colorName = variable.replace('$', '').trim();
  const colorValue = value.replace(';', '').trim();
  colors[colorName] = colorValue;
});

console.log('Current SCSS Colors:', JSON.stringify(colors, null, 2));
```

#### **Step 2: Visual Baseline**
Run visual regression tests to establish baseline:

```bash
npx percy exec -- npx playwright test tests/visual-regression.spec.ts
```

### **Day 2-3: Tailwind Configuration Update**

#### **Update `tailwind.config.cjs`**
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  theme: {
    extend: {
      colors: {
        // Primary brand colors (exact SCSS mappings)
        'uown-mint': '#00D4AA',
        'uown-mint-light': '#33DDB8',
        'uown-mint-dark': '#00B894',
        
        // Secondary colors
        'uown-dark': '#1A1A1A',
        'uown-gray': '#6B7280',
        'uown-light-gray': '#F3F4F6',
        
        // Gradient colors
        'gradient-start': '#00D4AA',
        'gradient-end': '#0EA5E9',
        
        // Add all existing SCSS colors with exact hex values
        // This ensures 1:1 visual mapping
      },
      backgroundImage: {
        // Convert SCSS gradients to Tailwind utilities
        'mint-gradient': 'linear-gradient(135deg, #00D4AA 0%, #0EA5E9 100%)',
        'dark-gradient': 'linear-gradient(135deg, #1A1A1A 0%, #374151 100%)',
      }
    },
  },
  plugins: [],
}
```

### **Day 3-4: Component Migration**

#### **Migration Strategy**
1. Replace SCSS color classes with Tailwind utilities
2. Update one component at a time
3. Run visual regression test after each component
4. Verify no visual changes

#### **Example Migration**
```typescript
// Before (SCSS)
<div className="bg-mint-gradient text-uown-dark">

// After (Tailwind)
<div className="bg-mint-gradient text-uown-dark">
```

#### **Automated Migration Script**
Create `migrate-colors.js`:

```javascript
const fs = require('fs');
const glob = require('glob');

const colorMappings = {
  'bg-mint': 'bg-uown-mint',
  'text-dark': 'text-uown-dark',
  // Add all mappings
};

// Find all component files
const files = glob.sync('src/components/**/*.{jsx,tsx,astro}');

files.forEach(file => {
  let content = fs.readFileSync(file, 'utf8');
  
  Object.entries(colorMappings).forEach(([oldClass, newClass]) => {
    content = content.replace(new RegExp(oldClass, 'g'), newClass);
  });
  
  fs.writeFileSync(file, content);
});
```

### **Day 5: Cleanup & Verification**

#### **Remove Unused SCSS**
1. Remove color variables from `src/styles/colors.scss`
2. Update imports in components
3. Run final visual regression tests
4. Verify bundle size reduction

---

## 🔧 Phase 3: Component Consolidation

### **Objective**
Unify button components and migrate to TypeScript for better maintainability.

### **Duration**: 5 days
### **Risk Level**: MEDIUM (requires careful state matching)

### **Day 1: Component Analysis**

#### **Button Component Audit**
Current button implementations:
- `src/components/Button.jsx`
- `src/components/Button.astro`
- `src/components/ButtonA.jsx`

#### **Usage Analysis Script**
```bash
# Find all button usages
grep -r "Button\|btn-" src/ --include="*.jsx" --include="*.tsx" --include="*.astro"
```

### **Day 2-3: Enhanced ButtonA Component**

#### **Create Unified Button Component**
Update `src/components/ButtonA.tsx`:

```typescript
import React from 'react';
import { clsx } from 'clsx';

interface ButtonProps {
  variant?: 'black' | 'mint' | 'white' | 'outline';
  size?: 'small' | 'medium' | 'large';
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
  href?: string;
  type?: 'button' | 'submit' | 'reset';
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'black',
  size = 'medium',
  children,
  className,
  disabled = false,
  onClick,
  href,
  type = 'button',
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variantClasses = {
    black: 'bg-uown-dark text-white hover:bg-gray-800 focus:ring-gray-500',
    mint: 'bg-uown-mint text-white hover:bg-uown-mint-dark focus:ring-uown-mint',
    white: 'bg-white text-uown-dark border border-gray-300 hover:bg-gray-50 focus:ring-gray-500',
    outline: 'border-2 border-uown-mint text-uown-mint hover:bg-uown-mint hover:text-white focus:ring-uown-mint',
  };
  
  const sizeClasses = {
    small: 'px-3 py-1.5 text-sm',
    medium: 'px-4 py-2 text-base',
    large: 'px-6 py-3 text-lg',
  };
  
  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';
  
  const buttonClasses = clsx(
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    disabledClasses,
    className
  );
  
  if (href && !disabled) {
    return (
      <a href={href} className={buttonClasses} {...props}>
        {children}
      </a>
    );
  }
  
  return (
    <button
      type={type}
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
};

export default Button;
```

### **Day 4: TypeScript Migration**

#### **Migration Strategy**
1. Convert `.jsx` files to `.tsx`
2. Add proper TypeScript interfaces
3. Update imports across the codebase
4. Run visual regression tests

#### **Component Interface Examples**
```typescript
// Hero component props
interface HeroProps {
  title: string;
  subtitle?: string;
  backgroundImage?: string;
  children?: React.ReactNode;
}

// Navigation component props
interface NavProps {
  isOpen?: boolean;
  onToggle?: () => void;
  currentPath?: string;
}
```

### **Day 5: Testing & Cleanup**

#### **Visual Regression Verification**
```bash
# Run full visual test suite
npx percy exec -- npx playwright test

# Verify no visual changes
# Check Percy dashboard for comparisons
```

---

## 🔒 Phase 4: Security Improvements

### **Objective**
Improve API security and CORS configuration (excluding .env changes per user request).

### **Duration**: 5 days
### **Risk Level**: MEDIUM (API changes require careful testing)

### **Day 1-2: CORS Security**

#### **Update `astro.config.mjs`**
```javascript
import { defineConfig } from 'astro/config';
import react from '@astrojs/react';
import tailwind from '@astrojs/tailwind';

export default defineConfig({
  integrations: [react(), tailwind()],
  server: {
    headers: {
      // Replace wildcard CORS with specific domains
      'Access-Control-Allow-Origin': process.env.NODE_ENV === 'production' 
        ? 'https://yourdomain.com' 
        : 'http://localhost:4321',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    }
  }
});
```

### **Day 3-4: API Input Sanitization**

#### **Create Validation Utilities**
Create `src/lib/validation.ts`:

```typescript
import { z } from 'zod';

// Email validation schema
export const emailSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
});

// Phone validation schema
export const phoneSchema = z.object({
  phone: z.string().regex(/^\+?[\d\s-()]+$/, 'Invalid phone number'),
  message: z.string().max(500, 'Message too long'),
});

// Sanitization functions
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .trim()
    .slice(0, 1000); // Limit length
};

export const validateAndSanitize = <T>(schema: z.ZodSchema<T>, data: unknown): T => {
  const result = schema.parse(data);
  
  // Sanitize string fields
  if (typeof result === 'object' && result !== null) {
    Object.keys(result).forEach(key => {
      if (typeof (result as any)[key] === 'string') {
        (result as any)[key] = sanitizeInput((result as any)[key]);
      }
    });
  }
  
  return result;
};
```

#### **Update API Routes**
Example for `src/pages/api/emailoctopus.ts`:

```typescript
import type { APIRoute } from 'astro';
import { validateAndSanitize, emailSchema } from '../../lib/validation';

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();
    
    // Validate and sanitize input
    const validatedData = validateAndSanitize(emailSchema, data);
    
    // Rate limiting check (implement as needed)
    // await checkRateLimit(request);
    
    // Process the validated data
    const response = await fetch('https://emailoctopus.com/api/1.6/lists/YOUR_LIST_ID/contacts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        api_key: process.env.EMAIL_OCTOPUS_AUTH,
        email_address: validatedData.email,
        fields: {
          FirstName: validatedData.name,
        },
      }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to subscribe');
    }
    
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
  } catch (error) {
    console.error('API Error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Invalid request',
        message: error instanceof Error ? error.message : 'Unknown error'
      }), 
      {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
```

### **Day 5: Testing & Documentation**

#### **API Testing**
Create `tests/api-security.spec.ts`:

```typescript
import { test, expect } from '@playwright/test';

test.describe('API Security Tests', () => {
  test('Email API - Input Validation', async ({ request }) => {
    // Test invalid email
    const invalidResponse = await request.post('/api/emailoctopus', {
      data: {
        email: 'invalid-email',
        name: 'Test User'
      }
    });
    
    expect(invalidResponse.status()).toBe(400);
    
    // Test valid email
    const validResponse = await request.post('/api/emailoctopus', {
      data: {
        email: '<EMAIL>',
        name: 'Test User'
      }
    });
    
    expect(validResponse.status()).toBe(200);
  });
  
  test('CORS Headers', async ({ request }) => {
    const response = await request.get('/');
    const headers = response.headers();
    
    expect(headers['access-control-allow-origin']).toBeDefined();
    expect(headers['access-control-allow-origin']).not.toBe('*');
  });
});
```

---

## 📊 Success Metrics & Monitoring

### **Visual Regression Metrics**
- ✅ Zero visual regressions during improvements
- ✅ 100% visual coverage of critical user journeys
- ✅ Cross-browser consistency maintained
- ✅ Mobile-first responsive design verified

### **Performance Metrics**
- ✅ CSS bundle size reduction (color system consolidation)
- ✅ Component reusability improvement
- ✅ TypeScript coverage increase
- ✅ Build time optimization

### **Security Metrics**
- ✅ API input validation coverage
- ✅ CORS policy tightening
- ✅ Error handling improvement
- ✅ Security vulnerability reduction

### **Code Quality Metrics**
- ✅ Component consolidation (3 button components → 1)
- ✅ TypeScript migration progress
- ✅ SCSS to Tailwind conversion
- ✅ Dependency updates

---

## 🚨 Risk Mitigation & Rollback Procedures

### **Visual Regression Detection**
1. **Automated Detection**: Percy will flag any visual changes
2. **Manual Review**: Review all flagged changes before approval
3. **Rollback Trigger**: Any unintended visual change triggers immediate rollback

### **Rollback Procedures**
```bash
# Immediate rollback if issues detected
git checkout feature-stake-page
git branch -D improvements/problematic-branch

# Restore previous state
git reset --hard HEAD~1
```

### **Testing Checkpoints**
- ✅ Visual regression test after each component change
- ✅ Cross-browser verification before merge
- ✅ Mobile responsiveness check
- ✅ API functionality verification

---

## 🔧 Troubleshooting Guide

### **Common Percy Issues**

#### **Screenshot Limit Exceeded**
```bash
# Check current usage
npx percy --version
npx percy builds:list

# Optimize test runs
# Run only critical tests on feature branches
```

#### **Visual Differences False Positives**
```typescript
// Add Percy CSS to stabilize dynamic elements
await percySnapshot(page, 'Page Name', {
  percyCSS: `
    .loading, .spinner, [data-loading="true"] {
      visibility: hidden !important;
    }
    [data-testid*="time"], .timestamp {
      visibility: hidden !important;
    }
  `
});
```

### **Common Playwright Issues**

#### **Timeout Issues**
```typescript
// Increase timeout for slow pages
await page.goto(url, { 
  waitUntil: 'networkidle',
  timeout: 30000 
});
```

#### **Element Not Found**
```typescript
// Wait for elements to be available
await page.waitForSelector('[data-testid="main-content"]');
await page.waitForLoadState('networkidle');
```

### **Build Issues**

#### **Astro Build Failures**
```bash
# Clear cache and rebuild
rm -rf node_modules/.astro
rm -rf dist
npm run build
```

#### **TypeScript Errors**
```bash
# Check TypeScript configuration
npx tsc --noEmit
```

---

## 📚 Additional Resources

### **Documentation Links**
- [Playwright Documentation](https://playwright.dev/docs/intro)
- [Percy Documentation](https://docs.percy.io/docs/playwright)
- [Astro Documentation](https://docs.astro.build/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

### **Useful Commands**
```bash
# Run visual tests locally
npx percy exec -- npx playwright test tests/visual-regression.spec.ts

# Run specific browser tests
npx playwright test --project="Mobile Safari"

# Generate Playwright test report
npx playwright show-report

# Check Percy build status
npx percy builds:list

# Update Playwright browsers
npx playwright install --with-deps
```

### **Environment Setup**
```bash
# Required environment variables
export PERCY_TOKEN="your-percy-token"
export NODE_ENV="development"

# Optional for enhanced features
export PLAYWRIGHT_BROWSERS_PATH="./browsers"
```

---

## 🎯 Next Steps

1. **Create Visual Regression Branch**: `git checkout -b improvements/visual-regression-setup`
2. **Install Dependencies**: Follow Phase 1 installation steps
3. **Configure Percy**: Set up Percy project and token
4. **Run Initial Tests**: Establish visual baseline
5. **Proceed with Improvements**: Follow phase-by-phase implementation

---

*This implementation guide serves as the single source of truth for the UOWN improvement process. Update this document as the project evolves and new requirements emerge.*

**Last Updated**: December 18, 2025
**Version**: 1.0
**Status**: Ready for Implementation

import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'topic',
  title: 'Topic',
  type: 'document',
  fields: [
    defineField({
      name: 'full_slug',
      title: 'FullSlug',
      type: 'string',
    }),
    defineField({
      name: 'name',
      title: 'Name',
      type: 'string',
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
    }),
    defineField({
      name: 'icon',
      title: 'Icon',
      type: 'image',
    }),
    defineField({
        name: 'order',
        title: 'Order',
        type: 'number',
      }),
    defineField({
        name: 'seo_description',
        title: 'Seo Description',
        type: 'string',
      }),
  ],
})

import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'post',
  title: 'Hub Post',
  type: 'document',
  fields: [
    define<PERSON><PERSON>({
      name: 'title',
      title: 'Title',
      type: 'string',
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
    }),
    defineField({
      name: 'author',
      title: 'Author',
      type: 'reference',
      to: {type: 'author'},
    }),
    defineField({
      name: 'thumbnail',
      title: 'Thumbnail',
      type: 'image',
      options: {
        hotspot: true,
      },
    }),
    defineField({
      name: 'category',
      title: 'Category',
      type: 'reference', 
      to: {type: 'category'},
    }),
    defineField({
      name: 'tags',
      title: 'Tags',
      type: 'array',
      of: [{type: 'reference', to: {type: 'tags'}}],
    }),
    defineField({
      name: 'fullSlug',
      title: 'Full Slug',
      type: 'string',
    }),
    defineField({
      name: 'firstPublishedAt',
      title: 'First Published at',
      type: 'datetime',
    }),
    define<PERSON>ield({
      name: 'bodyText',
      title: 'Body',
      type: 'blockContent',
    }),
    defineField({
      name: 'summary',
      title: 'Summary',
      type: 'string',
    })
  ],

  preview: {
    select: {
      title: 'title',
      author: 'author.name',
      media: 'mainImage',
    },
    prepare(selection) {
      const {author} = selection
      return {...selection, subtitle: author && `by ${author}`}
    },
  },
})

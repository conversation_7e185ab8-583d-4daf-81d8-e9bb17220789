import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'article',
  title: 'Help Centre Article',
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Title',
      type: 'string',
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
    }),
    defineField({
      name: 'topic',
      title: 'Topic',
      type: 'reference',
      to: {type: 'topic'},
    }),
    defineField({
      name: 'topics',
      title: 'Topics',
      type: 'array',
      of: [{type: 'reference', to: {type: 'topic'}}],
    }),
    defineField({
      name: 'fullSlug',
      title: 'Full Slug',
      type: 'string',
    }),
    defineField({
      name: 'firstPublishedAt',
      title: 'First Published at',
      type: 'datetime',
    }),
    defineField({
      name: 'contentHtml',
      title: 'HTML Content',
      type: 'blockContent',
    }),
    defineField({
      name: 'seoDescription',
      title: 'SEO Description',
      type: 'string',
    })
  ],
})

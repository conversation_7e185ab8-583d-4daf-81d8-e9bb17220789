{"name": "uown", "private": true, "version": "1.0.0", "main": "package.json", "license": "UNLICENSED", "scripts": {"dev": "sanity dev", "start": "sanity start", "build": "sanity build", "deploy": "sanity deploy", "deploy-graphql": "sanity graphql deploy"}, "keywords": ["sanity"], "dependencies": {"@sanity/vision": "^3.18.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-is": "^18.2.0", "sanity": "^3.18.1", "styled-components": "^5.3.9"}, "devDependencies": {"@sanity/eslint-config-studio": "^3.0.1", "@types/react": "^18.0.25", "@types/styled-components": "^5.1.26", "eslint": "^8.6.0", "prettier": "^3.0.2", "typescript": "^5.1.6"}, "prettier": {"semi": false, "printWidth": 100, "bracketSpacing": false, "singleQuote": true}}